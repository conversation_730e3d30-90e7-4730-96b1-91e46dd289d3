let NEXT_PUBLIC_DR_CODE_BASE_API_URL: string;
let NEXT_PUBLIC_DR_CODE_AUTH_BASE_API_URL: string;
let NEXT_PUBLIC_DR_CODE_FRONTEND_URL: string
let NEXT_PUBLIC_DR_CODE_SOCKET_URL: string;

if (process.env.NEXT_ENV === 'production') {
    NEXT_PUBLIC_DR_CODE_BASE_API_URL = "https://api.drcode.ai/testgpt/api";
    NEXT_PUBLIC_DR_CODE_AUTH_BASE_API_URL = "https://api.drcode.ai/operations?";
    NEXT_PUBLIC_DR_CODE_FRONTEND_URL = "https://drcode.ai"
    NEXT_PUBLIC_DR_CODE_SOCKET_URL = "wss://devapi.drcode.ai";
} else if (process.env.NEXT_ENV === 'development') {
    NEXT_PUBLIC_DR_CODE_BASE_API_URL = "https://devapi.drcode.ai/testgpt/api";
    NEXT_PUBLIC_DR_CODE_AUTH_BASE_API_URL = "https://devapi.drcode.ai/operations?";
    NEXT_PUBLIC_DR_CODE_FRONTEND_URL = "https://dev.drcode.ai"
    NEXT_PUBLIC_DR_CODE_SOCKET_URL = "wss://devapi.drcode.ai";
} else {
    // NEXT_PUBLIC_DR_CODE_BASE_API_URL = "http://localhost:3005/api";
    NEXT_PUBLIC_DR_CODE_BASE_API_URL = "https://devapi.drcode.ai/testgpt/api";
    NEXT_PUBLIC_DR_CODE_AUTH_BASE_API_URL = "https://devapi.drcode.ai/operations?";
    NEXT_PUBLIC_DR_CODE_FRONTEND_URL = "https://dev.drcode.ai";
    // NEXT_PUBLIC_DR_CODE_SOCKET_URL = "http://localhost:3005";
    NEXT_PUBLIC_DR_CODE_SOCKET_URL = "wss://devapi.drcode.ai";

    
}

export { NEXT_PUBLIC_DR_CODE_BASE_API_URL, NEXT_PUBLIC_DR_CODE_AUTH_BASE_API_URL,NEXT_PUBLIC_DR_CODE_FRONTEND_URL, NEXT_PUBLIC_DR_CODE_SOCKET_URL };