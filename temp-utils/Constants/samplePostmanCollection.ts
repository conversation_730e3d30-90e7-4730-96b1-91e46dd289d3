export const samplePostmanCollection = {
    info: {
        _postman_id: "89268baa-de6a-4cbc-b019-80f931cc4684",
        name: "Advanced REST API Collection",
        description:
            "A collection of all API endpoints for the advanced REST API, organized by controllers with examples.",
        schema:
            "https://schema.getpostman.com/json/collection/v2.1.0/collection.json",
        _exporter_id: "37395619",
    },
    item: [
        {
            name: "PublicController",
            item: [
                {
                    name: "GET /",
                    request: {
                        method: "GET",
                        header: [],
                        url: {
                            raw: "{{base_url}}/",
                            host: ["{{base_url}}"],
                            path: [""],
                        },
                    },
                    response: [
                        {
                            name: "GET / Response",
                            originalRequest: {
                                method: "GET",
                                header: [],
                                url: {
                                    raw: "{{base_url}}/",
                                    host: ["{{base_url}}"],
                                    path: [""],
                                },
                            },
                            status: "OK",
                            code: 200,
                            _postman_previewlanguage: "Text",
                            header: [
                                {
                                    key: "Content-Type",
                                    value: "text/html; charset=utf-8",
                                },
                                {
                                    key: "Content-Length",
                                    value: "36",
                                },
                            ],
                            cookie: [],
                            body: "Welcome to the advanced REST API!",
                        },
                    ],
                },
                {
                    name: "GET /public-data",
                    request: {
                        method: "GET",
                        header: [],
                        url: {
                            raw: "{{base_url}}/public-data",
                            host: ["{{base_url}}"],
                            path: ["public-data"],
                        },
                    },
                    response: [
                        {
                            name: "GET /public-data Response",
                            originalRequest: {
                                method: "GET",
                                header: [],
                                url: {
                                    raw: "{{base_url}}/public-data",
                                    host: ["{{base_url}}"],
                                    path: ["public-data"],
                                },
                            },
                            status: "OK",
                            code: 200,
                            _postman_previewlanguage: "Text",
                            header: [
                                {
                                    key: "Content-Type",
                                    value: "application/json; charset=utf-8",
                                },
                                {
                                    key: "Content-Length",
                                    value: "81",
                                },
                            ],
                            cookie: [],
                            body: '{\n  "data": "This is public data accessible without authentication."\n}',
                        },
                    ],
                },
            ],
        },
        {
            name: "AuthenticationController",
            item: [
                {
                    name: "POST /register",
                    request: {
                        method: "POST",
                        header: [
                            {
                                key: "Content-Type",
                                value: "application/json",
                                type: "text",
                            },
                        ],
                        body: {
                            mode: "raw",
                            raw: '{\n  "username": "testuser",\n  "password": "testpass"\n}',
                        },
                        url: {
                            raw: "{{base_url}}/register",
                            host: ["{{base_url}}"],
                            path: ["register"],
                        },
                    },
                    response: [
                        {
                            name: "POST /register Response",
                            originalRequest: {
                                method: "POST",
                                header: [
                                    {
                                        key: "Content-Type",
                                        value: "application/json",
                                        type: "text",
                                    },
                                ],
                                body: {
                                    mode: "raw",
                                    raw: '{\n  "username": "testuser",\n  "password": "testpass"\n}',
                                },
                                url: {
                                    raw: "{{base_url}}/register",
                                    host: ["{{base_url}}"],
                                    path: ["register"],
                                },
                            },
                            status: "OK",
                            code: 200,
                            _postman_previewlanguage: "Text",
                            header: [
                                {
                                    key: "Content-Type",
                                    value: "application/json; charset=utf-8",
                                },
                                {
                                    key: "Content-Length",
                                    value: "40",
                                },
                            ],
                            cookie: [],
                            body: '{\n  "message": "User registered successfully."\n}',
                        },
                    ],
                },
                {
                    name: "POST /login",
                    request: {
                        method: "POST",
                        header: [
                            {
                                key: "Content-Type",
                                value: "application/json",
                                type: "text",
                            },
                        ],
                        body: {
                            mode: "raw",
                            raw: '{\n  "username": "testuser",\n  "password": "testpass"\n}',
                        },
                        url: {
                            raw: "{{base_url}}/login",
                            host: ["{{base_url}}"],
                            path: ["login"],
                        },
                    },
                    response: [
                        {
                            name: "POST /login Response",
                            originalRequest: {
                                method: "POST",
                                header: [
                                    {
                                        key: "Content-Type",
                                        value: "application/json",
                                        type: "text",
                                    },
                                ],
                                body: {
                                    mode: "raw",
                                    raw: '{\n  "username": "testuser",\n  "password": "testpass"\n}',
                                },
                                url: {
                                    raw: "{{base_url}}/login",
                                    host: ["{{base_url}}"],
                                    path: ["login"],
                                },
                            },
                            status: "OK",
                            code: 200,
                            _postman_previewlanguage: "Text",
                            header: [
                                {
                                    key: "Content-Type",
                                    value: "application/json; charset=utf-8",
                                },
                                {
                                    key: "Content-Length",
                                    value: "160",
                                },
                            ],
                            cookie: [],
                            body: '{\n  "token": "your_jwt_token"\n}',
                        },
                    ],
                },
            ],
        },
        {
            name: "AdvancedDataController",
            item: [
                {
                    name: "GET /protected",
                    request: {
                        method: "GET",
                        header: [
                            {
                                key: "Authorization",
                                value: "Bearer {{token}}",
                                type: "text",
                            },
                        ],
                        url: {
                            raw: "{{base_url}}/protected",
                            host: ["{{base_url}}"],
                            path: ["protected"],
                        },
                    },
                    response: [
                        {
                            name: "GET /protected Response",
                            originalRequest: {
                                method: "GET",
                                header: [
                                    {
                                        key: "Authorization",
                                        value: "Bearer {{token}}",
                                        type: "text",
                                    },
                                ],
                                url: {
                                    raw: "{{base_url}}/protected",
                                    host: ["{{base_url}}"],
                                    path: ["protected"],
                                },
                            },
                            status: "OK",
                            code: 200,
                            _postman_previewlanguage: "Text",
                            header: [
                                {
                                    key: "Content-Type",
                                    value: "application/json; charset=utf-8",
                                },
                                {
                                    key: "Content-Length",
                                    value: "60",
                                },
                            ],
                            cookie: [],
                            body: '{\n  "message": "Hello, testuser! This is protected data."\n}',
                        },
                    ],
                },
                {
                    name: "POST /advanced-data",
                    request: {
                        method: "POST",
                        header: [
                            {
                                key: "Authorization",
                                value: "Bearer {{token}}",
                                type: "text",
                            },
                            {
                                key: "Content-Type",
                                value: "multipart/form-data",
                                type: "text",
                            },
                        ],
                        body: {
                            mode: "formdata",
                            formdata: [
                                {
                                    key: "text",
                                    value: "Hello World",
                                    type: "text",
                                },
                                {
                                    key: "number",
                                    value: "123",
                                    type: "text",
                                },
                                {
                                    key: "boolean",
                                    value: "false",
                                    type: "text",
                                },
                                {
                                    key: "array",
                                    value: '["apple","banana","cherry"]',
                                    type: "text",
                                },
                                {
                                    key: "object",
                                    value: '{"firstName":"John","lastName":"Doe"}',
                                    type: "text",
                                },
                            ],
                        },
                        url: {
                            raw: "{{base_url}}/advanced-data",
                            host: ["{{base_url}}"],
                            path: ["advanced-data"],
                        },
                    },
                    response: [
                        {
                            name: "POST /advanced-data Response",
                            originalRequest: {
                                method: "POST",
                                header: [
                                    {
                                        key: "Authorization",
                                        value: "Bearer {{token}}",
                                        type: "text",
                                    },
                                    {
                                        key: "Content-Type",
                                        value: "multipart/form-data",
                                        type: "text",
                                    },
                                ],
                                body: {
                                    mode: "formdata",
                                    formdata: [
                                        {
                                            key: "text",
                                            value: "Hello World",
                                            type: "text",
                                        },
                                        {
                                            key: "number",
                                            value: "123",
                                            type: "text",
                                        },
                                        {
                                            key: "boolean",
                                            value: "false",
                                            type: "text",
                                        },
                                        {
                                            key: "array",
                                            value: '["apple","banana","cherry"]',
                                            type: "text",
                                        },
                                        {
                                            key: "object",
                                            value: '{"firstName":"John","lastName":"Doe"}',
                                            type: "text",
                                        },
                                    ],
                                },
                                url: {
                                    raw: "{{base_url}}/advanced-data",
                                    host: ["{{base_url}}"],
                                    path: ["advanced-data"],
                                },
                            },
                            status: "OK",
                            code: 200,
                            _postman_previewlanguage: "Text",
                            header: [
                                {
                                    key: "Content-Type",
                                    value: "application/json; charset=utf-8",
                                },
                                {
                                    key: "Content-Length",
                                    value: "276",
                                },
                            ],
                            cookie: [],
                            body: '{\n  "message": "Data received successfully.",\n  "data": {\n    "id": 1,\n    "text": "Hello World",\n    "number": 123,\n    "boolean": false,\n    "array": ["apple", "banana", "cherry"],\n    "object": {"firstName": "John", "lastName": "Doe"},\n    "imagePath": "uploads/1634567890123-image.png",\n    "username": "testuser"\n  }\n}',
                        },
                    ],
                },
                {
                    name: "GET /advanced-data/:id",
                    request: {
                        method: "GET",
                        header: [
                            {
                                key: "Authorization",
                                value: "Bearer {{token}}",
                                type: "text",
                            },
                        ],
                        url: {
                            raw: "{{base_url}}/advanced-data/1",
                            host: ["{{base_url}}"],
                            path: ["advanced-data", "1"],
                        },
                    },
                    response: [
                        {
                            name: "GET /advanced-data/:id Response",
                            originalRequest: {
                                method: "GET",
                                header: [
                                    {
                                        key: "Authorization",
                                        value: "Bearer {{token}}",
                                        type: "text",
                                    },
                                ],
                                url: {
                                    raw: "{{base_url}}/advanced-data/1",
                                    host: ["{{base_url}}"],
                                    path: ["advanced-data", "1"],
                                },
                            },
                            status: "OK",
                            code: 200,
                            _postman_previewlanguage: "Text",
                            header: [
                                {
                                    key: "Content-Type",
                                    value: "application/json; charset=utf-8",
                                },
                                {
                                    key: "Content-Length",
                                    value: "260",
                                },
                            ],
                            cookie: [],
                            body: '{\n  "data": {\n    "id": 1,\n    "text": "Hello World",\n    "number": 123,\n    "boolean": false,\n    "array": ["apple", "banana", "cherry"],\n    "object": {"firstName": "John", "lastName": "Doe"},\n    "imagePath": "uploads/1634567890123-image.png",\n    "username": "testuser"\n  }\n}',
                        },
                    ],
                },
                {
                    name: "PUT /advanced-data/:id",
                    request: {
                        method: "PUT",
                        header: [
                            {
                                key: "Authorization",
                                value: "Bearer {{token}}",
                                type: "text",
                            },
                            {
                                key: "Content-Type",
                                value: "application/json",
                                type: "text",
                            },
                        ],
                        body: {
                            mode: "raw",
                            raw: '{\n  "text": "Updated text",\n  "number": 99,\n  "boolean": "true",\n  "array": "[4,5,6]",\n  "object": "{\\"newKey\\":\\"newValue\\"}"\n}',
                        },
                        url: {
                            raw: "{{base_url}}/advanced-data/1",
                            host: ["{{base_url}}"],
                            path: ["advanced-data", "1"],
                        },
                    },
                    response: [
                        {
                            name: "PUT /advanced-data/:id Response",
                            originalRequest: {
                                method: "PUT",
                                header: [
                                    {
                                        key: "Authorization",
                                        value: "Bearer {{token}}",
                                        type: "text",
                                    },
                                    {
                                        key: "Content-Type",
                                        value: "application/json",
                                        type: "text",
                                    },
                                ],
                                body: {
                                    mode: "raw",
                                    raw: '{\n  "text": "Updated text",\n  "number": 99,\n  "boolean": "true",\n  "array": "[4,5,6]",\n  "object": "{\\"newKey\\":\\"newValue\\"}"\n}',
                                },
                                url: {
                                    raw: "{{base_url}}/advanced-data/1",
                                    host: ["{{base_url}}"],
                                    path: ["advanced-data", "1"],
                                },
                            },
                            status: "OK",
                            code: 200,
                            _postman_previewlanguage: "Text",
                            header: [
                                {
                                    key: "Content-Type",
                                    value: "application/json; charset=utf-8",
                                },
                                {
                                    key: "Content-Length",
                                    value: "263",
                                },
                            ],
                            cookie: [],
                            body: '{\n  "message": "Data updated successfully.",\n  "data": {\n    "id": 1,\n    "text": "Updated text",\n    "number": 99,\n    "boolean": true,\n    "array": [4, 5, 6],\n    "object": {"newKey": "newValue"},\n    "imagePath": "uploads/1634567890123-image.png",\n    "username": "testuser"\n  }\n}',
                        },
                    ],
                },
                {
                    name: "PATCH /advanced-data/:id",
                    request: {
                        method: "PATCH",
                        header: [
                            {
                                key: "Authorization",
                                value: "Bearer {{token}}",
                                type: "text",
                            },
                            {
                                key: "Content-Type",
                                value: "application/json",
                                type: "text",
                            },
                        ],
                        body: {
                            mode: "raw",
                            raw: '{\n  "text": "Partially updated text",\n  "boolean": "false"\n}',
                        },
                        url: {
                            raw: "{{base_url}}/advanced-data/1",
                            host: ["{{base_url}}"],
                            path: ["advanced-data", "1"],
                        },
                    },
                    response: [
                        {
                            name: "PATCH /advanced-data/:id Response",
                            originalRequest: {
                                method: "PATCH",
                                header: [
                                    {
                                        key: "Authorization",
                                        value: "Bearer {{token}}",
                                        type: "text",
                                    },
                                    {
                                        key: "Content-Type",
                                        value: "application/json",
                                        type: "text",
                                    },
                                ],
                                body: {
                                    mode: "raw",
                                    raw: '{\n  "text": "Partially updated text",\n  "boolean": "false"\n}',
                                },
                                url: {
                                    raw: "{{base_url}}/advanced-data/1",
                                    host: ["{{base_url}}"],
                                    path: ["advanced-data", "1"],
                                },
                            },
                            status: "OK",
                            code: 200,
                            _postman_previewlanguage: "Text",
                            header: [
                                {
                                    key: "Content-Type",
                                    value: "application/json; charset=utf-8",
                                },
                                {
                                    key: "Content-Length",
                                    value: "263",
                                },
                            ],
                            cookie: [],
                            body: '{\n  "message": "Data patched successfully.",\n  "data": {\n    "id": 1,\n    "text": "Partially updated text",\n    "number": 99,\n    "boolean": false,\n    "array": [4, 5, 6],\n    "object": {"newKey": "newValue"},\n    "imagePath": "uploads/1634567890123-image.png",\n    "username": "testuser"\n  }\n}',
                        },
                    ],
                },
                {
                    name: "DELETE /advanced-data/:id",
                    request: {
                        method: "DELETE",
                        header: [
                            {
                                key: "Authorization",
                                value: "Bearer {{token}}",
                                type: "text",
                            },
                        ],
                        url: {
                            raw: "{{base_url}}/advanced-data/1",
                            host: ["{{base_url}}"],
                            path: ["advanced-data", "1"],
                        },
                    },
                    response: [
                        {
                            name: "DELETE /advanced-data/:id Response",
                            originalRequest: {
                                method: "DELETE",
                                header: [
                                    {
                                        key: "Authorization",
                                        value: "Bearer {{token}}",
                                        type: "text",
                                    },
                                ],
                                url: {
                                    raw: "{{base_url}}/advanced-data/1",
                                    host: ["{{base_url}}"],
                                    path: ["advanced-data", "1"],
                                },
                            },
                            status: "OK",
                            code: 200,
                            _postman_previewlanguage: "Text",
                            header: [
                                {
                                    key: "Content-Type",
                                    value: "application/json; charset=utf-8",
                                },
                                {
                                    key: "Content-Length",
                                    value: "35",
                                },
                            ],
                            cookie: [],
                            body: '{\n  "message": "Data deleted successfully."\n}',
                        },
                    ],
                },
            ],
        },
        {
            name: "ObjectsController",
            item: [
                {
                    name: "GET /objects",
                    request: {
                        method: "GET",
                        header: [
                            {
                                key: "accept",
                                value:
                                    "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
                                type: "text",
                            },
                            {
                                key: "accept-language",
                                value: "en-US,en;q=0.9",
                                type: "text",
                            },
                            {
                                key: "cache-control",
                                value: "max-age=0",
                                type: "text",
                            },
                            {
                                key: "priority",
                                value: "u=0, i",
                                type: "text",
                            },
                            {
                                key: "sec-ch-ua",
                                value:
                                    '"Chromium";v="130", "Brave";v="130", "Not?A_Brand";v="99"',
                                type: "text",
                            },
                            {
                                key: "sec-ch-ua-mobile",
                                value: "?0",
                                type: "text",
                            },
                            {
                                key: "sec-ch-ua-platform",
                                value: '"Windows"',
                                type: "text",
                            },
                            {
                                key: "sec-fetch-dest",
                                value: "document",
                                type: "text",
                            },
                            {
                                key: "sec-fetch-mode",
                                value: "navigate",
                                type: "text",
                            },
                            {
                                key: "sec-fetch-site",
                                value: "none",
                                type: "text",
                            },
                            {
                                key: "sec-fetch-user",
                                value: "?1",
                                type: "text",
                            },
                            {
                                key: "sec-gpc",
                                value: "1",
                                type: "text",
                            },
                            {
                                key: "upgrade-insecure-requests",
                                value: "1",
                                type: "text",
                            },
                            {
                                key: "user-agent",
                                value:
                                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
                                type: "text",
                            },
                        ],
                        url: {
                            raw: "https://api.restful-api.dev/objects",
                            protocol: "https",
                            host: ["api", "restful-api", "dev"],
                            path: ["objects"],
                        },
                    },
                    response: [
                        {
                            name: "GET /objects Response",
                            originalRequest: {
                                method: "GET",
                                header: [
                                    {
                                        key: "accept",
                                        value:
                                            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
                                        type: "text",
                                    },
                                    {
                                        key: "accept-language",
                                        value: "en-US,en;q=0.9",
                                        type: "text",
                                    },
                                    {
                                        key: "cache-control",
                                        value: "max-age=0",
                                        type: "text",
                                    },
                                    {
                                        key: "priority",
                                        value: "u=0, i",
                                        type: "text",
                                    },
                                    {
                                        key: "sec-ch-ua",
                                        value:
                                            '"Chromium";v="130", "Brave";v="130", "Not?A_Brand";v="99"',
                                        type: "text",
                                    },
                                    {
                                        key: "sec-ch-ua-mobile",
                                        value: "?0",
                                        type: "text",
                                    },
                                    {
                                        key: "sec-ch-ua-platform",
                                        value: '"Windows"',
                                        type: "text",
                                    },
                                    {
                                        key: "sec-fetch-dest",
                                        value: "document",
                                        type: "text",
                                    },
                                    {
                                        key: "sec-fetch-mode",
                                        value: "navigate",
                                        type: "text",
                                    },
                                    {
                                        key: "sec-fetch-site",
                                        value: "none",
                                        type: "text",
                                    },
                                    {
                                        key: "sec-fetch-user",
                                        value: "?1",
                                        type: "text",
                                    },
                                    {
                                        key: "sec-gpc",
                                        value: "1",
                                        type: "text",
                                    },
                                    {
                                        key: "upgrade-insecure-requests",
                                        value: "1",
                                        type: "text",
                                    },
                                    {
                                        key: "user-agent",
                                        value:
                                            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
                                        type: "text",
                                    },
                                ],
                                url: {
                                    raw: "https://api.restful-api.dev/objects",
                                    protocol: "https",
                                    host: ["api", "restful-api", "dev"],
                                    path: ["objects"],
                                },
                            },
                            status: "OK",
                            code: 200,
                            _postman_previewlanguage: "JSON",
                            header: [
                                {
                                    key: "Content-Type",
                                    value: "application/json",
                                    type: "text",
                                },
                            ],
                            cookie: [],
                            body: '[{"id":"1","name":"Google Pixel 6 Pro","data":{"color":"Cloudy White","capacity":"128 GB"}},{"id":"2","name":"Apple iPhone 12 Mini, 256GB, Blue","data":null},{"id":"3","name":"Apple iPhone 12 Pro Max","data":{"color":"Cloudy White","capacity GB":512}},{"id":"4","name":"Apple iPhone 11, 64GB","data":{"price":389.99,"color":"Purple"}},{"id":"5","name":"Samsung Galaxy Z Fold2","data":{"price":689.99,"color":"Brown"}},{"id":"6","name":"Apple AirPods","data":{"generation":"3rd","price":120}},{"id":"7","name":"Apple MacBook Pro 16","data":{"year":2019,"price":1849.99,"CPU model":"Intel Core i9","Hard disk size":"1 TB"}},{"id":"8","name":"Apple Watch Series 8","data":{"Strap Colour":"Elderberry","Case Size":"41mm"}},{"id":"9","name":"Beats Studio3 Wireless","data":{"Color":"Red","Description":"High-performance wireless noise cancelling headphones"}},{"id":"10","name":"Apple iPad Mini 5th Gen","data":{"Capacity":"64 GB","Screen size":7.9}},{"id":"11","name":"Apple iPad Mini 5th Gen","data":{"Capacity":"254 GB","Screen size":7.9}},{"id":"12","name":"Apple iPad Air","data":{"Generation":"4th","Price":"419.99","Capacity":"64 GB"}},{"id":"13","name":"Apple iPad Air","data":{"Generation":"4th","Price":"519.99","Capacity":"256 GB"}}]',
                        },
                    ],
                },
            ],
        },
    ],
    variable: [
        {
            key: "base_url",
            value: "https://advanced-rest-api.vercel.app",
        },
        {
            key: "token",
            value:
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InRlc3R1c2VyIiwiaWF0IjoxNzMzMzA4NTA2fQ.KQ_7kGBDop24BK1KDkxVDnR7mnED24DgSrMLrzHvpTc",
        },
    ],
}

export const MAX_PAYLOAD_SIZE = 1048576 // 1MB in bytes
