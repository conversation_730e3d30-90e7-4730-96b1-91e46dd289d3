import axios from "axios";
import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import { E2EStep } from "@/app/project/[projectId]/group/[groupId]/testSuit/e2e/[e2eId]/page";

export const handleSaveE2ERequest = async (
  testSuite: E2EStep,
  status?: string
) => {
  try {
    const response = await axios.put(
      `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.UPDATE_E2E_BY_E2E_STEP_ID}/${testSuite.id}`,
      {
        request: JSON.parse(testSuite.request as string),
        url: testSuite.url,
      }
    );

    return response.data;
  } catch (error) {
    console.error("Error updating e2e step:", error);
    return null;
  }
};

