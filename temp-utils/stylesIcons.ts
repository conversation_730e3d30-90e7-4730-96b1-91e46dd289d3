import { styled } from "@mui/material";

export const CustomIcon = styled("span")({
  borderRadius: 3,
  width: 18,
  height: 18,
  border: "2px solid #606060",
  backgroundColor: "transparent",
});

export const CustomCheckedIcon = styled("span")({
  borderRadius: 3,
  width: 18,
  height: 18,
  border: "2px solid #606060",
  backgroundColor: "#2E2E60",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  "& svg": {
    width: 12,
    height: 12,
  },
});

export const CustomIndeterminateIcon = styled("span")({
  borderRadius: 3,
  width: 18,
  height: 18,
  border: "2px solid #606060",
  backgroundColor: "#2E2E60",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  "&::before": {
    content: '""',
    width: 12,
    height: 2,
    backgroundColor: "#ffffff",
  },
});
