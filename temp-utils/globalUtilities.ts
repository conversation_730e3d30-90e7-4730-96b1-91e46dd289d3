export const setDataOnLocalStorage = (key: string, data: any) => {
  if (typeof window !== "undefined") {
    try {
      const valueToStore =
        typeof data === "string" ? data : JSON.stringify(data);
      localStorage.setItem(key, valueToStore);
    } catch (error) {
      console.error(
        `Error storing data to local storage for key ${key}:`,
        error
      );
    }
  }
};

export const getDataFromLocalStorage = <T>(key: string): T | string | null => {
  if (typeof window === "undefined") {
    return null; // Or handle it as per your requirement
  }

  const value = localStorage.getItem(key);
  if (value === null) {
    return null;
  }

  try {
    return JSON.parse(value) as T;
  } catch (error) {
    return value;
  }
};

export const removeDataFromLocalStorage = (key: string) => {
  if (typeof window !== "undefined") {
    localStorage.removeItem(key);
  }
};

export const isMobile = (): boolean => {
  if (typeof window !== "undefined") {
    return window.screen.width <= 992;
  }
  return false;
};

/**
 * @param object - Object of objects
 * @returns - Array of objects
 */
export const getArrayOfObjects = (object: any): Array<any> => {
  if (object) {
    return Object.keys(object).map((key) => {
      return object[key];
    });
  } else {
    return [];
  }
};
