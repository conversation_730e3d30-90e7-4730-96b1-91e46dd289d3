import { TestCase } from "@/components/test-runner/tests-table/TestsTable";

const getAssertions = (testCase: TestCase): any[] => {
  // Retrieve assertions from the testCase object using either key, or default to an empty array
  let assertions: any = testCase?.assertions_results || [];

  // If assertions is a string, attempt to parse it as JSO<PERSON>
  if (typeof assertions === "string") {
    // Remove wrapping quotes if present
    if (assertions.startsWith('"') && assertions.endsWith('"')) {
      assertions = assertions.slice(1, -1);
    }
    // If the string is wrapped in curly braces (which is invalid for an array), convert them to square brackets.
    if (assertions.startsWith("{") && assertions.endsWith("}")) {
      assertions = "[" + assertions.slice(1, -1) + "]";
    }
    try {
      assertions = JSON.parse(assertions);
    } catch (error) {
      console.error("Error parsing assertions JSON:", error);
      return assertions;
    }
  }

  // Ensure that assertions is an array. If not, wrap it in one.
  if (!Array.isArray(assertions)) {
    assertions = [assertions];
  }

  // If elements are strings, attempt to parse each one as JSON.
  assertions = assertions.map((assertion: any) => {
    if (typeof assertion === "string") {
      // Remove wrapping quotes if present
      if (assertion.startsWith('"') && assertion.endsWith('"')) {
        assertion = assertion.slice(1, -1);
      }
      try {
        return JSON.parse(assertion);
      } catch (error) {
        console.error("Error parsing assertion string to JSON:", error);
        // Return the original string if parsing fails
        return assertion;
      }
    }
    return assertion;
  });



  return assertions;
};

export { getAssertions };
