import { v4 as uuidv4 } from "uuid";
import { SignJWT } from "jose";

/**
 * Represents an environment variable in Postman.
 */
export interface EnvironmentVariable {
  key: string;
  value: string;
}

/**
 * Details of an API, including headers, path parameters, query parameters, and the request body.
 */
export interface ApiDetails {
  headers: Record<string, string>;
  path_params: Record<string, any>;
  query_params: Record<string, any>;
  request_body: any;
}

/**
 * NEW change here 21-02-2025: Interface for detailed API descriptions.
 */
export interface ApiDescription {
  api: string; // Overall API description
  path_param: Record<string, string>; // Mapping of each path parameter key to its description
  query_param: Record<string, string>; // Mapping of each query parameter key to its description
  request_body: Record<string, string>; // Mapping of each request body field key to its description
}

/**
 * Information about an API endpoint extracted from the Postman collection.
 */
export interface ApiInfo {
  id: string; // Unique identifier (UUID)
  controller: string; // Name of the controller or group
  name: string; // Name of the API endpoint
  description: ApiDescription; // NEW change here 21-02-2025: Detailed description info
  method: string; // HTTP method (GET, POST, etc.)
  url: string; // Raw URL string
  headers: Record<string, string>; // Headers to be sent with the request
  body: any; // Raw body content
  requires_auth: boolean; // Indicates if the API requires authentication
  file_fields: string[]; // List of file fields in the request body
  details: ApiDetails; // Detailed structured information
}

/**
 * Represents the entire Postman collection.
 */
export interface PostmanCollection {
  info: PostmanInfo; // General information about the collection
  item: PostmanItem[]; // Array of items (folders or requests)
  variable?: PostmanVariable[]; // Array of environment variables
  auth?: any;
}

/**
 * General information about the Postman collection.
 */
export interface PostmanInfo {
  name: string; // Name of the collection
  _postman_id: string; // Unique Postman ID
  description?: string; // Optional description
  schema: string; // Schema URL
}

/**
 * Represents an environment variable in Postman.
 */
export interface PostmanVariable {
  key: string;
  value: string;
  type?: string; // Optional type (e.g., text, secret)
  enabled?: boolean; // Optional flag indicating if the variable is enabled
  description?: string;
}

/**
 * Represents an item in the Postman collection, which can be a request or a folder containing sub-items.
 */
export interface PostmanItem {
  name: string; // Name of the item
  request?: PostmanRequest; // Request details if the item is a request
  response?: PostmanResponse[]; // Array of responses (if any)
  item?: PostmanItem[]; // Nested items (if the item is a folder)
  description?: string; // Optional description
  auth?: any;
}

/**
 * Represents a request within a Postman item.
 */
export interface PostmanRequest {
  method: string; // HTTP method (GET, POST, etc.)
  header?: PostmanHeader[]; // Array of headers
  url: PostmanURL; // URL information
  auth?: PostmanAuth; // Authentication information
  body?: PostmanBody; // Request body (if any)
  description?: string; // Optional description
}

/**
 * Represents a response within a Postman item.
 */
export interface PostmanResponse {
  code: number; // HTTP status code
  header: PostmanHeader[]; // Array of headers in the response
  body: string; // Response body
  status: string; // Status text (e.g., "OK")
  name: string; // Name of the response
}

/**
 * Represents a header in a Postman request or response.
 */
export interface PostmanHeader {
  key: string;
  value: string;
  description?: string; // Optional description
}

/**
 * Represents the URL information in a Postman request.
 */
export interface PostmanURL {
  raw: string; // Raw URL string
  protocol: string; // Protocol (e.g., "https")
  host: string[]; // Host segments
  port?: string; // Optional port
  path: string[]; // Path segments
  query?: PostmanQueryParam[]; // Query parameters
  variable?: PostmanVariable[]; // URL variables
}

/**
 * Represents a query parameter in a Postman URL.
 */
export interface PostmanQueryParam {
  key: string;
  value: string;
  description?: string; // Optional description
}

/**
 * Represents the authentication details in a Postman request.
 */
export interface PostmanAuth {
  type: string; // Type of authentication (e.g., "apikey", "basic")
  apikey?: PostmanAuthAPKey[];
  basic?: PostmanAuthBasic[];
  bearer?: PostmanAuthBearer[];
  oauth1?: PostmanAuthOAuth1[];
  oauth2?: PostmanAuthOAuth2[];
  // Add other authentication types as needed
}

/**
 * Represents API key authentication details.
 */
export interface PostmanAuthAPKey {
  key: string;
  value: string;
  addTo: string; // Where to add the API key (e.g., "header", "query")
}

/**
 * Represents Basic authentication details.
 */
export interface PostmanAuthBasic {
  key: string;
  value: string;
}

/**
 * Represents Bearer authentication details.
 */
export interface PostmanAuthBearer {
  key: string;
  value: string;
}

/**
 * Represents OAuth 1.0 authentication details.
 * Note: OAuth 1.0 is complex and may require additional properties.
 */
export interface PostmanAuthOAuth1 {
  key?: string;
  value?: string;
  // Additional fields like consumerKey, consumerSecret, etc.
}

/**
 * Represents OAuth 2.0 authentication details.
 */
export interface PostmanAuthOAuth2 {
  key: string;
  value: string;
  // Additional fields like accessToken, tokenType, etc.
}

/**
 * Represents the body of a Postman request.
 */
export interface PostmanBody {
  mode: string; // Mode of the body (e.g., "raw", "formdata", "urlencoded")
  raw?: string; // Raw body content
  formdata?: PostmanFormData[];
  urlencoded?: PostmanURLEncoded[];
  // Add other modes as needed (e.g., "file", "graphql")
}

/**
 * Represents a form-data item in a Postman request body.
 */
export interface PostmanFormData {
  key: string;
  value?: string;
  type?: string; // Type of the form-data item (e.g., "text", "file")
  src?: string; // Source file path if type is "file"
  description?: string; // Optional description
}

/**
 * Represents a URL-encoded form item in a Postman request body.
 */
export interface PostmanURLEncoded {
  key: string;
  value: string;
  description?: string; // Optional description
}

// Helper functions (unchanged)
function isNumeric(str: string): boolean {
  if (typeof str !== "string") return false;
  return !isNaN(Number(str)) && !isNaN(parseFloat(str));
}

function isVariableSegment(segment: string): boolean {
  if (segment && segment !== "") {
    return (
      segment.startsWith(":") ||
      (segment.startsWith("{") && segment.endsWith("}")) ||
      (segment.startsWith("{{") && segment.endsWith("}}"))
    );
  } else {
    return false;
  }
}

function extractVariableName(segment: string): string {
  if (segment.startsWith(":")) {
    return segment.slice(1);
  } else if (segment.startsWith("{{") && segment.endsWith("}}")) {
    return segment.slice(2, -2);
  } else if (segment.startsWith("{") && segment.endsWith("}")) {
    return segment.slice(1, -1);
  } else {
    return segment;
  }
}

function getSampleValueForParam(paramName: string,environmentVariables?: any[]): any {
  if (paramName.toLowerCase().includes("id")) {
    return 1;
  } else {
    return environmentVariables?.find((env) => env.key === paramName)?.value;
  }
}

function addHeader(
  apiInfo: ApiInfo,
  apiDetails: ApiDetails,
  key: string,
  value: string
) {
  apiInfo.headers[key] = value;
  apiDetails.headers[key] = value;
}

/**
 * Extracts all variable names from a given string in the format {{variable}}.
 * @param str The string to search for variables.
 * @returns A set of unique variable names.
 */
function extractVariables(str: string): Set<string> {
  const regex = /\{\{(\w+)\}\}/g;
  const variables = new Set<string>();
  let match: RegExpExecArray | null;
  while ((match = regex.exec(str)) !== null) {
    variables.add(match[1]);
  }
  return variables;
}

function removeComments(jsonString: string): string {
  // 1) Remove all multi-line comments first
  let withoutMultiLine = jsonString.replace(/\/\*[\s\S]*?\*\//g, "");

  // 2) Remove single-line comments *unless* they are part of http(s)://
  const lines = withoutMultiLine.split("\n");

  const processed = lines.map((line) => {
    let result = "";
    let searchStart = 0;

    while (true) {
      const idx = line.indexOf("//", searchStart);
      if (idx === -1) {
        result += line.slice(searchStart);
        break;
      }

      const prefix = line.slice(Math.max(0, idx - 6), idx).toLowerCase();
      if (prefix.endsWith("https:") || prefix.endsWith("http:")) {
        result += line.slice(searchStart, idx + 2);
        searchStart = idx + 2;
      } else {
        result += line.slice(searchStart, idx);
        break;
      }
    }

    return result;
  });

  return processed.join("\n");
}

/**
 * Loads a Postman collection and extracts environment variables and API information.
 * Variables are only extracted from the request parts (URL, headers, path params, query params, request body).
 * NEW change here 21-02-2025: Also extracts detailed descriptions for API, path parameters, query parameters, and request body fields.
 * @param postmanCollection The Postman collection to load.
 * @returns An object containing environment variables and APIs.
 */
export async function loadPostmanCollection(
  postmanCollection: PostmanCollection
): Promise<{
  env: EnvironmentVariable[];
  apis: ApiInfo[];
}> {
  // Validate Postman collection structure
  if (!postmanCollection.item || !Array.isArray(postmanCollection.item)) {
    throw new Error(
      "Invalid Postman collection: 'item' property is missing or not an array."
    );
  }

  // Load existing environment variables
  let environmentVariables: EnvironmentVariable[] = [];
  const existingEnvKeys = new Set<string>();
  if (postmanCollection.variable) {
    environmentVariables = postmanCollection.variable.map((variable: any) => ({
      key: variable.key,
      value: variable.value,
    }));

    postmanCollection.variable.forEach((variable: any) => {
      existingEnvKeys.add(variable.key);
    });
  }

  // Set to collect all unique variables found in requests
  const collectedVariables = new Set<string>();

  let apis: ApiInfo[] = [];
  // Helper to normalize URL from v2.0 vs v2.1
  function normalizeUrl(urlValue: string | PostmanURL) {
    if (typeof urlValue === "string") {
      try {
        const parsed = new URL(urlValue);
        const raw = urlValue;
        const host = parsed.hostname ? parsed.hostname.split(".") : [];
        const path = parsed.pathname
          ? parsed.pathname.split("/").filter((seg) => seg !== "")
          : [];
        const query = Array.from(parsed.searchParams).map(([key, value]) => ({
          key,
          value,
        }));
        return { raw, host, path, query };
      } catch (err) {
        return { raw: urlValue, host: [], path: [] };
      }
    } else {
      const { raw = "", host = [], path = [], query = [] } = urlValue;
      return { raw, host, path, query };
    }
  }

  /**
   * Processes a single Postman item (request or folder).
   * Extracts variables and detailed descriptions from the request parts.
   * @param item The Postman item to process.
   * @param controllerName The name of the controller or folder.
   * @param inheritedAuth Auth inherited from parent folders or collection
   */
  const processItem = async (
    item: PostmanItem,
    controllerName: string,
    inheritedAuth: any
  ) => {
    let currentAuth = inheritedAuth;

    // If this item (folder or request) has its own auth, override the inheritedAuth
    if (item.auth) {
      currentAuth = item.auth;
    }

    if (item.request) {
      const requestInfo = item.request as PostmanRequest;
      if (!requestInfo.url) {
        // throw Error("URL not found in request object");
        // continue;
        return;
      }
      const { raw, host, path, query } = normalizeUrl(requestInfo.url);

      // NEW change here 21-02-2025: Extract overall API description from request or item
      const overallDesc = requestInfo.description || item.description || "";

      // NEW change here 21-02-2025: Build the structured API description object.
      const apiDesc: ApiDescription = {
        api: overallDesc,
        path_param: {},
        query_param: {},
        request_body: {},
      };

      // NEW change here 21-02-2025: Extract path parameter descriptions from URL variables.
      if (requestInfo.url.variable && Array.isArray(requestInfo.url.variable)) {
        for (const variable of requestInfo.url.variable) {
          apiDesc.path_param[variable.key] = variable.description || "";
        }
      }

      // NEW change here 21-02-2025: Extract query parameter descriptions from URL query array.
      if (requestInfo.url.query && Array.isArray(requestInfo.url.query)) {
        for (const queryParam of requestInfo.url.query) {
          apiDesc.query_param[queryParam.key] = queryParam.description || "";
        }
      }

      const apiInfo: ApiInfo = {
        controller: controllerName,
        name: item.name,
        description: apiDesc, // NEW change here 21-02-2025: Attach structured description
        headers: {},
        body: {},
        requires_auth: false,
        file_fields: [],
        id: uuidv4(),
        method: requestInfo.method,
        url: raw,
        details: {
          headers: {},
          path_params: {},
          query_params: {},
          request_body: {},
        },
      };

      // Determine final auth for this request
      let finalAuth = requestInfo.auth || currentAuth;

      if (finalAuth && finalAuth.type && finalAuth.type !== "noauth") {
        switch (finalAuth.type) {
          case "apikey": {
            if (!finalAuth.apikey) break;
            const apiKeyConfig = Array.isArray(finalAuth.apikey)
              ? finalAuth.apikey
              : [finalAuth.apikey];
            const keyObj = apiKeyConfig.find((a: any) => a.key === "key");
            const valueObj = apiKeyConfig.find((a: any) => a.key === "value");
            const addToObj = apiKeyConfig.find((a: any) => a.key === "addTo");

            if (keyObj && valueObj && addToObj) {
              const { value: key } = keyObj;
              const apiKeyValue = valueObj.value;
              const addTo = addToObj.value;

              if (addTo === "header") {
                addHeader(apiInfo, apiInfo.details, key, apiKeyValue);
              } else if (addTo === "query") {
                apiInfo.details.query_params[key] = apiKeyValue;
              }

              apiInfo.requires_auth = true;

              if (
                apiKeyValue &&
                typeof apiKeyValue === "string" &&
                apiKeyValue.includes("{{")
              ) {
                extractVariables(apiKeyValue).forEach((varName) =>
                  collectedVariables.add(varName)
                );
              }
            }
            break;
          }

          /**
           * -------------------
           *      BASIC AUTH
           * -------------------
           */
          case "basic": {
            if (!finalAuth.basic) break;
            const basicConfig = Array.isArray(finalAuth.basic)
              ? finalAuth.basic
              : [finalAuth.basic];
            const usernameObj = basicConfig.find(
              (a: any) => a.key === "username"
            );
            const passwordObj = basicConfig.find(
              (a: any) => a.key === "password"
            );

            if (usernameObj && passwordObj) {
              const authHeader = `Basic {{API_Key_ID}}:{{API_KEY_SECRET}}`;
              addHeader(apiInfo, apiInfo.details, "Authorization", authHeader);
              apiInfo.requires_auth = true;

              if (
                usernameObj.value &&
                typeof usernameObj.value === "string" &&
                usernameObj.value.includes("{{")
              ) {
                extractVariables(usernameObj.value).forEach((varName) =>
                  collectedVariables.add(varName)
                );
              }
              if (
                passwordObj.value &&
                typeof passwordObj.value === "string" &&
                passwordObj.value.includes("{{")
              ) {
                extractVariables(passwordObj.value).forEach((varName) =>
                  collectedVariables.add(varName)
                );
              }
            }
            break;
          }

          /**
           * -------------------
           *     BEARER TOKEN
           * -------------------
           */
          case "bearer": {
            if (!finalAuth.bearer) break;
            const bearerConfig = Array.isArray(finalAuth.bearer)
              ? finalAuth.bearer
              : [finalAuth.bearer];
            const tokenObj = bearerConfig.find((a: any) => a.key === "token");
            if (tokenObj && tokenObj.value) {
              const authHeader = `Bearer ${tokenObj.value}`;
              addHeader(apiInfo, apiInfo.details, "Authorization", authHeader);
              apiInfo.requires_auth = true;

              if (
                typeof tokenObj.value === "string" &&
                tokenObj.value.includes("{{")
              ) {
                extractVariables(tokenObj.value).forEach((varName) =>
                  collectedVariables.add(varName)
                );
              }
            }
            break;
          }

          /**
           * -------------------
           *       OAUTH1
           * -------------------
           */
          case "oauth1": {
            if (!finalAuth.oauth1) {
              console.warn(`OAuth 1.0 config missing, skipping...`);
              break;
            }
            const oauth1Config = Array.isArray(finalAuth.oauth1)
              ? finalAuth.oauth1
              : [finalAuth.oauth1];
            console.warn(`OAuth 1.0 is not fully supported in this parser.`);
            apiInfo.requires_auth = true;
            break;
          }

          /**
           * -------------------
           *       OAUTH2
           * -------------------
           */
          case "oauth2": {
            if (!finalAuth.oauth2) break;
            const oauth2Config = Array.isArray(finalAuth.oauth2)
              ? finalAuth.oauth2
              : [finalAuth.oauth2];

            const tokenObj = oauth2Config.find(
              (a: any) => a.key === "accessToken"
            );
            const tokenTypeObj = oauth2Config.find(
              (a: any) => a.key === "tokenType"
            );

            // Skipping any changes for oauth2 as requested.
            if (
              tokenObj &&
              tokenObj.value &&
              tokenTypeObj &&
              tokenTypeObj.value
            ) {
              const authHeader = `${tokenTypeObj.value} ${tokenObj.value}`;
              addHeader(apiInfo, apiInfo.details, "Authorization", authHeader);
              if (
                typeof tokenObj.value === "string" &&
                tokenObj.value.includes("{{")
              ) {
                extractVariables(tokenObj.value).forEach((varName) =>
                  collectedVariables.add(varName)
                );
              }
              if (
                typeof tokenTypeObj.value === "string" &&
                tokenTypeObj.value.includes("{{")
              ) {
                extractVariables(tokenTypeObj.value).forEach((varName) =>
                  collectedVariables.add(varName)
                );
              }
            }
            break;
          }

          /**
           * -------------------
           *         JWT
           * -------------------
           */
          case "jwt": {
            if (!finalAuth.jwt) break;
            const jwtConfig = Array.isArray(finalAuth.jwt)
              ? finalAuth.jwt
              : [finalAuth.jwt];
            const secretObj = jwtConfig.find((a: any) => a.key === "secret");
            const algorithmObj = jwtConfig.find(
              (a: any) => a.key === "algorithm"
            );
            const payloadObj = jwtConfig.find((a: any) => a.key === "payload");
            const isSecretBase64EncodedObj = jwtConfig.find(
              (a: any) => a.key === "isSecretBase64Encoded"
            );
            const addTokenToObj = jwtConfig.find(
              (a: any) => a.key === "addTokenTo"
            );
            const headerPrefixObj = jwtConfig.find(
              (a: any) => a.key === "headerPrefix"
            );
            const queryParamKeyObj = jwtConfig.find(
              (a: any) => a.key === "queryParamKey"
            );

            if (secretObj && algorithmObj && payloadObj && addTokenToObj) {
              const secret = secretObj.value || "";
              const algorithm = algorithmObj.value || "HS256";
              const payloadStr = payloadObj.value || "{}";
              const isSecretBase64Encoded = isSecretBase64EncodedObj
                ? isSecretBase64EncodedObj.value
                : false;
              const addTokenTo = addTokenToObj.value;
              const headerPrefix = headerPrefixObj
                ? headerPrefixObj.value
                : "Bearer";
              const queryParamKey = queryParamKeyObj
                ? queryParamKeyObj.value
                : "token";

              if (typeof secret === "string" && secret.includes("{{")) {
                extractVariables(secret).forEach((varName) =>
                  collectedVariables.add(varName)
                );
              }
              if (typeof payloadStr === "string" && payloadStr.includes("{{")) {
                extractVariables(payloadStr).forEach((varName) =>
                  collectedVariables.add(varName)
                );
              }

              let payloadObject = {};
              try {
                payloadObject = JSON.parse(payloadStr);
              } catch (err) {
                console.warn(`Failed to parse JWT payload JSON:`, err);
              }

              let signingKey: Uint8Array;
              if (isSecretBase64Encoded) {
                try {
                  const decodedSecret = atob(secret);
                  signingKey = new TextEncoder().encode(decodedSecret);
                } catch (err) {
                  console.warn(`Failed to decode Base64 JWT secret:`, err);
                  signingKey = new TextEncoder().encode(secret);
                }
              } else {
                signingKey = new TextEncoder().encode(secret);
              }

              try {
                const token = await new SignJWT(payloadObject)
                  .setProtectedHeader({ alg: algorithm })
                  .sign(signingKey);

                if (addTokenTo === "header") {
                  addHeader(
                    apiInfo,
                    apiInfo.details,
                    "Authorization",
                    `${headerPrefix} ${token}`
                  );
                } else if (addTokenTo === "query") {
                  apiInfo.details.query_params[queryParamKey] = token;
                }

                apiInfo.requires_auth = true;
              } catch (err) {
                console.error(`Failed to sign JWT:`, err);
              }
            }
            break;
          }

          default: {
            console.warn(
              `Authentication type "${finalAuth.type}" not supported.`
            );
            break;
          }
        }
      } else {
        if (requestInfo.header) {
          for (const header of requestInfo.header) {
            addHeader(apiInfo, apiInfo.details, header.key, header.value);
            if (header.value.includes("{{")) {
              extractVariables(header.value).forEach((varName) =>
                collectedVariables.add(varName)
              );
            }
          }
        }
        apiInfo.requires_auth =
          "Authorization" in apiInfo.headers ||
          "authorization" in apiInfo.headers;
      }

      // Extract variables from URL raw string
      if (raw.includes("{{")) {
        extractVariables(raw).forEach((varName) =>
          collectedVariables.add(varName)
        );
      }

      if (host && host.length) {
        for (const hostSegment of host) {
          if (hostSegment.includes("{{")) {
            extractVariables(hostSegment).forEach((varName) =>
              collectedVariables.add(varName)
            );
          }
        }
      }

      if (query && query.length) {
        for (const queryParam of query) {
          console.log(
            "log:: query param",
            queryParam,
            isVariableSegment(queryParam.value)
          );
          if (isVariableSegment(queryParam.value)) {
            const paramName = extractVariableName(queryParam.value);
            apiInfo.details.query_params[queryParam.key] =  queryParam.value
              // getSampleValueForParam(paramName,environmentVariables);
          } else {
            apiInfo.details.query_params[queryParam.key] = queryParam.value;
            if (
              typeof queryParam.value === "string" &&
              queryParam.value.includes("{{")
            ) {
              extractVariables(queryParam.value).forEach((varName) =>
                collectedVariables.add(varName)
              );
            }
          }
        }
      }

      // Process path segments (for variable extraction and path parameters)
      if (path && path.length) {
        for (const segment of path) {
          if (isVariableSegment(segment)) {
            const varName = extractVariableName(segment);
            // NEW change here 21-02-2025: Add path parameter to details with template format
            apiInfo.details.path_params[varName] = `{{${varName}}}`;
            collectedVariables.add(varName);
          } else if (segment.includes("{{")) {
            extractVariables(segment).forEach((varName) =>
              collectedVariables.add(varName)
            );
          }
        }
      }
      // Extract request body and its field descriptions
      const body: any = requestInfo.body || {};
      if (body.mode === "raw" && body.raw) {
        const contentType =
          apiInfo.headers["Content-Type"] || apiInfo.headers["content-type"];
        if (contentType && contentType.includes("application/json")) {
          try {
            apiInfo.details.request_body = JSON.parse(body.raw);
          } catch (e) {
            console.error("Failed to parse JSON body:", e);
            apiInfo.details.request_body = body.raw;
          }
        } else {
          apiInfo.details.request_body = body.raw;
        }
        // In raw mode, we do not have individual field descriptions.
      } else if (body.mode === "formdata") {
        for (const formDataItem of body.formdata || []) {
          if (formDataItem.type === "file") {
            apiInfo.file_fields.push(formDataItem.key);
            apiInfo.details.request_body[formDataItem.key] =
              formDataItem.src || "";
            if (formDataItem.src && typeof formDataItem.src === "string") {
              apiInfo.details.request_body[formDataItem.key] = removeComments(
                formDataItem.src
              );
            }
            // NEW change here 21-02-2025: Store description for request body field.
            apiDesc.request_body[formDataItem.key] =
              formDataItem.description || "";
          } else {
            const cleanValue = formDataItem.value
              ? removeComments(formDataItem.value)
              : "";
            apiInfo.details.request_body[formDataItem.key] = cleanValue;
            // NEW change here 21-02-2025: Store description for request body field.
            apiDesc.request_body[formDataItem.key] =
              formDataItem.description || "";
          }
        }
      } else if (body.mode === "urlencoded") {
        for (const urlEncodedItem of body.urlencoded || []) {
          const cleanValue = urlEncodedItem.value
            ? removeComments(urlEncodedItem.value)
            : "";
          apiInfo.details.request_body[urlEncodedItem.key] = cleanValue;
          // NEW change here 21-02-2025: Store description for request body field.
          apiDesc.request_body[urlEncodedItem.key] =
            urlEncodedItem.description || "";
        }
      }

      console.log("log:: api infoo", apiInfo);

      apis.push(apiInfo);
    }

    // Recursively process nested items (folders)
    if (item.item && Array.isArray(item.item)) {
      for (const subItem of item.item) {
        await processItem(subItem, item.name, currentAuth);
      }
    }
  };

  // Start processing from the root items
  const rootAuth = postmanCollection.auth || null;

  for (const item of postmanCollection.item) {
    await processItem(item, item.name, rootAuth);
  }

  // After processing all requests, add collected variables to environmentVariables
  for (const varName of collectedVariables) {
    if (!existingEnvKeys.has(varName)) {
      environmentVariables.push({
        key: varName,
        value: "",
      });
    }
  }
  const result = {
    env: environmentVariables,
    apis: apis,
  };
  console.log(result);

  return result;
}
