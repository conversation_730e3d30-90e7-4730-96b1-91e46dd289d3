# Use the official Node.js image as the base image
FROM node:18 as base

# Development stage
FROM base as development
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
ENV NEXT_ENV=development
RUN npm run build
EXPOSE 3003
CMD ["npm", "run", "dev-start"]

# Production stage
FROM base as production
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN echo "Building the app"
ENV NEXT_ENV=production
RUN npm run build
EXPOSE 3000
CMD ["npm", "run", "prod-start"]