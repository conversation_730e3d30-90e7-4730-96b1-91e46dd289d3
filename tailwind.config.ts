import type { Config } from "tailwindcss";

const { nextui } = require("@nextui-org/theme");

export default {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@nextui-org/theme/dist/components/(popover|progress|spinner|button|ripple).js"
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        drcodePurple: "#875bf8",
        drcodeGray:"#5E5E66",
        drcodeGrayLight:"#D9D9E8",
        drcodeBlue:"#0D0D22",
        drCodeDarkBlue:"#1B1B41"
      },
    },
  },
  plugins: [nextui({
    themes: {
      "dark": {
        extend: "dark", // <- inherit default values from dark theme
        colors: {
          background: "#080814",
          foreground: "#ffffff",
         drcodePurple: "#875bf8",

          primary: {
            50: "#D6D6F3",
            100: "#D6D6F3",
            200: "#AFAFE7",
            300: "#7676B8",
            400: "#3E3E72",
            500: "#080814",
            600: "#050511",
            700: "#04040E",
            800: "#02020B",
            900: "#010109",
            DEFAULT: "#DD62ED",
            foreground: "#ffffff",
          },
          focus: "#F182F6",
          gray:{
            900:"#5E5E66"
          }
        }
      }
    }
  })],
} satisfies Config;
