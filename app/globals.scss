@tailwind base;
@tailwind components;
@tailwind utilities;

$primary-color: #875bf8;
$secondary-color: #1b1b41;
$tertiary-color: #f3effe;
$delete-color: #cf1322;
$primary-background: #080814;


$default-font: 500;
$defaul-text-size: 16px;
$primary-btn-text: #6b6baa;
$primary-text: #d1d1e3;
$secondary-text: #9494a1;
$primary-light-text: #b2b2c1;
$primary-border: #2e2e60;
$primary-border-hover: #6b6baa;
$drcode-blue: #0d0d22;

body {
  background-color: $primary-background;
  color: $secondary-text;
  font-weight: $default-font;
  font-size: $defaul-text-size;
}

:root {
  background-color: $primary-background;
  color: $secondary-text;
  font-weight: $default-font;
  font-size: $defaul-text-size;
}

body {
  
  /* Webkit Scrollbar */
  &::-webkit-scrollbar {
    width: 3px; /* Vertical scrollbar width */
    height: 17px; /* Horizontal scrollbar height */
    border-radius: 20px; /* Rounded scrollbar edges */
  }

  &::-webkit-scrollbar-track {
    background: transparent; /* Transparent scrollbar track */
  }

  &::-webkit-scrollbar-thumb {
    background-color: #6b6baa; /* Set scrollbar color */
    border-radius: 20px; /* Rounded scrollbar thumb */
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #555; /* Darker color when hovered */
  }

  /* Firefox Scrollbar */
  scrollbar-width: thin;
  scrollbar-color: #6b6baa transparent;
}

/* Toast Container */
.Toastify__toast-container {
  top: 0;
  /* Adjust if necessary */
  right: 0;
  /* Adjust if necessary */
}

.Toastify__toast {
  background: linear-gradient(
    115.34deg,
    #5635aa 1.26%,
    #41297f 66.07%
  ) !important;
  color: #ffffff !important;
  border: 1px solid #6041b0 !important;
}

.background-secondary {
  background-color: #1b1b41;
}

.background-primary {
  background-color: #080814;
}

.primary-border {
  border: 1px solid #2e2e60;

  &.border-thick-dashed {
    border-style: dashed;
    border-width: 3px;
  }
}

.secondary-border {
  border: 1px solid #2424499b;
}
.mention-highlight {
  
  color: rgb(239, 165, 252);
  border-radius: 4px;
  font-weight: 500;
  padding: 2px 4px;
  margin: 0 1px;
}

.button--primary {
  background-color: $primary-color;
  color: #ffffff;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
}

.button--outlined-primary {
  background-color: transparent;
  color: $primary-color;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid $primary-color;
  font-weight: 600;
  font-size: 12px;
}

.button--delete {
  background-color: transparent;
  color: $delete-color;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid $delete-color;
  font-weight: 500;
  font-size: 12px;
}

.button--soft-primary {
  background: transparent;
  color: $primary-color;
  border: 1px solid $primary-color;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 600;
  font-size: 12px;
}

.button--dark-secondary {
  background-color: $secondary-color;
  color: $primary-btn-text;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 600;
  font-size: 12px;
}

.button--primary,
.button--outlined-primary,
.button--delete,
.button--soft-primary,
.button--dark-secondary {
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.button--primary,
.button--outlined-primary,
.button--delete,
.button--soft-primary,
.button--dark-secondary {
  &:disabled {
    cursor: not-allowed;
  }
}

.primary-light-text {
  color: $primary-light-text;
  font-weight: 400;
}

.primary-input {
  border: 1px solid $primary-border;
  padding: 8px 12px;
  background-color: transparent;
  border-radius: 5px;
  &:hover {
    border: 1px solid $primary-border-hover;
    &:read-only {
      border: 1px solid $primary-border;
    }
  }
}

.button--small {
  padding: 4px 8px;
}

.button--medium {
  padding: 10px 20px;
}

.button--large {
  padding: 10px 30px;
}

.primary--border {
  border: 1px solid $primary-border;
}

.primary--textarea {
  background: transparent;
  border: 1px solid $primary-border;
  width: 100%;
  height: 100px;
  border-radius: 20px;
  padding: 1rem;
}

.title--large {
  font-size: 28px;
  font-weight: 600;
  line-height: 33.6px;
  text-decoration-skip-ink: none;
  color: $primary-text;
}

.title--medium {
  font-size: 18px;
  font-weight: 600;
  line-height: 33.6px;
  text-decoration-skip-ink: none;
  color: $primary-text;
}

.title--small {
  font-size: 12px;
  font-weight: 600;
  line-height: 33.6px;
  text-decoration-skip-ink: none;
  color: $primary-text;
}

.react-select__control {
  background-color: transparent !important;
  border: 1px solid $primary-border !important;
  border-radius: 5px !important;
  height: 100%;
  &:hover {
    border: 1px solid $primary-border-hover !important;
  }
}
.react-select__control--is-focused {
  border: 1px solid #875bf8 !important;
  box-shadow: #875bf8 0px 0px 0px 1px !important;
}

.react-select__single-value {
  color: #e2e2ed !important;
}

.react-select__option:hover {
  background-color:#2E2E60!important;
  color: white;
}

.react-select__menu {
  background-color: #131330 !important;
  border-radius: 4px;
}

.react-select__option {
  background-color: #131330 !important;
}

.react-select__option--is-selected {
  background-color: #2e2e60 !important;
  color: white;
}

.react-select__indicator-separator {
  display: none !important;
}

.react-select__multi-value {
  background-color: $primary-border !important;
  border-radius: 12px !important;
  color: $primary-text !important;
}

.react-select__multi-value__label {
  color: $primary-text !important;
}

.search-input-container {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  max-width: 300px;
  /* Adjust as needed */
}

.search-icon {
  position: absolute;
  left: 10px;
  margin-right: 8px;
  /* Add spacing between icon and input */
}

.pop-up {
  z-index: 999;
  position: fixed;
  background-color: white;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #0d0d22;
  border: 1px solid #1b1b41;
  border-radius: 16px;
}

.primary-search-input {
  // flex: 1;
  padding: 8px 12px;
  padding-left: 36px;
  /* Ensure space for the icon inside the input */
  outline: none;
}

.primary-input-next {
  background-color: #080814;
  margin: 0;
  padding: 0 !important;
}

.primary-input-next input {
  padding: 8px 12px;
  background-color: #080814;
  border-radius: 8px;
  font-size: 24px;
  font-weight: bold;
  color: #d1d1e3;
}

.dzu-previewFileName {
  color: white !important;
  margin-bottom: 12px;
}
.dzu-previewStatusContainer span {
  color: white !important;
}

.overflow-container::-webkit-scrollbar-track {
  background: transparent;
  margin-block: 5px; /* Adds space above and below the scrollbar */
}

.overflow-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
}
