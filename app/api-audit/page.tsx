import AuditHeader from "@/components/api-audit/landing-page/AuditHeader";
import AuditRedirectButton from "@/components/api-audit/landing-page/AuditRedirectButton";
import Banner from "@/components/landing/Banner";
import Footer from "@/components/landing/Footer";
import Image from "next/image";
import React from "react";

const benefit_1 = "/rocket_productivity_1.svg";
const benefit_2 = "/clock_2.svg";
const benefit_3 = "/risk_3.svg";
const benefit_4 = "/quality_4.svg";
const benefit_5 = "/integration_5.svg";
const benefit_6 = "/circuit_ai_6.svg";

const FEATURES = [
  {
    id: 1,
    title: "Postman Collection Integration",
    description:
      "Seamlessly upload api and audit your APls directly from Postman collections.",
    image: "/audit-postman-update.svg",
  },
  {
    id: 2,
    title: "In-Depth Analysis",
    description:
      "Gain valuable insights in to your APIs performance with detailed issue track!",
    image: "/audit-analysis.svg",
  },
  {
    id: 3,
    title: "Security Enhancements",
    description:
      "Identify vulnerabilities ard harden your APIs against potential threats.",
    image: "/audit-security.svg",
  },
];

const KEY_BENEFITS = [
  {
    id: 1,
    title: "Boost Team Productivity",
    description:
      "Automate code reviews, testing, and monitoring with DrCode to free your team for high-impact tasks. Save hours every week during the development lifecycle and focus on what truly matters.",
    image: benefit_1,
  },
  {
    id: 2,
    title: "Empower Your Testing Team",
    description:
      "Streamline developer-tester collaboration with DrCode’s VS Code extension, enabling seamless API-level testing for faster, more efficient results.",
    image: benefit_2,
  },
  {
    id: 3,
    title: "Effective Risk Mitigation",
    description:
      "Prevent code issues with DrCode’s proactive solutions, ensuring a smooth development process while minimizing downtime and costly errors.",
    image: benefit_3,
  },
  {
    id: 4,
    title: "Improve Code Quality and Reliability",
    description:
      "Boost code quality and reliability with advanced automation and intelligent insights, streamlining workflows to deliver robust, error-free results with confidence.",
    image: benefit_4,
  },
  {
    id: 5,
    title: "Seamless Integration",
    description:
      "Get started effortlessly with our 1-click installation VS Code. Experience the tool without the hassle of complex setup or high integration costs.",
    image: benefit_5,
  },
  {
    id: 6,
    title: "Stay Ahead with AI-Powered Innovation",
    description:
      "Transform your development lifecycle with AI tools built for modern challenges.",
    image: benefit_6,
  },
];

const WHY_CHOOSE_US = [
  {
    id: 1,
    title: "Our Vision",
    description:
      "To simplify technology and make it usable and accessible to everyone in the world.",
    image: "/EyeScan.svg",
  },
  {
    id: 2,
    title: "Our Mission",
    description:
      "To help users build, grow, and scale by adapting to scalable and affordable technology.",
    image: "/Target.svg",
  },
  {
    id: 3,
    title: "Our Values",
    description: "We're obsessed with ownership, integrity, and agility.",
    image: "/HandHeart.svg",
  },
];

const ApiAuditLandingPage = () => {
  return (
    <div className="px-24 py-4 overflow-auto h-screen overflow-x-hidden">
      <AuditHeader />

      {/* HERO SECTION */}
      <div className="flex justify-center items-start mt-10">
        <div className="py-10 space-y-5">
          <h1
            className="font-bold text-[64px] text-[#E2E2ED] "
            style={{
              lineHeight: "120%",
            }}
          >
            Audit Your APIs with AI
          </h1>
          <p className="text-[#B2B2C1] text-[18px] font-normal">
            Enhance your API festing with in-depth, Al-powered auditing for
            enhanced reliability and security.
          </p>

          <AuditRedirectButton />
        </div>
        <div className="">
          <Image
            src={"/api-audit-hero.svg"}
            width={100}
            height={100}
            style={{
              height: "100%",
              width: "100%",
            }}
            alt="API Audit"
          />
        </div>
      </div>

      {/* FEATURES */}
      <div className="mt-10">
        <div className="text-[#875BF8] text-[16px] font-bold text-center">
          Features
        </div>

        <div className="mt-2 flex flex-col items-center justify-center">
          <h2 className="text-[#E2E2ED] text-[48px] font-bold">
            Comprehensive API Testing & Auditing
          </h2>
          <p className="text-[#B2B2C1] text-[18px] font-normal text-center">
            Our Al-driven platform conducts thorough audits of your APls
            tielitfy issues and fortify your testing procedures, ensuring robust
            and secure API performance.
          </p>
        </div>

        <div className="flex justify-between items-center mt-20 gap-10">
          {FEATURES.map((feature) => (
            <div
              key={feature.id}
              className="flex flex-col items-center justify-center space-y-3"
            >
              <Image
                src={feature.image}
                width={48}
                height={48}
                alt={feature.title}
              />
              <h3 className="text-[#FFFFFF] text-[24px] font-bold text-center">
                {feature.title}
              </h3>
              <p className="text-[#9494A1] text-[18px] font-normal text-center">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* KEY BENEFITS */}
      <div className="mt-20 relative z-10">
        <div
          className="md:absolute md:inset-0"
          style={{
            backgroundImage: `url('/background.png')`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            maskImage:
              "linear-gradient(to bottom, transparent, #000 20%, #000 50%, transparent 100%)",
            WebkitMaskImage:
              "linear-gradient(to bottom, transparent, #000 20%, #000 80%, transparent 100%)",
            left: "-100px",
            right: "-100px",
            top: "0",
            bottom: "0",
            opacity: "0.3",
            zIndex: "0",
          }}
        />
        <div className="text-[#875BF8] text-[16px] font-bold text-center">
          Key benefits
        </div>
        <h2 className="mt-2 text-[#E2E2ED] text-[40px] font-bold text-center">
          Why choose DrCode?
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-10">
          {KEY_BENEFITS.map((benefit) => (
            <div
              key={benefit.id}
              className="rounded-2xl bg-gradient-to-b from-[#1c1c2e] to-[#1c1c2e]/80 p-6 border border-white/10 shadow-md max-w-sm text-white backdrop-blur-[14px] backdrop-saturate-150"
            >
              <Image
                src={benefit.image}
                width={48}
                height={48}
                alt={benefit.title}
              />
              <h3 className="text-base font-semibold mb-2">{benefit.title}</h3>
              <p className="text-sm text-white/80 leading-[1.5]">
                {benefit.description}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* ABOUT US */}
      <div className="mt-20">
        <div className="text-[#875BF8] text-[16px] font-bold text-center">
          About Us
        </div>
        <h2 className="text-[#E2E2ED] text-[40px] font-bold text-center">
          Join us on a journey of Innovation{" "}
        </h2>
        <div className="flex items-center justify-center gap-20 mt-16">
          {WHY_CHOOSE_US.map((whyChoose) => (
            <div key={whyChoose.id}>
              <Image
                src={whyChoose.image}
                width={48}
                height={48}
                alt={whyChoose.title}
              />
              <h3 className="text-[20px] font-bold mb-2">{whyChoose.title}</h3>
              <p className="text-[16px] text-white/80 font-normal leading-[1.5]">
                {whyChoose.description}
              </p>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-20">
        <Banner />
      </div>

      <div className="mt-20">
        <Footer />
      </div>
    </div>
  );
};

export default ApiAuditLandingPage;
