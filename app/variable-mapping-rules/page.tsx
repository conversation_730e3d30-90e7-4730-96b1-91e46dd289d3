import React from "react";

const VariableMappingRules = () => {
  return (
    <div className="max-w-3xl my-10 mx-auto p-6 bg-[#1B1B41] shadow rounded-lg">
      <h2 className="text-3xl font-semibold mb-6 text-white">Variable Mapping Rules</h2>
      <p className="mb-8 text-gray-300">
        Define how data flows between steps and flows by mapping response variables to request fields.
      </p>

      <ol className="list-decimal list-inside space-y-8 text-gray-300">
        <li>
          <strong className="text-white">Specific Step to Step</strong>
          <pre className="bg-gray-800 text-gray-200 p-3 rounded font-mono overflow-x-auto mt-2">
            flow1.step1.response.session.token ={" "}
            <span className="bg-[#2c2c6c] font-semibold rounded px-1">
              flow2.step1
            </span>
            .request.headers.key
          </pre>
          <p className="mt-1 text-sm">
            Map <code className="bg-[#2c2c6c] px-1 rounded">token</code> from <code>flow1.step1.response</code> to <code>flow2.step1.request.headers.key</code>.
          </p>
        </li>

        <li>
          <strong className="text-white">Global Across All Flows & Steps</strong>
          <pre className="bg-gray-800 text-gray-200 p-3 rounded font-mono overflow-x-auto mt-2">
            flow1.step1.response.data.token = flows.
            <span className="bg-[#2c2c6c] font-semibold rounded px-1">steps</span>
            .request.headers.authorization
          </pre>
          <p className="mt-1 text-sm">
            Replace <code>authorization</code> header in <em>all steps of all flows</em> with <code>token</code> from <code>flow1.step1.response</code>.
          </p>
        </li>

        <li>
          <strong className="text-white">All Steps Within One Flow</strong>
          <pre className="bg-gray-800 text-gray-200 p-3 rounded font-mono overflow-x-auto mt-2">
            flow1.step1.response.data.token = flow1.
            <span className="bg-[#2c2c6c] font-semibold rounded px-1">steps</span>
            .request.headers.authorization
          </pre>
          <p className="mt-1 text-sm">
            Replace <code>authorization</code> header in <em>all steps of flow1</em> with <code>token</code> from <code>flow1.step1.response</code>.
          </p>
        </li>
      </ol>
    </div>
  );
};

export default VariableMappingRules;
