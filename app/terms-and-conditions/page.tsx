// pages/TNCPage.tsx
import Footer from "@/components/landing/Footer";
import Details from "@/components/TNC/Details";
import dynamic from "next/dynamic";
import React from "react";
const ScrollToTop = dynamic(() => import("@/components/common/scroll-to-top/ScrollToTop"), { ssr: false });

const TNCPage: React.FC = () => {
  return (
    <div className="bg-[#080814] min-h-screen text-white w-full max-w-[2050px] mx-auto">
      <ScrollToTop />
      <Details />
      <div className="py-5">
        {" "}
        <Footer />
      </div>
    </div>
  );
};

export default TNCPage;
