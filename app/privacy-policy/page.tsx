import Details from "@/components/PrivacyPolicy/PrivacyDetails";
const ScrollToTop = dynamic(() => import("@/components/common/scroll-to-top/ScrollToTop"), { ssr: false });
import Footer from "@/components/landing/Footer";
import dynamic from "next/dynamic";

const PrivacyPolicy = () => {
  return (
    <div className="bg-[#080814] min-h-screen text-white  w-full max-w-[2050px] mx-auto ">
      <ScrollToTop />
      <Details />

      <div className="py-5">
        {" "}
        <Footer />
      </div>
    </div>
  );
};

export default PrivacyPolicy;
