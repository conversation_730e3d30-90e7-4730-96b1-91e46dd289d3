"use client"
import HomeContent from '@/components/landing/HomeContent';

const Headers = dynamic(() => import("@/components/landing/Headers"), { ssr: false });
const Flow = dynamic(() => import("@/components/landing/Flow"), { ssr: false });
const DescriptionSection = dynamic(() => import("@/components/landing/DescriptionSection"), { ssr: false });
const Footer = dynamic(() => import("@/components/landing/Footer"), { ssr: false });

import BenefitSection from '@/components/landing/Benefit';
import AboutUs from '@/components/landing/AboutUs';
import Review from '@/components/landing/Review';
import Banner from '@/components/landing/Banner';
import dynamic from 'next/dynamic';
import Agents from '@/components/landing/Agents';

export default function Home() {

	return (
		<div className='bg-[#0A0A1A]'>
			<div onClick={() => window.open("https://marketplace.visualstudio.com/items?itemName=DrCodeAI.drcode-vscode-tool", "_blank")}>
				<img
					src={'/Promo.svg'}
					alt="Flow step"
					className="hidden md:block w-full cursor-pointer"
				/>
			</div>
			<div onClick={() => window.open("https://marketplace.visualstudio.com/items?itemName=DrCodeAI.drcode-vscode-tool", "_blank")}>
				<img
					src={'/PromoMobile.svg'}
					alt="Flow step"
					className="block md:hidden w-full"
				/>
			</div>
			<Headers />
			<HomeContent />
			{/* <Flow /> */}
			<Agents />
			<BenefitSection />
			<DescriptionSection />
			<Review />
			<AboutUs />
			<div className="my-32">
				<Banner />
			</div>
			<Footer />
		</div>
	);
}
