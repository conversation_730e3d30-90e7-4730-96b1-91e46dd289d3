import TopLoader from "@/components/common/top-loader/TopLoader";
import type { Metadata } from "next";

export const metadata: Metadata = {
    title: "Environment",
    description: "Dr Code: Experience AI-powered automation API Testing Platform",
    icons: {
        icon: "/logo.png",
        apple: "/logo.png",
    },
    manifest: "/manifest.json"
};

export default function ProjectLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {

    return (
        <div className="pl-[70px]">
            <TopLoader/>
      {children}
        </div>
    );
}
