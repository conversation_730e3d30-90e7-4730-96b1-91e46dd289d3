'use client'
import RequestHeaders from '@/components/add-apis/request-headers/RequestHeaders';
import React, { useEffect, useState } from 'react'
import { KeyValuePair } from 'tailwindcss/types/config';
import Image from 'next/image';
import styles from "./EnironmentDetails.module.scss"
import { ButtonPrimary } from '@/components/common/button-primary/ButtonPrimary';
import { ButtonOutlinedPrimary } from '@/components/common/button-outlined-primary/ButtonOutlinedPrimary';
import Link from 'next/link';
import { useGlobalStore } from '@/stores/globalstore';
import axios from 'axios';
import { useParams, useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import { getDataFromLocalStorage } from '@/temp-utils/globalUtilities';
import { LOCAL_STORAGE_DATA_KEYS, UserData } from '@/temp-utils/Constants/localStorageDataModels';
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from '@/temp-utils/Constants/globalVariables';
import TopLoader from '@/components/common/top-loader/TopLoader';
import useToast from '@/hooks/useToast';

const EnvironmentDetailsPage = () => {

    const {showToast} = useToast()

    const [keyValuePairs, setKeyValuePairs] = useState<KeyValuePair[]>([{ key: '', value: '' }]);
    const [environmentName, setEnvironmentName] = useState('')
    const { showLoader } = useGlobalStore();
    const { environmentId } = useParams();
    const userData: UserData = getDataFromLocalStorage(LOCAL_STORAGE_DATA_KEYS.USER_DATA) as UserData;


    const router = useRouter();

    const handleInputChange = (index: number, field: keyof KeyValuePair, value: string) => {
        const updatedPairs = [...keyValuePairs];
        updatedPairs[index][field] = value;
        if (index === keyValuePairs.length - 1 && (updatedPairs[index].key || updatedPairs[index].value)) {
            updatedPairs.push({ key: '', value: '' });
        }
        setKeyValuePairs(updatedPairs);
    };
    const deleteKeyValuePair = (index: number) => {
        const filterArray = keyValuePairs.filter((value, keyValueindex) => index != keyValueindex)
        setKeyValuePairs(filterArray)
    }

    const createEnvironment = async () => {
        try {
            //showLoader(true)
            let environmentRequestObj: any = {
                "name": environmentName,
                "env": {},
                "user_id": userData.user_id
            }
            let emptyObject = environmentRequestObj?.env
            keyValuePairs.pop()
            keyValuePairs.map((item) => {
                emptyObject[item.key] = item.value
            })
            const response = await axios.post(`${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables`, environmentRequestObj);
            if (response.status == 201) {
                // toast.success('Environment Added')
                showToast('Environment Added', 'success')
                
                router.push("/environment")
            }
        } catch (err) {
            console.error('Error:', err);
        } finally {
            //showLoader(false)
        }
    }
    const updateEnvironment = async () => {
        try {
            //showLoader(true)
            let environmentRequestObj: any = {
                "name": environmentName,
                "env": {},
                "user_id": userData.user_id
            }
            let emptyObject = environmentRequestObj?.env
            keyValuePairs.pop()
            keyValuePairs.map((item) => {
                emptyObject[item.key] = item.value
            })
            const response = await axios.put(`${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables/${environmentId}`, environmentRequestObj);
            if (response.status == 200) {
                // toast.success('Environmet Updated')
                showToast('Environment Updated', 'success')

                // router.push("/environment")
            }
        } catch (err) {
            console.error('Error:', err);
        } finally {
            //showLoader(false)
        }
    }
    const getEnvironmentDetails = async () => {

        try {
            //showLoader(true)
            const response = await axios.get(`${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables/${environmentId}`);
            let responseData: {
                id: number
                env: any
                user_id: string
                name: string
                created_at: string
                updated_at: string
            } = response.data
            setEnvironmentName(responseData.name);
            let empyArray: KeyValuePair[] = []
            for (let key in responseData.env) {
                empyArray.push({ value: responseData.env[key], key: key })
            }
            empyArray.push({ value: "", key: "" })

            setKeyValuePairs(empyArray)

        } catch (err) {
            console.error('Error:', err);
        } finally {
            //showLoader(false)
        }
    }

    useEffect(() => {
        if (environmentId !== 'add') {
            getEnvironmentDetails()
        }
    }, [])


    return (
        <div className={`${styles['environment-details-container']} `}>
                  <TopLoader />
            
            <div onClick={()=>router.back()} className='flex gap-2 cursor-pointer'>
                <Image src={'/arrow-left.png'} quality={100} alt='back' width={20} height={20} />
                <p>Back</p>
            </div>
            <p className='title--medium mt-3'>Environment name</p>
            <input
                value={environmentName}
                onChange={(event) => setEnvironmentName(event.target.value)}
                className="primary-input !rounded-none text-white"
                placeholder="Enter Environment name"
            />

            <div className={`${styles['key-value-table']}`} >
                <div className='grid grid-cols-2 gap-1'>
                    <span className='primary--border px-3 py-2 '>Variable</span>
                    <span className='primary--border px-3 py-2'>Value</span>
                </div>

                {keyValuePairs.length > 0 && keyValuePairs.map((pair, index) => (
                    <div key={index} className={`${styles['key-value-container']} grid grid-cols-2 relative mb-1 gap-1 cursor-pointer  `}>
                        <input
                            value={pair.key}
                            onChange={(event) => handleInputChange(index, 'key', event.target.value)}
                            className="primary-input !rounded-none text-white"
                            placeholder="Enter Variable"
                        />
                        <input
                            value={pair.value}
                            onChange={(event) => handleInputChange(index, 'value', event.target.value)}
                            className="primary-input !rounded-none  text-white"
                            placeholder="Enter value"
                        />
                        {index != 0 && <div onClick={() => deleteKeyValuePair(index)} className={`${styles['trash-bin-container']} absolute right-2 top-3`} >
                            <Image
                                src="/trashbin.svg"
                                alt=""
                                width={20}
                                height={20}
                            />
                        </div>}

                    </div>
                ))
                }
            </div>
            <div className='flex gap-4 mt-4'>
                {environmentId == 'add' ?
                    <ButtonPrimary onClick={createEnvironment} size='medium' text='Create' />
                    :
                    <ButtonPrimary onClick={updateEnvironment} size='medium' text='Update' />}
                <ButtonOutlinedPrimary onClick={() =>router.back()} size='medium' text='Back' />
            </div>
        </div>
    )
}

export default EnvironmentDetailsPage