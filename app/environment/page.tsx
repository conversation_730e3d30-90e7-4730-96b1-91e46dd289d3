'use client'
import { ButtonOutlinedPrimary } from '@/components/common/button-outlined-primary/ButtonOutlinedPrimary';
import DrCodeTable from '@/components/common/drcode-table/DrCodeTable';
import { DrCodeTableColumn } from '@/components/common/drcode-table/DrCodeTableTypes';
import Link from 'next/link';
import React, { useEffect, useState } from 'react'
import { GroupTableData } from '../project/[projectId]/page';
import { KeyValuePair } from 'tailwindcss/types/config';
import { useGlobalStore } from '@/stores/globalstore';
import axios from 'axios';
import styles from "./[eniromentId]/EnironmentDetails.module.scss"
import { useRouter } from 'next/navigation';
import { getDataFromLocalStorage } from '@/temp-utils/globalUtilities';
import { LOCAL_STORAGE_DATA_KEYS, UserData } from '@/temp-utils/Constants/localStorageDataModels';
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from '@/temp-utils/Constants/globalVariables';
import Image from 'next/image';

export interface EnvironmentList {
    id: number
    env: { [key: string]: any }
    user_id: string
    name: string
    created_at: string
    updated_at: string
}
export default function Environment() {


    const { showLoader } = useGlobalStore();
    const [environmentList, setEnvironmentList] = useState<EnvironmentList[]>([])
    const [envDetails, setEnvDetails] = useState<EnvironmentList>()
    const [showDeletePopup, setShowDeletePopup] = useState(false);
    const router = useRouter()
    const userData: UserData = getDataFromLocalStorage(LOCAL_STORAGE_DATA_KEYS.USER_DATA) as UserData;




    const handleDeleteClick = (event: any, row: any) => {
        event.stopPropagation();
        setEnvDetails(row);
        setShowDeletePopup(true)
    }
    const handleEditClick = (event: any, row: any) => {
        event.stopPropagation()
        router.push(`/environment/${row.id}`)
    }

    const deleteEnv = async (envDetails: EnvironmentList | undefined) => {
        setShowDeletePopup(false)
        try {
            //showLoader(true)
            const response = await axios.delete(`${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables/${envDetails?.id}`);
            let filterGroupList = environmentList?.filter((item, index) => item.id !== envDetails?.id)
            setEnvironmentList(filterGroupList)

        } catch (err) {
            console.error('Error:', err);
        } finally {
            //showLoader(false)
        }

    }




    const columns: DrCodeTableColumn<EnvironmentList>[] = [
        {
            displayName: 'Variable', accessor: 'name',

        },
        {
            displayName: 'Values', accessor: 'description',

            dataRender: (row: EnvironmentList) => {
                return (
                    <div className='grid grid-cols-2 gap-2'>
                        <div className='title--medium'>Key</div>
                        <div className='title--medium'>Value</div>

                        <div className='break-all' >
                            {Object.keys(row.env).map((key) => (
                                <div className='primary--border text-[11px] p-1' key={key}>{key}</div>
                            ))}
                        </div>
                        <div className='break-all' >
                            {Object.values(row.env).map((value) => (
                                <div className='primary--border text-[11px] p-1' key={value}>{value}</div>
                            ))}
                        </div>
                    </div >
                )
            }
        },

        {
            displayName: 'Actions',
            accessor: 'actions',
            dataRender: (row: EnvironmentList) => {
                return (
                    <div className='flex gap-2'>
                        <ButtonOutlinedPrimary text='Delete' size='small' onClick={(e) => { handleDeleteClick(e, row) }} />
                        <ButtonOutlinedPrimary text='Edit' size='small' onClick={(e) => { handleEditClick(e, row); }} />
                    </div>
                )
            }
        }
    ];
    const getEnvironmentList = async () => {
        try {
            //showLoader(true)
            const response = await axios.get(`${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables/user/${userData.user_id}`);
            setEnvironmentList(response.data)

        } catch (err) {
            console.error('Error:', err);
        } finally {
            //showLoader(false)
        }
    }

    useEffect(() => {
        getEnvironmentList()
    }, [])
    return (
        <div className='flex justify-center flex-col items-center p-8'>
            <div className='flex items-center justify-between w-full'>
                <Link href={'/project'} className='flex gap-2 cursor-pointer'>
                    <Image src={'/arrow-left.png'} quality={100} alt='back' width={20} height={20} />
                    <p>Back</p>
                </Link>
                <div className='flex justify-end  w-full'><Link href={'/environment/add'} className='button--outlined-primary button--small'>+ Add Environment</Link></div>
            </div>
            <p className='text-xl my-4'>Environments</p>
            <DrCodeTable className='w-1/2' columns={columns} data={environmentList} />
            {environmentList.length <= 0 &&
                <div className='flex justify-center items-center flex-col mt-14'>
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#875BF8"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        width="180px"
                        height="180px"
                    >
                        <circle cx="12" cy="12" r="3" />
                        <circle cx="6" cy="6" r="2" />
                        <circle cx="18" cy="6" r="2" />
                        <circle cx="6" cy="18" r="2" />
                        <circle cx="18" cy="18" r="2" />
                        <line x1="12" y1="9" x2="12" y2="6" />
                        <line x1="12" y1="15" x2="12" y2="18" />
                        <line x1="9" y1="12" x2="6" y2="12" />
                        <line x1="15" y1="12" x2="18" y2="12" />
                        <line x1="21" y1="12" x2="23" y2="12" />
                        <line x1="22" y1="11" x2="22" y2="13" />
                    </svg>
                    <p className='title--large'> No Environment Found </p>
                    <p className='mt-2'>You haven’t added any environment yet.</p>
                </div>
            }

            {(showDeletePopup) && (

                <div className='pop-up'>
                    <div className='p-4 relative'>
                        <p className='title--large text-center'>Are you sure you want to delete ? </p>
                        <div className='grid grid-cols-2 gap-4 mt-8'>
                            <button onClick={() => deleteEnv(envDetails)} className='button--delete button--small mt-4'>Yes</button>
                            <button onClick={() => setShowDeletePopup(false)} className='button--outlined-primary button--small mt-4'>No</button>
                        </div>
                    </div>
                </div>
            )}


        </div>
    )
}

