"use client";
import {
  LOCAL_STORAGE_DATA_KEYS,
  UserData,
} from "@/temp-utils/Constants/localStorageDataModels";
import { getDataFromLocalStorage } from "@/temp-utils/globalUtilities";
import { redirect, useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import axios from "axios";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";

const ApiAuditReportPage = () => {
  const [loadingStep, setLoadingStep] = useState<
    "LOADING" | "FETCHING PROJECT"
  >("LOADING");

  const [userData, setUserData] = useState<UserData | null>(null);
  const [checkingAuth, setCheckingAuth] = useState(true);

  const router = useRouter();

  useEffect(() => {
    const data = getDataFromLocalStorage(
      LOCAL_STORAGE_DATA_KEYS.USER_DATA
    ) as UserData;
    if (!data?.user_id) {
      redirect("/login?redirect-page=audit");
    } else {
      setUserData(data);
      setCheckingAuth(false);
    }
  }, []);

  const createEnvironmentVariables = async (data: any) => {
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables`,
        data
      );
      return response;
    } catch (error) {
      console.error("Error in creating environment variables:", error);
    }
  };

  const createGroup = async (projectId: number) => {
    const response = await createEnvironmentVariables({
      name: "Default Environment",
      env: {},
      user_id: userData!.user_id,
    });
    let environmentVariable = response?.data.id;

    const payload = {
      groupName: "Default Folder",
      projectId: projectId,
      description: "This is a Default Folder",
      env: environmentVariable,
      env_id: environmentVariable,
    };

    try {
      await axios.post(`${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/groups`, payload);
    } catch (error) {
      console.error(error);
    }
  };

  const createProject = async () => {
    const payload = {
      projectName: "Default Project",
      userId: userData!.user_id,
    };
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/projects`,
        payload
      );
      return response.data.data.id;
    } catch (err) {
      console.error("Error:", err);
    }
  };

  const fetchUserProjects = async () => {
    setLoadingStep("FETCHING PROJECT");
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/projects/user/${userData!.user_id}`
      );
      if (response.data.success) {
        let responseData = response.data.data;
        if (responseData.length === 0) {
          const projectId = await createProject();
          await createGroup(projectId);
          router.push(`/project/${projectId}/api-audit`);
        } else {
          router.push(`/project/${responseData[0].id}/api-audit`);
        }
      }
    } catch (err) {
      console.error("Error:", err);
    }
  };

  useEffect(() => {
    if (userData?.user_id) {
      fetchUserProjects();
    }
  }, [userData]);

  if (checkingAuth) {
    return (
      <div className="h-screen flex justify-center items-center">
        Checking login info...
      </div>
    );
  }

  return (
    <div>
      {loadingStep === "FETCHING PROJECT" && (
        <div className="h-screen flex justify-center items-center">
         Please wait, Redirecting you to audit page...
        </div>
      )}
    </div>
  );
};

export default ApiAuditReportPage;
