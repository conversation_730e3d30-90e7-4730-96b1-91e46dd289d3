import TopLoader from "@/components/common/top-loader/TopLoader";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "API Audit Report",
  description: "Dr Code: Experience AI-powered automation API Testing Platform",
  icons: {
    icon: "/logo.png",
    apple: "/logo.png",
  },
  manifest: "/manifest.json",
};

export default function ApiAuditReportLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  return (
    <div className="max-h-[100vh] overflow-hidden">
      <TopLoader />
      {children}
    </div>
  );
}
