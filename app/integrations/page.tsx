"use client";
import PrimaryButton from "@/components/common/buttons/PrimaryButton";
import PrimaryButtonOutlined from "@/components/common/buttons/PrimaryButtonOutlined";
import IntegrationCard from "@/components/integrations/IntegrationCard";
import React from "react";
import useIntegrations from "./hooks/useIntegrations";

const INTEGRATIONS = [
  {
    name: "JIRA",
    description: "Effortlessly create JIRA issues from DrCode.",
    available: true,
    id: 1,
  },
  {
    name: "Slack",
    description: "Effortlessly create Slack issues from DrCode.",
    available: false,
    id: 2,
  },
  {
    name: "<PERSON>rello",
    description:
      "Create Trello cards for better task visualization from DrCode.",
    available: false,
    id: 3,
  },
];

const IntegrationsPage = () => {
  const { handleJiraAuth } = useIntegrations();
  const handleConnect = (integrationId: number) => {
    switch (integrationId) {
      case 1:
        handleJiraAuth();
        break;
      default:
        break;
    }
  };
  const handleDisconnect = (integrationId: number) => {
    switch (integrationId) {
      case 1:
        break;
      default:
        break;
    }
  };
  return (
    <div className="p-6">
      <div className="flex h-[calc(100vh-56px)] w-full gap-6 justify-center flex-wrap">
        {INTEGRATIONS.map((integration) => (
          <IntegrationCard
            integration={integration}
            handleConnect={handleConnect}
            handleDisconnect={handleDisconnect}
          />
        ))}
      </div>
    </div>
  );
};

export default IntegrationsPage;
