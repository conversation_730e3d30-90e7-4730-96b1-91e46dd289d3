import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import {
  LOCAL_STORAGE_DATA_KEYS,
  UserData,
} from "@/temp-utils/Constants/localStorageDataModels";
import { getDataFromLocalStorage } from "@/temp-utils/globalUtilities";
import axios from "axios";
import React from "react";

const useIntegrations = () => {
  const userData: UserData = getDataFromLocalStorage(
    LOCAL_STORAGE_DATA_KEYS.USER_DATA
  ) as UserData;

  const handleJiraAuth = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.AUTH_JIRA}?userId=${userData.user_id}`
      );

      if (response.data.success) {
        console.log("log:: data ", response.data.data.authUrl);
        window.location.href = response.data.data.authUrl;
      }
    } catch (error) {
      console.log(error);
    }
  };

  const fetchJiraProjects = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GET_JIRA_PROJECTS}?userId=${userData.user_id}`
      );

      return response.data.data;
    } catch (error) {
      console.log(error);
      throw error;
    }
  };

  const setJiraProject = async (projectId: string | number) => {
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.SET_SELECTED_PROJECT}?userId=${userData.user_id}`,
        {
          projectId,
        }
      );

      return response.data.data;
    } catch (error) {
      console.log(error);
      throw error;
    }
  };

  const handleJiraConnect = () => {};

  const handleJiraDisconnect = () => {};

  const handleSlackConnect = () => {};

  const handleSlackDisconnect = () => {};

  const handleTrelloConnect = () => {};

  const handleTrelloDisconnect = () => {};

  return {
    handleJiraConnect,
    handleJiraDisconnect,
    handleSlackConnect,
    handleSlackDisconnect,
    handleTrelloConnect,
    handleTrelloDisconnect,
    handleJiraAuth,
    fetchJiraProjects,
    setJiraProject,
  };
};

export default useIntegrations;
