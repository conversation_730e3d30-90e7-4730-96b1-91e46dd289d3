"use client";
import { LOCAL_STORAGE_DATA_KEYS } from "@/temp-utils/Constants/localStorageDataModels";
import { removeDataFromLocalStorage } from "@/temp-utils/globalUtilities";
import { useRouter } from "next/navigation";
import React from "react";
import { CiLogout } from "react-icons/ci";

const LogoutButton = () => {
  const router = useRouter();
  const handleLogoutClick = () => {
    removeDataFromLocalStorage(LOCAL_STORAGE_DATA_KEYS.ACCESS_TOKEN);
    removeDataFromLocalStorage(LOCAL_STORAGE_DATA_KEYS.SESSION_ID);
    removeDataFromLocalStorage(LOCAL_STORAGE_DATA_KEYS.USER_DATA);
    removeDataFromLocalStorage(LOCAL_STORAGE_DATA_KEYS.SELECTED_ORG);
    router.push("/");
  };
  return (
    <div onClick={handleLogoutClick} className="flex gap-2 items-center cursor-pointer">
      <CiLogout /> 
      {/* <p className="text-[12px]">Logout</p> */}
    </div>
  );
};

export default LogoutButton;
