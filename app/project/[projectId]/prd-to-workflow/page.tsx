"use client";
import React, { useState } from "react";
import PrimaryInput from "@/components/common/primary-input/PrimaryInput";
import FileUploader from "@/components/common/file-uploader/FileUploader";
import PrimaryButtonOutlined from "@/components/common/buttons/PrimaryButtonOutlined";
import PrimaryTextarea from "@/components/common/primary-textarea/PrimaryTextarea";
import { useParams, useRouter } from "next/navigation";
import axios, { AxiosResponse } from "axios";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import TestCaseLoader from "@/components/common/test-case-loader/TestCaseLoader";
import BottomSheet from "@/components/common/right-bottom-sheet/RightBottomSheet";
import WorkflowExplorer from "@/components/common/data-flow/WorkflowExplorer";
import { FaBars } from "react-icons/fa";

const LOADING_STEPS = [
  { id: 1, title: "Analyzing PRD" },
  { id: 2, title: "Generating workflow" },
  { id: 3, title: "Validating across scenarios" },
  { id: 4, title: "Finalizing workflow" },
];

const PrdToWorkflow = () => {
  const [loading, setLoading] = useState(false);
  const [workflowName, setWorkflowName] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [prdText, setPrdText] = useState("");
  const [useText, setUseText] = useState(false);
  const { projectId } = useParams();
  const router = useRouter();

  const [isCollapsed, setIsCollapsed] = useState(false);

  const handleCollapse = () => {
    setIsCollapsed((prev) => !prev);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!workflowName || (!file && !prdText)) {
      alert("Please provide both workflow name and PRD document or text.");
      return;
    }

    setLoading(true);
    try {
      let requestBody = {
        prdText: prdText,
        projectId: projectId,
        workflowName: workflowName,
      };

      let response: AxiosResponse;

      if (useText) {
        response = await axios.post(
          `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.CREATE_WORKFLOW_WITH_PRD_TEXT}`,
          requestBody
        );
      } else {
        if (file) {
          const formData = new FormData();
          formData.append("file", file);
          formData.append("workflowName", workflowName);
          formData.append("projectId", projectId as string);

          response = await axios.post(
            `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.CREATE_WORKFLOW_WITH_PRD_FILE}`,
            formData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
              },
            }
          );
        }
      }

      console.log("response is ", response.data);
      if (response.data.workflow) {
        router.push(
          `/project/${projectId}/prd-to-workflow/${response.data.workflow.id}`
        );
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  return (
    <>
      <BottomSheet
        isOpen={isCollapsed}
        onClose={handleCollapse}
        side="left"
        widthInPercentage="30%"
      >
        <WorkflowExplorer handleCollapse={handleCollapse} />
      </BottomSheet>
      <div className={`py-4 px-8 pr-0 flex gap-2`}>
        <div className="absolute top-[2rem] left-2 z-10">
          <FaBars onClick={handleCollapse} className="cursor-pointer" />
        </div>
        <div className="p-4 flex flex-col items-center justify-center w-screen h-screen">
          {loading ? (
            <TestCaseLoader steps={LOADING_STEPS} />
          ) : (
            <>
              <h4 className="text-[24px] text-[#D1D1E3] font-semibold leading-[120%] tracking-[-2%] mb-4">
                Create Workflow
              </h4>
              <div
                onSubmit={handleSubmit}
                className="space-y-4 max-w-md min-w-[450px]"
              >
                <PrimaryInput
                  border="primary--border"
                  placeholder="Workflow Name"
                  value={workflowName}
                  onChange={(e) => setWorkflowName(e.target.value)}
                  autoComplete="true"
                />
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="prdInputType"
                      id="uploadPRD"
                      checked={!useText}
                      onChange={() => setUseText(false)}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2"
                    />
                    <label
                      htmlFor="uploadPRD"
                      className="text-[#D1D1E3] font-medium cursor-pointer"
                    >
                      Upload PRD
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="prdInputType"
                      id="enterText"
                      checked={useText}
                      onChange={() => setUseText(true)}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2"
                    />
                    <label
                      htmlFor="enterText"
                      className="text-[#D1D1E3] font-medium cursor-pointer"
                    >
                      Enter PRD as Text
                    </label>
                  </div>
                </div>
                {!useText ? (
                  <FileUploader
                    handleData={(selectedFile) => setFile(selectedFile)}
                    label="Upload PRD to create workflow"
                    acceptedFileTypes={[".pdf", ".docx", ".txt"]}
                  />
                ) : (
                  <PrimaryTextarea
                    placeholder="Enter PRD text here"
                    value={prdText}
                    onChange={(e) => setPrdText(e.target.value)}
                    style={{
                      height: "300px",
                    }}
                  />
                )}
                <PrimaryButtonOutlined label="Submit" onClick={handleSubmit} />
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default PrdToWorkflow;
