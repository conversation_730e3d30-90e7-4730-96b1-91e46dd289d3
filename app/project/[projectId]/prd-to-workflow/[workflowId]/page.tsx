"use client";
import { useParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import { JourneyData } from "../types";
import DataFlow from "@/components/common/data-flow/Dataflow";
import axios from "axios";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import LoaderGif from "@/components/common/loader-gif/LoaderGif";
import useToast from "@/hooks/useToast";
import { TestsExplorer } from "@/components/test-runner/tests-explorer/TestsExplorer";
import BottomSheet from "@/components/common/right-bottom-sheet/RightBottomSheet";
import WorkflowExplorer from "@/components/common/data-flow/WorkflowExplorer";
import { FaBars } from "react-icons/fa";

const WorkFlow = () => {
  const [workflowData, setWorkflowData] = React.useState<{
    flow: JourneyData;
    id: number;
    workflow_name: string;
  }>(null);
  const [loading, setLoading] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { workflowId } = useParams();
  const { showToast } = useToast();

  const handleCollapse = () => {
    setIsCollapsed((prev) => !prev);
  };

  const fetchWorkflowData = async () => {
    setLoading(true);
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GET_WORKFLOW}/${workflowId}`
      );

      setWorkflowData({
        ...response.data,
        flow: { journey: response.data.flow },
      });
      setLoading(false);
    } catch (error) {
      showToast("Error fetching workflow data", "error");
      console.error("Error fetching workflow data:", error);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!workflowId) return;
    fetchWorkflowData();
  }, [workflowId]);

  if (loading) {
    return (
      <div className="h-screen w-screen flex items-center justify-center">
        {" "}
        <LoaderGif />
      </div>
    );
  }

  return (
    <>
      <BottomSheet
        isOpen={isCollapsed}
        onClose={handleCollapse}
        side="left"
        widthInPercentage="30%"
      >
        <WorkflowExplorer handleCollapse={handleCollapse} />
      </BottomSheet>
      <div className={`py-4 px-8 pr-0 flex gap-2`}>
        <div className="absolute top-[2rem] left-2 z-10">
          <FaBars onClick={handleCollapse} className="cursor-pointer" />
        </div>
        <div className="p-4 w-full">
          <h4 className="text-[24px] text-[#D1D1E3] font-semibold leading-[120%] tracking-[-2%] mb-4 capitalize">
            {workflowData?.workflow_name || "Workflow"}
          </h4>
          {workflowData?.flow && (
            <DataFlow
              data={workflowData?.flow}
              onUpdate={(updatedData) => {
                console.log("Updated Data:", updatedData);
              }}
            />
          )}
        </div>
      </div>
    </>
  );
};

export default WorkFlow;
