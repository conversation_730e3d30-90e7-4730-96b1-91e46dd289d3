import TopLoader from "@/components/common/top-loader/TopLoader";
import type { Metadata } from "next";
import Head from "next/head";

export const metadata: Metadata = {
  title: "PRD to Workflow",
  description: "Dr Code: Experience AI-powered automation API Testing Platform",
  icons: {
    icon: "/logo.png",
    apple: "/logo.png",
  },
  manifest: "/manifest.json",
};

export default function PrdToWorkflowLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: any;
}>) {
  return (
    <>
      <Head>
        <title>PRD to Workflow</title>
        <meta
          name="description"
          content="Dr Code: Experience AI-powered automation API Testing Platform"
        />
        <link rel="icon" href="/logo.png" />
        <link rel="apple-touch-icon" href="/logo.png" />
        <link rel="manifest" href="/manifest.json" />
      </Head>
      <TopLoader />
      {children}
    </>
  );
}
