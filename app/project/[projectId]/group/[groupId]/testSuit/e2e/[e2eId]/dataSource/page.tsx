import React from "react";

import DataSourceComponent from "@/components/data-source/DataSource";

const DataSource = () => {
  return (
    <>
      <div className="py-5 px-10 overflow-x-auto h-screen w-full">
        <div className="text-[#D1D1E3] text-[24px] font-bold mb-1">
          Review Uploaded Data
        </div>
        <div className="text-[#9494A1] text-[14px] font-normal mb-2">
          Here's a preview of your uploaded CSV. Make any necessary changes
          before testing.
        </div>
        <DataSourceComponent />
      </div>
    </>
  );
};

export default DataSource;
