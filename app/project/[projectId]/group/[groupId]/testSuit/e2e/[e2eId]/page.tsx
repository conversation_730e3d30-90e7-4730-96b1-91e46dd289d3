"use client";
import DrCodeTable from "@/components/common/drcode-table/DrCodeTable";
import { DrCodeTableColumn } from "@/components/common/drcode-table/DrCodeTableTypes";
import FloatingDropdown from "@/components/common/floating-dropdown/FloatingDropdown";
import Modal from "@/components/common/modal/Modal";
import TestCaseLoader from "@/components/common/test-case-loader/TestCaseLoader";
import ApiTestDetailedView from "@/components/test-runner/api-test-detailed-view/ApiTestDetailedView";
import EnvList from "@/components/test-runner/env-list/EnvList";
import RightNavigation, {
  NavigationItem,
} from "@/components/test-runner/right-navigation/RightNavigation";
import useToast from "@/hooks/useToast";
import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import { useGlobalStore } from "@/stores/globalstore";
import { getAssertions } from "@/temp-utils/assertionsUtilities";
import {
  NEXT_PUBLIC_DR_CODE_AUTH_BASE_API_URL,
  NEXT_PUBLIC_DR_CODE_BASE_API_URL,
} from "@/temp-utils/Constants/globalVariables";
import axios from "axios";
import { useParams, usePathname, useRouter } from "next/navigation";
import React, { JSX, useMemo, useState } from "react";
import { FaBars, FaPlayCircle, FaSpinner } from "react-icons/fa";
import { FaDeleteLeft } from "react-icons/fa6";
import { MdDeleteOutline } from "react-icons/md";
import { RxCheck, RxGear } from "react-icons/rx";
import FileMappingModal from "../../[testSuitId]/file-mapping-model/FileMappingModal";
import { PiPencilLine, PiPlugsConnectedFill } from "react-icons/pi";
import useDataSource from "@/components/data-source/useDataSource";
import { TestAssertionEntity } from "@/components/test-runner/api-test-detailed-view/blocks/assertion-results/AssertionResults";
import { assert, expect } from "chai";
import ToggleSwitch from "@/components/common/toggle-switch/ToggleSwitch";
import DataSourcePopup from "@/components/data-source/DataSourcePopup";
import { IoMdInformationCircleOutline } from "react-icons/io";
import { Tooltip } from "@/components/common/tooltip/Tooltip";
import BottomSheet from "@/components/common/right-bottom-sheet/RightBottomSheet";
import PrimaryInput from "@/components/common/primary-input/PrimaryInput";
import PrimaryButton from "@/components/common/buttons/PrimaryButton";
import { RiResetLeftFill } from "react-icons/ri";
import { TestsExplorer } from "@/components/test-runner/tests-explorer/TestsExplorer";
import { CiTrash } from "react-icons/ci";
import Image from "next/image";

export interface ResponseBody {
  [key: string]: any;
}

// Define the structure of the headers
export interface ResponseHeaders {
  [key: string]: string;
}

export interface TestResponse {
  response: ResponseBody | string | any;
  headers: ResponseHeaders;
  statusCode: number | null;
  error: string | null;
  elapsedTime: string | null;
}

export interface AssertionResult {
  passed: boolean;
  message: string;
}

export interface E2EScenario {
  id: number;
  e2e_id: string;
  name: string;
  description: string;
  main_flow: string;
  steps: E2EStep[];
  group_id?: number;
  is_datasource?: boolean;
  status?: string;
  api_sequence_t: string;
}
export interface E2EStep {
  id: number;
  scenario_id: number;
  step_number: number;
  name: string;
  description: string;
  method: string;
  url: string;
  request: {
    headers?: Record<string, string>;
    json_body?: Record<string, any>;
    path_params?: Record<string, any>;
    query_params?: Record<string, any>;
  };
  expected_response?: string;
  tags?: string[];
  data_mapping?: any;

  // UI/logic fields
  open?: boolean;
  checkbox?: boolean;
  statusCode?: number;
  response?: TestResponse;

  // Assertion fields
  assertions?: string | null;
  assertions_results: AssertionResult[];
  status: "PASSED" | "FAILED" | "WARNING";
}

const E2EView = () => {
  const { e2eId, groupId }: { e2eId: string; groupId: string } = useParams();
  const [e2eData, setE2eData] = React.useState<any>(null);
  const [isPending, setIsPending] = React.useState(false);
  const [scenerios, setScenerios] = React.useState<E2EScenario[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [envModal, setEnvModal] = React.useState(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [selectedFilter, setSelectedFilter] = useState<string>("All");
  const [envVarsFromPreviousStep, setEnvVarsFromPreviousStep] = useState<Record<
    string,
    any
  > | null>(null);
  const [realTestDataModal, setRealTestDataModal] = useState<boolean>(false);

  const { setEnvironment, environment, triggerRevalidateTestsExplorer } =
    useGlobalStore();

  const intervalIdRef = React.useRef<NodeJS.Timeout | null>(null);

  const { showToast } = useToast();
  const { handleConnectWithDataSource, checkDataSourceAvailability } =
    useDataSource();
  const router = useRouter();
  const pathname = usePathname();

    const [isCollapsed, setIsCollapsed] = useState(false);
  
    const handleCollapse = () => {
      setIsCollapsed((prev) => !prev);
    };

  const [realTestData, setRealTestData] = useState<any>(null);
  const [mappingStep, setMappingStep] = useState<number>(0);
  const [uniqueHeaders, setUniqueHeaders] = useState<string[]>([]);
  const [currentScenerio, setCurrentScenerio] = useState<E2EScenario | null>(
    null
  );

  const [selectedEnvOptions, setSelectedEnvOptions] = useState<
    { value: string; label: string }[]
  >([]);

  const [isConnected, setIsConnected] = useState(false);
  const [showDataSourceWarningModal, setShowDataSourceWarningModal] =
    useState(false);

  const [chooseDataSourceModal, setChooseDataSourceModal] = useState(false);

  const [runTimeSettingsModal, setRunTimeSettingsModal] = useState(false);
  const [waitTime, setWaitTime] = useState(10000);

  const [isEditing, setIsEditing] = useState(false);

  const settingsOptionsList = [
    {
      name: "Edit Environment",
      id: 1,
      onClick: () => {
        // router.push(`/environment/${environment}`);
        setEnvModal(true);
      },
    },
    {
      name: "Edit Run time Settings",
      id: 2,
      onClick: () => setRunTimeSettingsModal(true),
    },
  ];

  function flattenStepTypes(step: any) {
    // store step in array in localStorage and do not add duplicate
    const stepsArray = localStorage.getItem(`${e2eId}_stepsArray`);
    const steps = stepsArray ? JSON.parse(stepsArray ?? "[]") : [];
    if (!steps.some((s: any) => s.step_number === step.step_number)) {
      steps.push(step);
    }
    localStorage.setItem(`${e2eId}_stepsArray`, JSON.stringify(steps));
    const result = {};
    const stepPrefix = `step${step.step_number}`;

    for (const [section, def] of Object.entries(step.types) as any) {
      if (def.type === "object" && def.properties) {
        for (const [key, prop] of Object.entries(def.properties) as any) {
          result[`${stepPrefix}_${section}_${key}`] = prop.type;
        }
      }
    }

    return result;
  }

  const getE2EDetailsByGroupId = async () => {
    if (!groupId) return;
    setLoading(true);
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GET_E2E_BY_GROUP_ID}/${groupId}`
      );

      if (response.data?.data?.[0]?.status === "pending") {
        setIsPending(true);
      } else {
        setIsPending(false);
        intervalIdRef.current && clearInterval(intervalIdRef.current);
        intervalIdRef.current = null;
        // triggerRevalidateTestsExplorer();
      }

      // console.log({
      //   response: response.data.data?.[0]?.steps.map((step) => {
      //     flattenStepTypes(step);
      //   }),
      // });

      setEnvironment(response.data.data[0]?.env_id);
      setE2eData(response.data.data[0]);
      if (response.data.data[0]?.connect) {
        setIsConnected(true);
      }

      const filteredTestCases = response.data?.data[0].scenarios;

      const newTestCases = filteredTestCases.map((testCase: any) => {
        if (testCase.steps && Array.isArray(testCase.steps)) {
          const newSteps = testCase.steps.map((step: E2EStep) => ({
            ...step,
            request: { ...step.request, url: step?.url },
            checkbox: false,
            open: false,
            statusCode: step.response?.statusCode || 0,
          }));
          return { ...testCase, steps: newSteps };
        } else {
          // If no steps exist, add properties at the test case level.
          return { ...testCase, checkbox: false, open: false, expanded: false };
        }
      });

      setScenerios(newTestCases);
      try {
        const typesArray = response.data.data?.[0]?.steps.map((step) =>
          flattenStepTypes(step)
        );

        localStorage.setItem(`${e2eId}_typesArray`, JSON.stringify(typesArray));
      } catch (e) {
        console.log("error ", e);
      }

      return newTestCases;
    } catch (error) {
      console.log("Error fetching E2E details:", error.message);
    } finally {
      setLoading(false);
    }
  };

  const checkForDataAndRun = async (scenario: any) => {
    let response;
    setCurrentScenerio(scenario);
    if (!scenario.is_datasource) {
      setChooseDataSourceModal(true);
      return;
    }
    if (isConnected) {
      response = await connectWithDataSource();
      if (!response.success) {
        setShowDataSourceWarningModal(true);
        return;
      }
      // await getE2EDetailsByGroupId();
    }
    handleRunScenario(
      response?.data?.[0] ?? scenario,
      response?.data ?? scenerios
    );
  };

  const checkDataSourceStatus = async () => {
    const response = await checkDataSourceAvailability();
  };

  const handleRunScenario = async (scenario: any, scenerios) => {
    await checkDataSourceStatus();
    let newScenarios = [...scenerios];

    const stepsToRun = scenario.steps;

    const updatedScenarioIndex = scenerios.findIndex(
      (sc) => sc.id === scenario.id
    );
    if (updatedScenarioIndex !== -1) {
      const scenarioCopy = { ...scenario };
      scenarioCopy.steps = scenarioCopy.steps.map((st: E2EStep) => ({
        ...st,
        assertions_results: [{ passed: false, message: "Processing..." }],
      }));

      // Update state right away so the UI shows "Processing..."
      newScenarios[updatedScenarioIndex] = scenarioCopy;
      setScenerios(newScenarios);
    }
    if (updatedScenarioIndex !== -1) {
      const scenarioCopy: any = { ...scenario };
      scenarioCopy.steps = scenarioCopy.steps.map((st: any) => ({
        ...st,
        statusCode: "LOADING",
        assertions_results: [{ passed: false, message: "Processing..." }],
      }));

      const newScenarios = [...scenerios];
      newScenarios[updatedScenarioIndex] = scenarioCopy;
      setScenerios(newScenarios);
    }

    let envVarsFromPreviousStep: any = null;

    for (let i = 0; i < stepsToRun.length; i++) {
      const step = stepsToRun[i];
      const updatedEnvVars = await runScenerioTest(
        e2eId,
        step.step_number,
        scenario.id,
        envVarsFromPreviousStep,
        newScenarios
      );

      envVarsFromPreviousStep = updatedEnvVars;
      await runScenerioAssertions(e2eId, step.step_number, scenario.id);
    }
  };

  const runScenerioTest = async (
    e2eId: string,
    stepNumber: number,
    scenarioId: number,
    envVarsFromPreviousStep: any, // <-- add this parameter
    scenerios
  ) => {
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GET_E2E_TEST_RESPONSE}`,
        {
          e2eId: e2eId,
          waitTime: waitTime,
          step: stepNumber,
          envVarsFromPreviousStep: envVarsFromPreviousStep,
        }
      );

      const newEnvVars = response.data.data?.data?.envVars;

      const scenerio = scenerios.find((sc: any) => sc.id === scenarioId);
      const currentStep: E2EStep = scenerio?.steps.find(
        (st: any) => st.step_number === stepNumber
      );

      currentStep.assertions_results = [
        { passed: "processing" as any, message: "Processing..." },
      ];

      setScenerios((prevScenarios) =>
        prevScenarios.map((sc) => {
          if (sc.id !== scenarioId) return sc;
          const newSteps = sc.steps.map((s: any) =>
            s.id === currentStep.id ? { ...s, ...currentStep } : s
          );
          return { ...sc, steps: newSteps };
        })
      );

      const responseBody = response?.data?.data?.data?.step?.response;
      setScenerios((prev) =>
        prev.map((sc) => {
          if (sc.id !== scenarioId) return sc;
          const updatedSteps: E2EStep[] = sc.steps.map((st: E2EStep) =>
            st.step_number === stepNumber
              ? {
                  ...st,
                  response: responseBody,
                  statusCode: responseBody?.statusCode,
                }
              : st
          );

          return { ...sc, steps: updatedSteps };
        })
      );

      return newEnvVars; // <-- return updated envVars
    } catch (error) {}
  };

  const runScenerioAssertions = async (
    e2eId: string,
    stepNumber: number,
    scenarioId: number
  ) => {
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GET_E2E_TEST_ASSERTIONS}`,
        {
          e2eId: e2eId,
          waitTime: waitTime,
          step: stepNumber,
        }
      );

      const assertionsResults = response.data?.data?.assertions_results;
      const assertions = response.data?.data?.assertions;
      setScenerios((prev) =>
        prev.map((sc) => {
          if (sc.id !== scenarioId) return sc;
          const updatedSteps: E2EStep[] = sc.steps.map((st: E2EStep) =>
            st.step_number === stepNumber
              ? {
                  ...st,
                  assertions_results: assertionsResults,
                  assertions: assertions,
                  status: response.data.data.status,
                }
              : st
          );
          return { ...sc, steps: updatedSteps };
        })
      );
    } catch (error) {}
  };

  const handleDeleteScenario = async (scenarioId: any) => {
    try {
      const response = await axios.delete(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.DELETE_E2E_SCENARIO}/${scenarioId}`
      );
      if (response.data?.success) {
        setScenerios((prev) => prev.filter((sc: any) => sc.id !== scenarioId));
        showToast("Scenario deleted successfully", "success");
      } else {
        console.error("Failed to delete scenario:", response.data?.message);
        showToast("Failed to delete scenario", "error");
      }
    } catch (error) {
      console.error("Error deleting scenario:", error.message);
      showToast("Error deleting scenario", "error");
    }
  };

  const handleToggleStepOpen = (scenarioId: number, stepId: number) => {
    setScenerios((prev) =>
      prev.map((sc) => {
        if (sc.id !== scenarioId) return sc;
        const updatedSteps = sc.steps.map((st: any) =>
          st.id === stepId
            ? { ...st, open: !st.open, expanded: !st.expanded }
            : st
        );
        return { ...sc, steps: updatedSteps };
      })
    );
  };

  const handleSaveRequest = async (testSuite: E2EStep, status?: string) => {
    console.log("log:: testSuite", testSuite);
    try {
      const response = await axios.put(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.UPDATE_E2E_BY_E2E_STEP_ID}/${testSuite.id}`,
        {
          request: JSON.parse(testSuite.request as string),
          url: testSuite.url,
        }
      );

      if (response.data.success) {
        showToast("Request saved successfully", "success");
        // setScenerios((prev) =>
        //   prev.map((sc) => {
        //     if (sc.id !== testSuite.scenario_id) return sc;
        //     const updatedSteps = sc.steps.map((st: E2EStep) =>
        //       st.id === testSuite.id ? { ...st, request: testSuite.request } : st
        //     );
        //     return { ...sc, steps: updatedSteps };
        //   })
        // );
      }
    } catch (error) {}
  };

  const modifyRequestBodyRow = (
    rowIndex: number,
    body: string, // stringified request object
    step: E2EStep
  ) => {
    console.log("log:: body", body, step, rowIndex);

    let parsedBody;
    try {
      parsedBody = JSON.parse(body); // This is your full `request` object
    } catch (err) {
      console.error("Invalid JSON in request body", err);
      return;
    }

    setScenerios((prevScenarios) =>
      prevScenarios.map((scenario) => {
        if (scenario.id === step.scenario_id) {
          const updatedSteps = scenario.steps.map((s) => {
            if (s.id === step.id) {
              const updatedData = parsedBody.url
                ? { ...s, request: parsedBody, url: parsedBody.url }
                : { ...s, request: parsedBody };
              console.log("log:: updated data is ", updatedData);
              return updatedData;
            }
            return s;
          });

          return { ...scenario, steps: updatedSteps };
        }
        return scenario;
      })
    );
  };

  const modifyAssertionRow = (
    rowIndex: number,
    body: string,
    step: E2EStep
  ) => {
    console.log("log:: body", body, step, rowIndex);

    setScenerios((prevScenarios) =>
      prevScenarios.map((scenario) => {
        if (scenario.id === step.scenario_id) {
          const updatedSteps = scenario.steps.map((s) => {
            if (s.id === step.id) {
              return {
                ...s,
                assertions: body,
                assertions_results: step.assertions_results || [],
              };
            }
            return s;
          });

          return { ...scenario, steps: updatedSteps };
        }
        return scenario;
      })
    );
  };

  const modifyAssertionResultsRow = async (
    result: TestAssertionEntity[],
    step: E2EStep
  ) => {
    setScenerios((prevScenarios) =>
      prevScenarios.map((scenario) => {
        if (scenario.id === step.scenario_id) {
          const updatedSteps = scenario.steps.map((s) => {
            if (s.id === step.id) {
              return {
                ...s,
                assertions_results: structuredClone(result),
                running: false,
              };
            }
            return s;
          });

          return { ...scenario, steps: updatedSteps };
        }
        return scenario;
      })
    );

    // Prepare the updated step data
    const updatedStep = {
      ...step,
      assertions_results: result,
      running: false,
    };

    console.log({ updatedStep });

    // Save to server
    try {
      const updateUrl = `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.UPDATE_E2E_BY_E2E_STEP_ID}/${updatedStep.id}`;
      await axios.put(updateUrl, {
        assertions: { assertions: updatedStep.assertions },
        assertions_results: updatedStep.assertions_results.map((item) =>
          JSON.stringify(item)
        ),
      });

      await runScenerioAssertions(e2eId, updatedStep.step_number, updatedStep.scenario_id);

    } catch (error) {
      console.error("Error updating assertions:", error);
    }
  };

  const runAssertionsLocally = async(
    rowIndex: number,
    response: any,
    row?: E2EStep
  ) => {
    console.log({ response, rowIndex, row });

    const testCaseAssertion = (
      (row.assertions as any)?.assertions?.assertions ??
      (row.assertions as any)?.assertions ??
      (row.assertions as any)
    )?.replace("```", "");

    if (!testCaseAssertion?.trim()) {
      return;
    }

    const assertionResults: TestAssertionEntity[] = [];

    try {
      // Define test helpers
      const it = (description: string, testFn: () => void) => {
        try {
          testFn();
          assertionResults.push({ message: description, passed: true });
          console.log("Test passed", { description });
        } catch (err: any) {
          console.log("Test failed", { description, error: err.message });
          assertionResults.push({
            message: description,
            passed: false,
          });
        }
      };

      const describe = (description: string, suiteFn: () => void) => {
        try {
          console.log(`Running test suite: ${description}`);
          suiteFn();
        } catch (err) {
          console.error(`Error in describe block "${description}":`, err);
        }
      };

      const before = (fn: () => void) => {};
      const after = (fn: () => void) => {};
      const beforeEach = (fn: () => void) => {};
      const afterEach = (fn: () => void) => {};

      // Execute the test script in an isolated environment
      const testRunner = new Function(
        "it",
        "expect",
        "assert",
        "response",
        "describe",
        "before",
        "after",
        "beforeEach",
        "afterEach",
        testCaseAssertion
      );

      testRunner(
        it,
        expect,
        assert,
        response,
        describe,
        before,
        after,
        beforeEach,
        afterEach
      );




    } catch (err) {
      console.error("Test execution failed:", err);
    }

    setScenerios((prev) =>
      prev.map((sc) => {
        if (sc.id !== row.scenario_id) return sc;
        const updatedSteps: E2EStep[] = sc.steps.map((st: E2EStep) =>
          st.step_number === row.step_number
            ? {
                ...st,
                assertions_results: assertionResults,
                running: false,
              }
            : st
        );

        return { ...sc, steps: updatedSteps };
      })
    );
    modifyAssertionResultsRow(assertionResults, row);
  };

  const navigationItems: NavigationItem[] = [
    // {
    //   icon: "custom_test_case.svg",
    //   title: "Custom Test Case",
    //   onClick: () => {

    //       setBusinessTestCaseModal(true);
    //   },
    // },
    {
      icon: scenerios?.[0]?.is_datasource ? "edit_csv.svg" : "csv_upload.svg",
      title: scenerios?.[0]?.is_datasource ? "Edit Data" : "Live Data Test",
      onClick: () => {
        if (scenerios?.[0]?.is_datasource) {
          router.push(`${pathname}/dataSource?isEdit=true`);
        } else {
          setRealTestDataModal(true);
          localStorage.removeItem("csvData");
          localStorage.removeItem("csvUniqueHeaders");
          localStorage.removeItem(`${e2eId}_stepsArray`);
          localStorage.removeItem(`${e2eId}_typesArray`);
          localStorage.removeItem(`${e2eId}_csvKeyTypes`);
          setUniqueHeaders([]);
          setRealTestData(null);
        }
      },
    },
  ];

  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);

  const filteredScenarios = useMemo(() => {
    const bySearch = (scenario: E2EScenario) => {
      const lower = searchQuery.toLowerCase();
      return (
        scenario.name.toLowerCase().includes(lower) ||
        scenario.description.toLowerCase().includes(lower)
      );
    };
    return selectedFilter === "All"
      ? scenerios.filter(bySearch)
      : scenerios.filter(bySearch);
  }, [scenerios, searchQuery, selectedFilter]);

  const currentScenarios = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredScenarios.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredScenarios, currentPage, itemsPerPage]);

  const connectWithDataSource = async () => {
    let newScenerios = currentScenarios;
    try {
      const response = await handleConnectWithDataSource(false);
      if (response.success) {
        newScenerios = await getE2EDetailsByGroupId();
        showToast("Connected with data source successfully.", "success");
      }

      return {
        success: response.success,
        message: response.message,
        data: newScenerios,
      };
    } catch (error) {
      console.error(
        "Error connecting with data source:",
        error?.response?.data?.message
      );
      // showToast(
      //   error?.response?.data?.message || "Failed to connect with data source.",
      //   "error"
      // );
      return {
        success: false,
        message:
          error?.response?.data?.message ||
          "Failed to connect with data source.",

        data: newScenerios,
      };
    }
  };

  const handleDataMappingChange = (
    step: E2EStep,
    key: string,
    value: string,
    changed: "key" | "value",
    rowIndex: number
  ) => {
    setScenerios((prevScenarios) =>
      prevScenarios.map((scenario) => {
        if (scenario.id === step.scenario_id) {
          const updatedSteps = scenario.steps.map((s) => {
            if (s.id === step.id) {
              const newMapping = { ...s.data_mapping };

              if (changed === "value") {
                newMapping[key] = value;
              } else if (changed === "key") {
                const existingValue = newMapping[key];
                delete newMapping[key];
                newMapping[value] = existingValue;
              }

              return {
                ...s,
                data_mapping: newMapping,
              };
            }
            return s;
          });

          return {
            ...scenario,
            steps: updatedSteps,
          };
        }

        return scenario;
      })
    );
  };

  const handleAddRow = (step: E2EStep) => {
    const updatedSteps = scenerios.map((sc) => {
      if (sc.id === step.scenario_id) {
        const newKey = ``;
        const updatedSteps = sc.steps.map((s) => {
          if (s.id === step.id) {
            return {
              ...s,
              data_mapping: {
                ...s.data_mapping,
                [newKey]: "",
              },
            };
          }
          return s;
        });
        return { ...sc, steps: updatedSteps };
      }
      return sc;
    });
    setScenerios(updatedSteps);
  };

  const handleDeleteRow = (step: E2EStep, rowIndex: number) => {
    const updatedScenarios = scenerios.map((sc) => {
      if (sc.id === step.scenario_id) {
        const updatedSteps = sc.steps.map((s) => {
          if (s.id === step.id) {
            const entries = Object.entries(s.data_mapping);
            // Remove the entry at the specified index
            entries.splice(rowIndex, 1);
            // Convert entries back to object
            const updatedMapping = Object.fromEntries(entries);

            return {
              ...s,
              data_mapping: updatedMapping,
            };
          }
          return s;
        });

        return {
          ...sc,
          steps: updatedSteps,
        };
      }
      return sc;
    });

    setScenerios(updatedScenarios);
  };

  const handleVariableMapping = async () => {
    scenerios.map((sc)=>delete sc.api_sequence_t)
    try {
      const response = await axios.put(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/e2e/e2e/${e2eId}/verify`,
        scenerios
      );

      console.log("log:: variable response ", response);
      if (response.data.success) {
        getE2EDetailsByGroupId();
      }
    } catch (error) {
      console.log("log:: error ", error);
    }
  };

  const handleUpdateE2EScenarioName = () => {
    try {
      const response = axios.put(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/e2e/${scenerios?.[0]?.id}`,
        { name: scenerios?.[0]?.name }
      );
      setIsEditing(false);
    } catch (error) {
      console.log("log:: error ", error);
    }
  };

  const handleConnectData = async (value) => {
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.TOGGLE_CONNECT_FOR_E2E}/${e2eId}`
      );

      console.log("log:: response is ", response);
      if (response?.data?.success) {
        setIsConnected(response?.data?.data?.connect);

        if (response?.data?.data?.connect) {
          showToast("Data connected successfully", "success");
        } else {
          showToast("Data disconnected successfully", "success");
        }
      }
    } catch (error) {
      console.error("Error connecting data:", error);
    }
  };

  const handleResetAssertions = async () => {
    try {
      // delete scenerios?.[0]?.api_sequence_t;
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/e2e/e2e/${e2eId}/reset-assertions`
      );

      console.log("log:: variable response ", response);
      if (response.data.success) {
        getE2EDetailsByGroupId();
      }
    } catch (error) {
      console.log("log:: error ", error);
    }
  };

  const handleDeleteStep = async (scenarioId: number, stepId: number) => {
    try {
      const response = await axios.delete(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.DELETE_E2E_STEP}/${stepId}`
      );

      console.log("log:: response is ", response);
      if (response?.data?.success) {
        getE2EDetailsByGroupId();
      }
    } catch (error) {
      console.error("Error deleting step:", error);
    }
  };

  React.useEffect(() => {
    getE2EDetailsByGroupId();

    intervalIdRef.current = setInterval(() => {
      if (!e2eData || e2eData?.status === "pending") {
        getE2EDetailsByGroupId();
      }
    }, 10000);

    return () => clearInterval(intervalIdRef.current);
  }, [groupId]);

  const columns = useMemo(
    () => [
      {
        accessor: "stepInfo",
        displayName: "Step Info",
        dataRender: (step: any) => (
          <div className="pl-4">
            <div className="text-[#C1C1CB] font-medium">
              Step {step.step_number}: {step.name}
            </div>
            <div className="text-xs text-[#9494A1]">{step.description}</div>
          </div>
        ),
      },
      {
        accessor: "gap",
        displayName: "",
        dataRender: (step: any) => (
          <div className="pl-4">
          </div>
        ),
      },
      {
        accessor: "httpStatus",
        displayName: "HTTP Status",
        dataRender: (step: any) => (
          <div className="flex items-center justify-center w-auto">
            <div
              className={`border ${
                step.statusCode == 0 ? "text-error-100" : "text-[#C1C1CB]"
              } border-[#3F3F47] font-semibold font-sans text-sm px-3 rounded-md py-1`}
            >
              {step.statusCode === "LOADING" ? (
                <FaSpinner className="animate-spin text-white text-lg" />
              ) : step.statusCode === undefined ||
                step?.response?.statusCode === null ||
                Object.keys(step.response || {}).length === 0 ? (
                "-"
              ) : step.response?.statusCode === 0 ? (
                "ERROR"
              ) : (
                step.response?.statusCode
              )}
            </div>
          </div>
        ),
      },
      {
        accessor: "testResult",
        displayName: "Test Result",
        dataRender: (step: E2EStep) => {
          return (
            <>
              {step?.assertions_results?.length > 0 &&
              step?.assertions_results[0]?.message === "Processing..." ? (
                <div className="inline-block px-2 py-1 bg-yellow-100 text-yellow-800 rounded-md text-sm">
                  Processing
                </div>
              ) : (
                <>
                  {step.status === "PASSED" ? (
                    <div className="inline-block px-2 py-1 bg-[#15B0974D] text-[#A4F4E7] rounded-md text-sm">
                      Passed
                    </div>
                  ) : step.status === "FAILED" ? (
                    <div className="inline-block px-2 py-1 bg-[#C037444D] text-[#E4626F] rounded-md text-sm">
                      Failed
                    </div>
                  ) : step.status === "WARNING" ? (
                    <div className="inline-block px-2 py-1 bg-[#EDA1454D] text-[#F4C790] rounded-md text-sm">
                      Warning
                    </div>
                  ) : (
                    <div className="inline-block px-2 py-1 bg-vscode-editor-background text-gray-500 rounded-md text-sm">
                      Not Run
                    </div>
                  )}
                </>
                // (() => {
                //   const assertions = getAssertions(step as any);

                //   return assertions.length > 0 ? (
                //     assertions.some(
                //       (assertion: any) => assertion.message === "Processing..."
                //     ) ? (
                //       <div className="inline-block ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-md text-sm">
                //         Processing
                //       </div>
                //     ) : assertions.every(
                //         (assertion: any) =>
                //           assertion.passed || assertion.success
                //       ) ? (
                //       <div className="inline-block ml-2 px-2 py-1 bg-[#15B0974D] text-[#A4F4E7] rounded-md text-sm">
                //         Passed
                //       </div>
                //     ) : (
                //       <div className="inline-block ml-2 px-2 py-1 bg-[#C037444D] text-[#E4626F] rounded-md text-sm">
                //         Failed
                //       </div>
                //     )
                //   ) : (
                //     <div className="inline-block ml-2 px-2 py-1 bg-vscode-editor-background text-gray-500 rounded-md text-sm">
                //       Not Run
                //     </div>
                //   );
                // })()
              )}
            </>
          );
        },
      },
      {
        accessor: "actions",
        displayName: "Delete",
        dataRender: (step: E2EStep) => {
          return (
            <div className="flex items-center gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteStep(step.scenario_id, step.id);
                }}
                className="p-2"
              >
                {/* <CiTrash/> */}
                <Tooltip
                    content="Delete this step"
                    position="right"
                  >
                    <Image
                      src="/delete-icon.png"
                      height={20}
                      width={20}
                      alt="delete step"
                    />
                  </Tooltip>
                
              </button>
            </div>
          );
        },
      },
    ],
    [scenerios, selectedFilter, searchQuery]
  );

  if (loading || isPending) {
    return (
      <div className="h-screen w-full flex flex-col justify-center items-center">
        <TestCaseLoader />
      </div>
    );
  }

  return (
    <div className="flex w-full max-w-full">
      <div className="font-sans text-[#D1D1E3] h-[100vh] pr-2 pb-20 overflow-auto w-full">
        <div className="flex items-start justify-between mb-5 mt-2 max-w-full">
          <div className="max-w-[45%]">
            <div className="flex gap-2 items-center">
               <FaBars onClick={handleCollapse} className="cursor-pointer" />
              {isEditing && (
                <>
                  <PrimaryInput
                    value={currentScenarios?.[0]?.name}
                    onChange={(e) =>
                      setScenerios((prev) => {
                        const updatedScenarios = [...prev];
                        updatedScenarios[0].name = e.target.value;
                        return updatedScenarios;
                      })
                    }
                  />
                  <span
                    onClick={handleUpdateE2EScenarioName}
                    className="cursor-pointer text-[14px] text-drcodePurple hover:border-b-1 hover:border-drcodePurple"
                  >
                    Save
                  </span>
                  <span
                    onClick={() => {
                      setIsEditing(false);
                    }}
                    className="cursor-pointer text-[14px] hover:border-b-1"
                  >
                    Cancel
                  </span>
                </>
              )}
              {!isEditing && (
                <>
                  <h2 className="text-xl font-semibold">
                    {currentScenarios?.[0]?.name}
                  </h2>
                  <PiPencilLine
                    className="cursor-pointer"
                    onClick={() => setIsEditing(true)}
                  />
                </>
              )}
            </div>
            <div
              title={currentScenarios?.[0]?.description}
              className={`text-sm text-[#9999A8] ${
                currentScenarios?.[0]?.status === "VERIFY"
                  ? "max-w-[100%]"
                  : "max-w-[80%]"
              } text-ellipsis overflow-hidden whitespace-nowrap`}
            >
              {currentScenarios?.[0]?.status === "VERIFY"
                ? "Please verify the data mapping"
                : currentScenarios?.[0]?.description}
            </div>
          </div>
          {currentScenarios?.[0]?.status === "VERIFY" ? (
            <>
              <PrimaryButton label="Verify" onClick={handleVariableMapping} />
            </>
          ) : (
            <>
              <div className="flex justify-end gap-4 items-center">
                <button
                  className="flex items-center gap-2 px-3 py-1.5 text-white bg-[#875BF8] hover:bg-drcodePurple/80 border border-drcodePurple rounded-md font-semibold text-sm"
                  onClick={() => checkForDataAndRun(currentScenarios?.[0])}
                >
                  <FaPlayCircle />
                  Run Scenario
                </button>
                <div
                  className="button--outlined-primary py-2 px-2 text-[14px] text-drcodePurple font-semibold flex gap-2 cursor-pointer items-center"
                  onClick={handleResetAssertions}
                >
                  <RiResetLeftFill size={18}/>
                  Reset Assertions
                </div>
                {/* <div
                  className="text-[14px] text-drcodePurple font-semibold flex gap-2 cursor-pointer items-center"
                  onClick={() => setEnvModal(true)}
                >
                  <PiPencilLine />
                  Edit Environment
                </div> */}
                <FloatingDropdown
                  buttonContent={
                    <button className="button--outlined-primary py-2 px-2 text-[12px] flex gap-2">
                      <RxGear color="#875bf8" size={16} />
                    </button>
                  }
                >
                  <ul>
                    {settingsOptionsList.map((settingsOption) => {
                      return (
                        <p
                          onClick={settingsOption.onClick}
                          className="font-semibold text-[11px] rounded-md p-1 cursor-pointer hover:bg-drcodePurple/20 text-white"
                        >
                          {settingsOption.name}
                        </p>
                      );
                    })}
                  </ul>
                </FloatingDropdown>

                <div className="flex gap-2 items-center">
                  <ToggleSwitch
                    key={isConnected ? "on" : "off"}
                    disabled={!scenerios?.[0]?.is_datasource}
                    value={isConnected}
                    onToggle={(value) => handleConnectData(value)}
                    label="Connect Data"
                  />

                  <Tooltip
                    content="Link your data source to run tests with real values."
                    position="bottom"
                  >
                    <IoMdInformationCircleOutline />
                  </Tooltip>
                </div>

                
                {/* NEW: Delete Scenario Button */}
                <button
                  onClick={() =>
                    handleDeleteScenario(currentScenarios?.[0]?.id)
                  }
                >
                  <Tooltip
                    content="Delete the E2E Flow"
                    position="right"
                  >
                    <Image
                      src="/delete-icon.png"
                      height={20}
                      width={20}
                      alt="delete step"
                    />
                  </Tooltip>
                </button>
              </div>
            </>
          )}
        </div>

        {scenerios?.map((scenario) => {
          if (scenario.status === "VERIFY") {
            return (
              <div className="space-y-6">
                {scenario.steps.map((st: E2EStep, idx) => (
                  <div
                    key={st.id}
                    className="rounded-xl border border-[#2E2E3A] bg-[#1E1E28] shadow-sm p-4"
                  >
                    <div className="flex items-baseline gap-2 mb-4">
                      <span className="text-sm text-[#9494A1] font-semibold">
                        Step {idx + 1}
                      </span>
                      <span className="text-md text-white font-medium">
                        {st.name}
                      </span>
                    </div>

                    <table className="w-full table-fixed border-collapse text-sm mb-2">
                      <thead>
                        <tr className="text-left text-[#7C7C8A] text-xs border-b border-[#2E2E3A]">
                          <th className="py-2 px-2 w-1/3">Key</th>
                          <th className="py-2 px-2 w-2/3">Value</th>
                          <th className="py-2 px-2 w-10"></th>
                        </tr>
                      </thead>
                      <tbody>
                        {Object.entries(st.data_mapping).map(
                          ([key, value], rowIdx) => (
                            <tr
                              key={rowIdx}
                              className={rowIdx % 2 === 0 ? "bg-[#262632]" : ""}
                            >
                              <td className="py-2 px-2 align-top text-[#C1C1CB] font-medium">
                                <PrimaryInput
                                  // disabled={true}
                                  type="text"
                                  value={key as any}
                                  onChange={(e) =>
                                    handleDataMappingChange(
                                      st,
                                      key,
                                      e.target.value,
                                      "key",
                                      rowIdx
                                    )
                                  }
                                  className="w-full bg-[#2E2E3A] border border-[#3F3F4B] rounded-md px-3 py-1.5 text-[#C1C1CB] text-sm placeholder:text-[#757589] focus:outline-none focus:ring-2 focus:ring-[#4E4EF6]"
                                />
                              </td>
                              <td className="py-2 px-2">
                                <PrimaryInput
                                  type="text"
                                  value={value as any}
                                  onChange={(e) =>
                                    handleDataMappingChange(
                                      st,
                                      key,
                                      e.target.value,
                                      "value",
                                      rowIdx
                                    )
                                  }
                                  className="w-full bg-[#2E2E3A] border border-[#3F3F4B] rounded-md px-3 py-1.5 text-[#C1C1CB] text-sm placeholder:text-[#757589] focus:outline-none focus:ring-2 focus:ring-[#4E4EF6]"
                                />
                              </td>
                              <td className="py-2 px-2">
                                <MdDeleteOutline
                                  onClick={() => handleDeleteRow(st, rowIdx)}
                                  className="cursor-pointer"
                                  size={30}
                                />
                              </td>
                            </tr>
                          )
                        )}
                      </tbody>
                    </table>

                    <div className="flex justify-end">
                      <button
                        onClick={() => handleAddRow(st)}
                        className="text-[#4E4EF6] text-sm font-medium hover:underline"
                      >
                        + Add Row
                      </button>
                    </div>
                  </div>
                ))}

                <div className="flex justify-end">
                  <PrimaryButton
                    label="Verify"
                    onClick={handleVariableMapping}
                  />
                </div>
              </div>
            );
          }

          const scenarioProcessing = scenario.steps.some((st: E2EStep) =>
            st.assertions_results?.some(
              (res: any) =>
                res.passed === "processing" || res.passed === "generating"
            )
          );

          // 2) Introduce a scenarioStatus variable with a possible "Processing" value:
          let scenarioStatus: "Not Run" | "Passed" | "Failed" | "Processing" =
            "Not Run";
          // scenario-level pass/fail

          const allStepsHaveResults = scenario.steps.every(
            (s: E2EStep) =>
              s.assertions_results && s.assertions_results.length > 0
          );

          let scenarioFailed = false;
          try {
            scenarioFailed = scenario.steps.some((st: E2EStep) =>
              st.assertions_results?.some(
                (res: any) => !JSON.parse(res ?? "{}")?.passed
              )
            );
          } catch (error) {
            scenarioFailed = false;
          }

          const scenarioPassed = allStepsHaveResults && !scenarioFailed;

          // 3) Figure out scenarioStatus in priority order:
          if (scenarioProcessing) {
            scenarioStatus = "Processing";
          } else if (scenarioPassed) {
            scenarioStatus = "Passed";
          } else if (allStepsHaveResults && scenarioFailed) {
            scenarioStatus = "Failed";
          } else {
            scenarioStatus = "Not Run";
          }

          return (
            <div
              key={scenario.id}
              className="mb-5 border border-white/30 p-4 rounded-lg bg-vscode-editor-background "
            >
              {/* <div className="flex items-center justify-between mb-1">
                <h3 className="text-lg font-bold text-[#D1D1E3]">
                  {scenario.name}
                </h3>
                <div className="flex items-center gap-2">
                  {scenarioStatus === "Processing" && (
                    <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-md">
                      PROCESSING
                    </span>
                  )}
                  {scenarioStatus === "Passed" && (
                    <span className="px-2 py-1 bg-[#15B0974D] text-[#A4F4E7] text-xs rounded-md">
                      PASSED
                    </span>
                  )}
                  {scenarioStatus === "Failed" && (
                    <span className="px-2 py-1 bg-[#C037444D] text-[#E4626F] text-xs rounded-md">
                      FAILED
                    </span>
                  )}
                  {scenarioStatus === "Not Run" && (
                    <span className="px-2 py-1 bg-gray-600 text-white text-xs rounded-md">
                      NOT RUN
                    </span>
                  )}
              
                  <ToggleSwitch
                    disabled={!scenario.is_datasource}
                    value={isConnected}
                    onToggle={(value) => setIsConnected(value)}
                    label="Connect Data"
                  />
                  <Tooltip
                    content="Link your data source to run tests with real values."
                    position="bottom"
                  >
                    <IoMdInformationCircleOutline />
                  </Tooltip>

                  <button
                    className="flex items-center gap-2 px-3 py-1.5 text-white bg-[#875BF8] hover:bg-drcodePurple/80 border border-drcodePurple rounded-md font-semibold text-sm"
                    onClick={() => checkForDataAndRun(scenario)}
                  >
                    <FaPlayCircle />
                    Run Scenario
                  </button>
                  <button onClick={() => handleDeleteScenario(scenario.id)}>
                    <MdDeleteOutline />
                  </button>
                </div>
              </div>
              <p className="mb-2 text-sm text-[#9999A8]">
                {scenario.description}
              </p> */}

              {/* Steps */}
              <div className="mt-3">
                <DrCodeTable
                  pageSize={50}
                  className="w-full"
                  data={scenario.steps}
                  columns={columns}
                  onRowExpanded={(id, step) => {
                    handleToggleStepOpen(scenario.id, step.id);
                  }}
                  rowExpandedRender={(step: any) => (
                    <ApiTestDetailedView
                      apiTestDetails={{
                        ...step,
                        request: JSON.stringify(step.request, null, 2),
                      }}
                      generateAssertions={() => {}}
                      handleSaveRequest={handleSaveRequest as any}
                      idx={step.id}
                      modifyAssertionRow={modifyAssertionRow as any}
                      modifyRequestBodyRow={modifyRequestBodyRow as any}
                      modifyResponseBodyRow={() => {}}
                      runAssertionsLocally={runAssertionsLocally as any}
                      running={false}
                      isE2E={true}
                    />
                  )}
                />
              </div>
            </div>
          );
        })}
      </div>

      <div className="min-w-[6%]">
        <RightNavigation navigationItems={navigationItems} />
      </div>

      {envModal && (
        <Modal
          isOpen={envModal}
          title={
            <>
              <div className="font-bold text-xl text-vscode-editor-foreground">
                Edit Environment Variables
              </div>
              <p className="text-gray-500 text-sm">
                It's your environment, update the keys like a pro!
              </p>
            </>
          }
        >
          <EnvList
            environmentId={environment?.toString()}
            handleClose={() => setEnvModal(false)}
            setSelectedEnvOptions={setSelectedEnvOptions}
          />
        </Modal>
      )}

      {/* {realTestDataModal && (
        <FileMappingModal
          isOpen={realTestDataModal}
          handleClose={() => setRealTestDataModal(false)}
        />
      )} */}

      {realTestDataModal && (
        <BottomSheet
          isOpen={realTestDataModal}
          onClose={() => {
            setRealTestDataModal(false);
          }}
        >
          <FileMappingModal
            isOpen={realTestDataModal}
            handleClose={() => setRealTestDataModal(false)}
          />
        </BottomSheet>
      )}
      {showDataSourceWarningModal && (
        <Modal
          isOpen={showDataSourceWarningModal}
          showFooterButtons={true}
          saveLabel="Add Data"
          cancelLabel="Continue without data"
          onCancel={() => {
            setShowDataSourceWarningModal(false);
            handleRunScenario(currentScenerio as any, scenerios);
          }}
          onSave={() => {
            router.push(`${pathname}/dataSource?isEdit=true`);
            setShowDataSourceWarningModal(false);
          }}
        >
          <p>
            You don't have sufficient data to run the test. Please add data to
            the test case, or continue with the existing data.
          </p>
        </Modal>
      )}
      {chooseDataSourceModal && (
        <DataSourcePopup
          onClose={() => setChooseDataSourceModal(false)}
          onCsvClick={() => {
            setChooseDataSourceModal(false);
            setRealTestDataModal(true);
          }}
          onAiClick={() => {
            setChooseDataSourceModal(false);
            handleRunScenario(currentScenerio as any, scenerios);
          }}
        />
      )}

      {runTimeSettingsModal && (
        <Modal
          isOpen={runTimeSettingsModal}
          title={
            <>
              <div className="font-bold text-xl text-vscode-editor-foreground">
                Advanced Settings
              </div>
              <p className="text-gray-500 text-sm">
                Configure the test execution parameters.
              </p>
            </>
          }
        >
          <>
            <label htmlFor="waitTime" className="mb-1 text-[12px]">
              Timeout Duration for Request Completion (ms)
            </label>
            <PrimaryInput
              type="number"
              id="waitTime"
              min={0}
              value={waitTime}
              onChange={(e) => setWaitTime(Number(e.target.value))}
              // className="border border-drcodePurple/50 px-3 py-2 focus:outline-drcodePurple rounded-md bg-vscode-editor-background text-white"
              placeholder="e.g., 1000"
            />

            <div className="flex gap-2 items-end justify-end">
              <button
                onClick={() => setRunTimeSettingsModal(false)}
                className="button--primary mt-4 py-2 px-8 text-[14px]"
                style={{
                  fontSize: "14px",
                }}
              >
                Save Settings
              </button>
            </div>
          </>
        </Modal>
      )}

<BottomSheet
        isOpen={isCollapsed}
        onClose={handleCollapse}
        side="left"
        widthInPercentage="30%"
      >
        <TestsExplorer handleCollapse={handleCollapse} />
      </BottomSheet>
    </div>
  );
};

export default E2EView;
