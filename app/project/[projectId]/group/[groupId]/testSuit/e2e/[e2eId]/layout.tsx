import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import type { Metadata } from "next";

type Props = {
  params: Promise<{ e2eId: string }>;
};
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const id = (await params).e2eId;
  const resource = await fetch(
    `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GET_E2E_BY_E2E_ID}/${id}`
  ).then((res) => res.json());

  return {
    title: `Test Suite - ${resource?.data?.[0]?.name}`,
    openGraph: {
      // images: [],
    },
  };
}

export default function TestSuitIdLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
  params: any;
}>) {
  return <>{children}</>;
}
