import TopLoader from "@/components/common/top-loader/TopLoader";
import type { Metadata } from "next";

export const metadata: Metadata = {
    title: "Add API",
    description: "Dr Code: Experience AI-powered automation API Testing Platform",
    icons: {
        icon: "/logo.png",
        apple: "/logo.png",
    },
    manifest: "/manifest.json"
};

export default function AddSuitLayout({
    children
}: Readonly<{
    children: React.ReactNode;
}>) {

    return (
        <>
            <TopLoader/>
            {children}
        </>
    );
}
