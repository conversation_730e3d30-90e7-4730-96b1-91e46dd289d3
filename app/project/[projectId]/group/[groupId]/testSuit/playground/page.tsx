import React, { Suspense } from "react";
import Image from "next/image";
import AddApiScreen from "@/components/add-apis/add-api-screen/AddApiScreen";
import Loader from "@/components/common/loader/Loader";

const AddApi = () => {
  return (
    <Suspense fallback={<Loader />}>
      <div className={`min-h-[100vh] flex w-[80%] mx-auto overflow-x-hidden`}>
        <AddApiScreen />
      </div>
    </Suspense>
  );
};
export default AddApi;
