import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import type { Metadata } from "next";

type Props = {
  params: Promise<{ testSuitId: string }>;
};
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const id = (await params).testSuitId;
  const resource = await fetch(
    `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testSuites/${id}`
  ).then((res) => res.json());

  return {
    title: `Test Suite - ${resource?.data?.test_suite_name}`,
    openGraph: {
      // images: [],
    },
  };
}

export default function TestSuitIdLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
  params: any;
}>) {
  return <>{children}</>;
}
