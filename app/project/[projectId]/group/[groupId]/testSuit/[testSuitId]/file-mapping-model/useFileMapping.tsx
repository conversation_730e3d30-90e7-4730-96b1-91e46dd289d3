import useToast from "@/hooks/useToast";
import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import axios from "axios";
import { useParams, usePathname } from "next/navigation";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

export enum DATASOURCE_TYPE {
  TEST_SUITE = "test_suite",
  E2E = "e2e",
}

const useFileMapping = () => {
  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();
  const id = (params.testSuitId as string) ?? params.e2eId;

  const { showToast } = useToast();

  const [realTestData, setRealTestData] = useState<any>(null);
  const [mappingStep, setMappingStep] = useState<number>(0);
  const [uniqueHeaders, setUniqueHeaders] = useState<string[]>([]);
  const [realTestDataModal, setRealTestDataModal] = useState<boolean>(false);
  const [fileKeys, setFileKeys] = useState<string[]>([]);
  const [keyTypes, setKeyTypes] = useState<{ [key: string]: string }>({});

  const handleSampleCsvDownload = async () => {
    try {
      const id = (params.testSuitId as string) ?? params.e2eId;
      // const type = params.testSuitId
      //   ? DATASOURCE_TYPE.TEST_SUITE
      //   : DATASOURCE_TYPE.E2E;

      // const response = await axios.get(
      //   `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.DOWNLOAD_SAMPLE_CSV}?entity_id=${id}&type=${type}&download=false`
      // );

      // console.log("log:: response is ", response);

      // const blob = new Blob([response.data], { type: "text/csv" });
      // const url = window.URL.createObjectURL(blob);

      // const link = document.createElement("a");
      // link.href = url;
      // link.setAttribute("download", `test_data_template_${id}.csv`);

      // document.body.appendChild(link);
      // link.click();

      // // Clean up
      // document.body.removeChild(link);
      // window.URL.revokeObjectURL(url);

      // use papaparse to convert filekeys to csv
      const csvContent =
        "data:text/csv;charset=utf-8," +
        uniqueHeaders.map((e) => e).join(",") +
        "\n";
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement("a");
      link.setAttribute("href", encodedUri);
      link.setAttribute("download", `test_data_template_${id}.csv`);
      document.body.appendChild(link); // Required for FF
      link.click();
      document.body.removeChild(link); // Clean up
      showToast("CSV downloaded successfully", "success");
    } catch (error) {
      console.error("Error downloading CSV", error);

      showToast("Something went wrong", "error");
    }
  };

  const getFileData = async () => {
    try {
      const type = params.testSuitId
        ? DATASOURCE_TYPE.TEST_SUITE
        : DATASOURCE_TYPE.E2E;

      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.DOWNLOAD_SAMPLE_CSV}?entity_id=${id}&type=${type}&download=false`
      );
      console.log("log:: response is ", response.data.data);
      if (type === DATASOURCE_TYPE.E2E) {
        setFileKeys(response.data.data?.columns);
      } else {
        setFileKeys(response.data?.data?.fields);
      }
    } catch (error) {}
  };

  const handleFileData = (data: any) => {
    if (!data) {
      return;
    }
    if (data.length === 0) {
      showToast("Please enter some data in csv", "error");
      return;
    }
    console.log("log:: data is ", data);
    setRealTestData(data);
  };

  const handleCSVMapping = () => {
    localStorage.setItem("csvData", JSON.stringify(realTestData));

    if (pathname.includes("dataSource")) {
      const currentUrl = new URL(window.location.href);

      // Remove the "isEdit" param
      currentUrl.searchParams.delete("isEdit");

      const newPath =
        currentUrl.pathname +
        (currentUrl.search ? `?${currentUrl.searchParams.toString()}` : "");

      console.log("newPath:", newPath);

      router.replace(newPath); // ✅ use router.replace instead of window.location.replace
      setRealTestDataModal(false);
      
    } else {
      router.push(`${pathname}/dataSource`);
    }
  };

  const handleCSVMappingStep = () => {
    setMappingStep(1);
  };

  const mergeSteps = (stepArray) => {
    return stepArray.reduce((acc, stepObj) => {
      return { ...acc, ...stepObj };
    }, {});
  };

  const getFileTypes = () => {
    const typesArray = localStorage.getItem(`${id}_typesArray`);
    if (typesArray) {
      let types = JSON.parse(typesArray ?? "[]");
      let mergedTypes = mergeSteps(types);
      setKeyTypes(mergedTypes);
      localStorage.setItem(`${id}_csvKeyTypes`, JSON.stringify(mergedTypes));
    }
  };

  // call getFileData when the component mounts
  useEffect(() => {
    getFileData();
    getFileTypes();
  }, [id]);

  return {
    realTestData,
    setRealTestData,
    mappingStep,
    setMappingStep,
    uniqueHeaders,
    setUniqueHeaders,
    handleSampleCsvDownload,
    handleFileData,
    handleCSVMapping,
    handleCSVMappingStep,
    pathname,
    router,
    setRealTestDataModal,
    realTestDataModal,
    fileKeys,
    setKeyTypes,
    keyTypes,
  };
};

export default useFileMapping;
