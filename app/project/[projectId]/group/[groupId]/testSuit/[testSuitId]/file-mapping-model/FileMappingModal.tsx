import FileUploader from "@/components/common/file-uploader/FileUploader";
import Modal from "@/components/common/modal/Modal";
import Image from "next/image";
import { useParams, usePathname, useRouter } from "next/navigation";
import React, { useState } from "react";
import useFileMapping from "./useFileMapping";
import PrimaryButton from "@/components/common/buttons/PrimaryButton";
import PrimaryButtonOutlined from "@/components/common/buttons/PrimaryButtonOutlined";
import Select from "react-select";

interface FileMappingModalProps {
  isOpen?: boolean;
  setModalOpen?: (isOpen: boolean) => void;
  handleClose?: () => void;
  isEdit?: boolean;
  setReuploadModal?: (isOpen: boolean) => void;
}

const FileMappingModal = ({
  isOpen = false,
  handleClose = () => {},
  isEdit = false,
  setModalOpen = () => {},
  setReuploadModal = () => {},
}: FileMappingModalProps) => {
  const {
    realTestData,
    handleCSVMapping,
    handleCSVMappingStep,
    handleFileData,
    handleSampleCsvDownload,
    mappingStep,

    setMappingStep,
    setUniqueHeaders,
    fileKeys,
    keyTypes,
    setKeyTypes,
  } = useFileMapping();

  const params = useParams();
  const id = (params.testSuitId as string) ?? params.e2eId;
  return (
    <div className="h-full">
      <div className="space-y-2 h-[80vh] overflow-auto">
        {mappingStep === 0 ? (
          <>
            <div className="text-[16px] text-[#D1D1E3]">
              Define Unique Constraints for API Testing{" "}
            </div>
            <div className="text-[13px] text-[#9494A1]">
              Easily mark fields as unique using checkboxes to simulate
              real-world data scenarios in your API tests.
            </div>

            <table className="min-w-full max-h-[60%]  text-white   rounded-lg overflow-auto">
              <thead>
                <tr className="border-b-1 border-gray-600">
                  <th className="p-1 text-left text-sm text-[#B2B2C1]">Key</th>
                  <th className="p-1 text-left text-sm text-[#B2B2C1]">
                    Unique
                  </th>
                  <th className="p-1 text-left text-sm text-[#B2B2C1]">
                    Value type
                  </th>
                </tr>
              </thead>
              <tbody>
                {fileKeys?.map((key, idx) => (
                  <tr key={idx} className="">
                    <td className="p-1 py-2 border-b border-gray-700 text-[14px] font-normal">
                      {key}
                    </td>
                    <td className="p-1 py-2 border-b border-gray-700">
                      <input
                        type="checkbox"
                        className="w-4 h-4 accent-blue-500"
                        onChange={(e) => {
                          setUniqueHeaders((prevUniqueHeaders) => {
                            const newUniqueHeaders = [...prevUniqueHeaders];
                            if (e.target.checked) {
                              newUniqueHeaders.push(key);
                            } else {
                              const index = newUniqueHeaders.indexOf(key);
                              if (index > -1) {
                                newUniqueHeaders.splice(index, 1);
                              }
                            }
                            localStorage.setItem(
                              "csvUniqueHeaders",
                              JSON.stringify(newUniqueHeaders)
                            );

                            setKeyTypes((prevKeyTypes) => {
                              if (prevKeyTypes[key]) {
                                return prevKeyTypes;
                              }
                              localStorage.setItem(
                                "csvKeyTypes",
                                JSON.stringify({
                                  ...prevKeyTypes,
                                  [key]: "string",
                                })
                              );
                              return {
                                ...prevKeyTypes,
                                [key]: "string",
                              };
                            });

                            return newUniqueHeaders;
                          });
                        }}
                      />
                    </td>
                    <td className="p-1 py-2 border-b border-gray-700">
                      {/* <Select
                        options={[
                          { value: "string", label: "String" },
                          { value: "integer", label: "Integer" },
                          { value: "boolean", label: "Boolean" },
                          { value: "null", label: "Null" },
                        ]}
                        onChange={(e) => {
                          setKeyTypes((prevKeyTypes) => {
                            const newKeyTypes = { ...prevKeyTypes };
                            newKeyTypes[key] = e.value;
                            localStorage.setItem(
                              "csvKeyTypes",
                              JSON.stringify(newKeyTypes)
                            );
                            return newKeyTypes;
                          });
                        }}
                        value={keyTypes[key]}
                        
                        placeholder="String"
                        classNamePrefix="react-select"
                        instanceId="method-select"
                        styles={{
                          control: (provided) => ({
                            ...provided,
                            fontSize: "14px",
                            color: "#777781",
                          }),
                          singleValue: (provided) => ({
                            ...provided,
                            fontSize: "14px",
                            color: "#777781",
                          }),
                          option: (provided, state) => ({
                            ...provided,
                            fontSize: "14px",
                            color: state.isSelected ? "white" : "#777781",
                            backgroundColor: state.isSelected
                              ? "#777781"
                              : "white",
                          }),
                          placeholder: (provided) => ({
                            ...provided,
                            fontSize: "14px",
                            color: "#777781",
                          }),
                        }}
                      /> */}

                      <select
                        value={keyTypes[key]}
                        onChange={(e) => {
                          setKeyTypes((prev) => {
                            localStorage.setItem(
                              `${id}_csvKeyTypes`,
                              JSON.stringify({
                                ...prev,
                                [key]: e.target.value,
                              })
                            );
                            return {
                              ...prev,
                              [key]: e.target.value,
                            };
                          });
                        }}
                        className="w-full p-1 border border-gray-600 rounded-lg"
                      >
                        <option value="string">String</option>
                        <option value="number">Number</option>
                        <option value="boolean">Boolean</option>
                        <option value="null">Null</option>
                      </select>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </>
        ) : (
          <>
            <div className="text-[16px] text-[#D1D1E3]">
              Upload Real Data for Testing
            </div>
            <div className="text-[13px] text-[#9494A1]">
              Use real data to test your API. Just download, fill, and upload
              the CSV.
            </div>
            <div className="flex items-center gap-3 w-full bg-[#1B1B41] rounded-lg p-2">
              <Image
                src={`/csv_upload.svg`}
                alt={"Upload Real Test Data"}
                width={24}
                height={24}
              />
              <div className="text-[12px] text-[#B2B2C1]">
                Ensure accurate data import with our{" "}
                <span
                  onClick={handleSampleCsvDownload}
                  className="underline pl-1 cursor-pointer"
                >
                  {" "}
                  CSV template.
                </span>
              </div>
            </div>
            <FileUploader handleData={handleFileData} />
          </>
        )}
      </div>
      <div className="mt-auto flex justify-end gap-2">
        {mappingStep === 1 && (
          <PrimaryButtonOutlined
            label="Back"
            onClick={() => {
              setMappingStep(0);
            }}
          />
        )}
        <PrimaryButtonOutlined
          label="Close"
          onClick={() => {
            setMappingStep(0);
            handleClose();
          }}
        />
        <PrimaryButton
          label={mappingStep === 1 ? "Save" : "Download Sample CSV"}
          onClick={
            mappingStep === 1
              ? () => {
                  handleCSVMapping();

                  setTimeout(() => {
                    handleClose();
                  }, 1000);
                }
              : () => {
                  // handleCSVMapping();
                  handleSampleCsvDownload();
                  setMappingStep(1);
                  // setModalOpen(false);
                }
          }
          // disabled={realTestData === null}
        />
      </div>
      {mappingStep === 0 && (
        <p
          className="mt-2 text-center underline cursor-pointer text-[11px]"
          onClick={() => setMappingStep(1)}
        >
          Already have a CSV?
        </p>
      )}

      {/* </Modal> */}
    </div>
  );
};

export default FileMappingModal;
