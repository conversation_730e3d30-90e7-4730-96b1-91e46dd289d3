import type { Metadata } from "next";

// export const metadata: Metadata = {
//     title: "Playground",
//     description: "Dr Code: Experience AI-powered automation API Testing Platform",
//     icons: {
//         icon: "/logo.png",
//         apple: "/logo.png",
//     },
//     manifest: "/manifest.json"
// };
type Props = {
  params: Promise<{ testsuitId: string }>;
};
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const id = (await params).testsuitId;
  // const product = await fetch(`https://.../${id}`).then((res) => res.json())

  // optionally access and extend (rather than replace) parent metadata
  // const previousImages = (await parent).openGraph?.images || []

  return {
    title: `Test Suit - ${id}`,
    openGraph: {
      // images: ['/', ...previousImages],
    },
  };
}

export default function TestSuitLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
  params: any;
}>) {
  return <>{children}</>;
}
