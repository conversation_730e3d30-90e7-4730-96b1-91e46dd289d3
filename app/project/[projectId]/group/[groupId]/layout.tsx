"use client";

import { TestsExplorer } from "@/components/test-runner/tests-explorer/TestsExplorer";
import styles from "./page.module.scss";
import {
  ImperativePanelHandle,
  Panel,
  PanelGroup,
  PanelResizeHandle,
} from "react-resizable-panels";
import { useRef, useState } from "react";
import { FaBars } from "react-icons/fa";
import BottomSheet from "@/components/common/right-bottom-sheet/RightBottomSheet";

export const dynamic = "force-dynamic";

export default function PlaygroundLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: any;
}>) {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const panelRef = useRef<ImperativePanelHandle>(null);
  const handleCollapse = () => {
    setIsCollapsed((prev) => !prev);
  };

  return (
    <>
      {/* <PanelGroup
        onDrag={(e) => console.log(e)}
        autoSaveId="123"
        direction="horizontal"
      >
        <Panel
          collapsible
          ref={panelRef}
          defaultSize={25}
          minSize={20}
          maxSize={50}
        >
          <aside className={styles["tests-explorer"]}>
            <TestsExplorer handleCollapse={handleCollapse} />
          </aside>
        </Panel>
        <PanelResizeHandle className="w-2" />
        <Panel defaultSize={73}>
          <main
            className={`${styles["main-view"]} ${
              isCollapsed ? "p-4 pl-14" : "p-4 pr-0"
            }`}
          >
            {children}
          </main>
        </Panel>
        <PanelResizeHandle />
      </PanelGroup> */}
      {/* <div
        className={`absolute left-0 top-0 h-full w-1/4 z-10 ${
          isCollapsed ? "hidden" : ""
        }`}
      > */}
      <BottomSheet
        isOpen={isCollapsed}
        onClose={handleCollapse}
        side="left"
        widthInPercentage="30%"
      >
        <TestsExplorer handleCollapse={handleCollapse} />
      </BottomSheet>
      <div className={`py-4 px-8 pr-0 flex gap-2`}>
        {/* <div className="absolute top-[2rem] left-2 z-10">
          <FaBars onClick={handleCollapse} className="cursor-pointer" />
        </div> */}
        {children}
      </div>
    </>
  );
}
