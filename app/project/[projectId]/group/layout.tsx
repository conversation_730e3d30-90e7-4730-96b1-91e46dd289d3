import type { Metadata } from "next";
import Head from "next/head";

// export const metadata: Metadata = {
//     title: "Group",
//     description: "Dr Code: Experience AI-powered automation API Testing Platform",
//     icons: {
//         icon: "/logo.png",
//         apple: "/logo.png",
//     },
//     manifest: "/manifest.json"
// };

export default function PlaygroundLayout({
    children, params
}: Readonly<{
    children: React.ReactNode;
    params: any
}>) {

    return (
        <div>
            <Head>
                <title>Group</title>
                <meta name="description" content="Dr Code: Experience AI-powered automation API Testing Platform" />
                <link rel="icon" href="/logo.png" />
                <link rel="apple-touch-icon" href="/logo.png" />
                <link rel="manifest" href="/manifest.json" />
            </Head>
            {children}
        </div>
    );
}
