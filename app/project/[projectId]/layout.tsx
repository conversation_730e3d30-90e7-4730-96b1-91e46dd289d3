import Sidenav from "@/components/common/sidenav/Sidenav";
import TopLoader from "@/components/common/top-loader/TopLoader";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Groups",
  description: "Dr Code: Experience AI-powered automation API Testing Platform",
  icons: {
    icon: "/logo.png",
    apple: "/logo.png",
  },
  manifest: "/manifest.json",
};

export default function ProjectDetailsLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: any;
}>) {
  return (
    <>
      <TopLoader />
      <div className="flex w-full">
        <Sidenav />
        <div className="w-full">{children}</div>
      </div>
    </>
  );
}
