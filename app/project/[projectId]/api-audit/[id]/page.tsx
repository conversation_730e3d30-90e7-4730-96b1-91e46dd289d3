"use client";

import PrimaryButton from "@/components/common/buttons/PrimaryButton";
import PrimaryButtonOutlined from "@/components/common/buttons/PrimaryButtonOutlined";
import TestCaseLoader from "@/components/common/test-case-loader/TestCaseLoader";
import useToast from "@/hooks/useToast";
import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import {
  NEXT_PUBLIC_DR_CODE_BASE_API_URL,
  NEXT_PUBLIC_DR_CODE_FRONTEND_URL,
} from "@/temp-utils/Constants/globalVariables";
import axios from "axios";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";
import { FaRegEye } from "react-icons/fa";
import { IoDownloadOutline } from "react-icons/io5";
import { MdOutlineFileDownload } from "react-icons/md";
const POLL_INTERVAL = 6000; // 3 seconds

const ApiAuditReportPage = () => {
  const [loading, setLoading] = useState(false);
  const [auditData, setAuditData] = useState(null);
  const [steps, setSteps] = useState([
    {
      id: 1,
      title: "API Auditing is in progress",
    },
  ]);
  const { id, projectId } = useParams();

  const router = useRouter();

  const { showToast } = useToast();

  const pollTimeout = useRef<NodeJS.Timeout | null>(null);

  const fetchApiAuditReport = async () => {
    setLoading(true);
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GET_API_AUDIT_REPORT}/${id}`
      );

      const result = response.data.data;

      if (result.passed_test_suites > 0) {
        const passedStep = {
          id: 2,
          title: `${result?.passed_test_suites} Test suites passed`,
        };

        setSteps((prevSteps) => {
          const index = prevSteps.findIndex((item) => item.id === 2);

          if (index !== -1) {
            // Step with id 2 exists, update it
            const updatedSteps = [...prevSteps];
            updatedSteps[index] = passedStep;
            return updatedSteps;
          } else {
            // Step doesn't exist, add it
            return [...prevSteps, passedStep];
          }
        });
      }

      if (result.failed_test_suites > 0) {
        const failedStep = {
          id: 3,
          title: `${result?.failed_test_suites} Test suites failed`,
        };

        setSteps((prevSteps) => {
          const index = prevSteps.findIndex((item) => item.id === 3);

          if (index !== -1) {
            // Step with id 2 exists, update it
            const updatedSteps = [...prevSteps];
            updatedSteps[index] = failedStep;
            return updatedSteps;
          } else {
            // Step doesn't exist, add it
            return [...prevSteps, failedStep];
          }
        });
      }

      setAuditData(result);

      if (result.status !== "completed") {
        pollTimeout.current = setTimeout(fetchApiAuditReport, POLL_INTERVAL);
      }
    } catch (e) {
      console.error("Error fetching audit report:", e);
    }
    setLoading(false);
  };

  const handleDownloadPdf = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/audit/${id}/download-pdf`,
        {
          responseType: "blob", // Important: to get binary data
        }
      );

      if (response.status === 404) {
        showToast("Please Wait, Preparing final report", "error");
        return;
      }

      const blob = new Blob([response.data], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;

      // You can customize the file name
      link.download = `audit-report-${id}.pdf`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);
    } catch (error) {
      console.error("Failed to download PDF", error);
    }
  };

  const handleGroupRedirect = () => {
    router.push(`/project/${projectId}/group/${auditData.group_id}`);
  };

  useEffect(() => {
    if (!id) return;

    fetchApiAuditReport();

    // Cleanup on unmount
    return () => {
      if (pollTimeout.current) clearTimeout(pollTimeout.current);
    };
  }, [id]);

  return (
    <div className="h-screen overflow-auto">
      {loading || auditData?.status !== "completed" ? (
        <>
          <TestCaseLoader steps={steps} />
        </>
      ) : (
        <>
          {auditData && (
            <>
              <div className="flex h-full">
                {/* <div
              className="py-4 px-2"
              style={{
                width: "0%",
              }}
            >
              <div className="flex justify-between">
                <p>Previous Audits</p>
                <Link href={"/api-audit"} />
              </div>
            </div> */}
                <div className="p-7 w-full">
                  <div className="flex justify-between items-center w-full">
                    <div>
                      <div className="flex gap-2 items-end">
                        <div
                          className="text-[#D1D1E3] text-[24px]"
                          style={{
                            lineHeight: "normal",
                          }}
                        >
                          {auditData.name}
                        </div>
                        <div
                          className="text-[#B2B2C1] text-sm font-normal"
                          style={{
                            lineHeight: "24px",
                          }}
                        >
                          {new Intl.DateTimeFormat("en-US", {
                            year: "numeric",
                            month: "short",
                            day: "numeric",
                            hour: "numeric",
                            minute: "numeric",
                          }).format(new Date(auditData.created_at))}
                        </div>
                      </div>
                      <div className="text-[#9494A1] text-[14px] font-normal">
                        {auditData?.summary}
                      </div>
                    </div>
                    <div className="flex gap-4">
                      <PrimaryButton
                        onClick={handleDownloadPdf}
                        style={{
                          display: "flex",
                          gap: "12px",
                          alignItems: "center",
                        }}
                      >
                        <MdOutlineFileDownload size={16} />
                        Download Report
                      </PrimaryButton>

                      <PrimaryButtonOutlined
                        style={{
                          display: "flex",
                          gap: "12px",
                          alignItems: "center",
                        }}
                        onClick={handleGroupRedirect}
                      >
                        <FaRegEye size={16} />
                        View test case
                      </PrimaryButtonOutlined>
                    </div>
                  </div>

                  <div className="flex justify-between bg-[#131330] rounded-md mt-8">
                    <div className="px-5 py-4">
                      <div className="text-[#9494A1] text-sm">
                        Failed Test Suites
                      </div>
                      <div className="">{auditData?.failed_test_suites}</div>
                    </div>
                    <div className="px-5 py-4">
                      <div className="text-[#9494A1] text-sm">
                        Error Test Suites
                      </div>
                      <div className="">{auditData?.error_test_suites}</div>
                    </div>
                    <div className="px-5 py-4">
                      <div className="text-[#9494A1] text-sm">
                        Passed Test Suites
                      </div>
                      <div className="">{auditData?.passed_test_suites}</div>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </>
      )}

      {/* {auditData && (
        <>
          <div className="bg-[#1E1E2F] p-6  shadow-md text-[#C1C1CB] space-y-4 border border-[#2A2A3A] h-screen">
            <h2 className="text-xl font-semibold text-white">
              Audit Report Summary
            </h2>

            <p className="text-sm text-[#A1A1B2]">{auditData?.summary}</p>

            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm text-white">
              <div className="bg-[#2B2B3C] p-4 rounded-lg text-center">
                <div className="text-xs text-[#7C7C8A] mb-1">
                  Failed Test Suites
                </div>
                <div className="text-lg font-bold text-red-400">
                  {auditData?.failed_test_suites}
                </div>
              </div>
              <div className="bg-[#2B2B3C] p-4 rounded-lg text-center">
                <div className="text-xs text-[#7C7C8A] mb-1">
                  Error Test Suites
                </div>
                <div className="text-lg font-bold text-yellow-400">
                  {auditData?.error_test_suites}
                </div>
              </div>
              <div className="bg-[#2B2B3C] p-4 rounded-lg text-center">
                <div className="text-xs text-[#7C7C8A] mb-1">
                  Passed Test Suites
                </div>
                <div className="text-lg font-bold text-green-400">
                  {auditData?.passed_test_suites}
                </div>
              </div>
            </div>

            <div className="pt-4 space-y-2">
              <div className="text-white font-medium text-sm">
                Links to Test Suites:
              </div>

              <div className="space-y-2">
                {auditData?.test_suites?.map((ts) => {
                  if (ts.status === "failed")
                    return (
                      <a
                        key={ts.test_suite_id}
                        target="_blank"
                        rel="noopener noreferrer"
                        href={`${NEXT_PUBLIC_DR_CODE_FRONTEND_URL}/project/${projectId}/group/${auditData.group_id}/testSuit/${ts.test_suite_id}`}
                        className="block text-[#4E4EF6] hover:underline text-sm hover:text-blue-400 transition"
                      >
                        • {ts.test_suite_name}
                      </a>
                    );
                })}
              </div>
            </div>

            <div className="pt-4">
              <button
                onClick={handleDownloadPdf}
                className="bg-[#4E4EF6] hover:bg-[#3F3FF0] text-white text-sm font-medium py-2 px-4 rounded-lg transition"
              >
                Download PDF
              </button>
            </div>
          </div>
        </>
      )} */}
    </div>
  );
};

export default ApiAuditReportPage;
