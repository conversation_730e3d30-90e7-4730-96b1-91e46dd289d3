"use client";
import ApiAuditOnboarding from "@/components/api-audit/ApiAuditOnboarding";
import FloatingDropdown from "@/components/common/floating-dropdown/FloatingDropdown";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import axios from "axios";
import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import { FiFileText } from "react-icons/fi";
import { IoChevronDownOutline } from "react-icons/io5";
import { LuUserRound } from "react-icons/lu";
import LogoutButton from "../../logout-button/LogoutButton";

const STEPS = [
  {
    icon: "/fileSend2.svg",
    title: "Upload JSON/Postman Collection",
    description: "Drag & drop or paste your collection.",
    stepIcon: "/1.svg",
  },
  {
    icon: "/Checklist.svg",
    title: "Select APIs to Audit",
    description: "Choose specific endpoints from the collection.",
    stepIcon: "/2.svg",
  },
  {
    icon: "/fileSend2.svg",
    title: "Add Environment Variables",
    description: "Configure key-value pairs used in the API.",
    stepIcon: "/3.svg",
  },
  {
    icon: "/Checklist.svg",
    title: "Generate Audit Report",
    description:
      "Instantly get an AI-powered analysis with issues and recommendations.",
    stepIcon: "/4.svg",
  },
];
const ApiAuditPage = () => {
  const { projectId } = useParams();
  const [auditReports, setAuditReports] = useState([]);

  const fetchProjectAuditReports = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/audit/project/${projectId}`
      );
      console.log("log:: audit ", response.data);
      if (response.data.success) {
        setAuditReports(response.data.data);
      }
    } catch (error) {
      setAuditReports([]);
    }
  };

  useEffect(() => {
    if (!projectId) return;

    fetchProjectAuditReports();
  }, []);

  return (
    <div>
      {/* <div className="flex justify-between items-center p-4 py-1 bg-drCodeDarkBlue">
        <Image
          className="cursor-pointer"
          src={`/primary_logo.svg`}
          alt="dr-code-logo"
          width={120}
          height={120}
        />

        <FloatingDropdown
          buttonContent={
            <div className="flex items-center gap-1 cursor-pointer">
              <div className="h-10 w-10 rounded-full bg-drcodeBlue flex items-center justify-center">
                <LuUserRound />
              </div>
              <IoChevronDownOutline />
            </div>
          }
        >
          <LogoutButton />
        </FloatingDropdown>
      </div> */}

      <div className="h-screen pb-20 overflow-auto">
        <div className="flex flex-col justify-center items-center mt-5">
          <Image
            src={"/api-audit.svg"}
            height={48}
            width={48}
            alt="API AUDIT"
          />
          <h1 className="text-3xl text-[#D1D1E3] font-semibold mt-4">
            API Auditing
          </h1>
          <p className="mt-2 text-[#9494A1] font-normal text-sm">
            Audit your APIs effortlessly. Upload, configure, and get detailed
            insights in minutes.
          </p>

          <div className="mt-3 w-[550px]">
            {auditReports?.length === 0 && (
              <>
                <p>Process overview</p>
                <div className="bg-[#0D0D22] p-2 rounded-lg mt-2">
                  {STEPS.map((step, index) => (
                    <div
                      key={index}
                      className={`p-2 rounded-md flex gap-6 ${
                        index === 0 ? "bg-[#1B1B41]" : ""
                      }`}
                    >
                      <Image
                        src={step.icon}
                        height={42}
                        width={42}
                        alt={step.title}
                      />
                      <div>
                        <div className="text-[#E2E2ED] text-sm font-bold">
                          {step.title}
                        </div>
                        <div className="text-[#9494A1] text-[13px] font-normal">
                          {step.description}
                        </div>
                      </div>
                      <Image
                        src={step.stepIcon}
                        height={44}
                        width={22}
                        alt={`Step ${index + 1}`}
                        className="ml-auto mr-10"
                      />
                    </div>
                  ))}
                </div>
              </>
            )}
            <div className="flex justify-center">
              <ApiAuditOnboarding isFresh={auditReports?.length === 0 || !auditReports?.length}/>
            </div>

            <div className="mt-8 w-full p-4">
              <h2 className="text-sm font-semibold text-[#B2B2C1] mb-4">
                Audit History
              </h2>

              <div className="max-h-[400px] p-7 bg-[#0D0D22] rounded-2xl overflow-auto space-y-3">
                {auditReports?.length ? (
                  auditReports.map((audit) => (
                    <Link
                      key={audit.id}
                      href={`/project/${projectId}/api-audit/${audit.id}`}
                      className="flex items-center gap-3 p-4 hover:bg-[#2E2E60] border border-[#2E2E60] rounded-lg transition-colors"
                    >
                      <FiFileText className="w-5 h-5 text-[#4E4EF6]" />
                      <span className="text-sm text-[#E4E4EB]">
                        {audit.name}
                      </span>
                    </Link>
                  ))
                ) : (
                  <div className="text-sm text-[#A1A1AA]">
                    No previous audit reports found.
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiAuditPage;
