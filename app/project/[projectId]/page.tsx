"use client";
import ProjectList from "@/components/common/project-list/ProjectList";

import { getDataFromLocalStorage } from "@/temp-utils/globalUtilities";
import {
  LOCAL_STORAGE_DATA_KEYS,
  UserData,
} from "@/temp-utils/Constants/localStorageDataModels";
import AnalyticsComponent from "@/components/analytics/analyticsComponent";



export default function ProjectDetailsPage({ params }: any) {
  const userData: UserData = getDataFromLocalStorage(
    LOCAL_STORAGE_DATA_KEYS.USER_DATA
  ) as UserData;

  return (
    <div className="h-screen flex w-full">
      <div className="p-6 w-full  h-screen overflow-auto">
        <div className="mb-8 flex justify-between items-center">
          <p className="text-[#D1D1E3] title--large font-bold text-2xl">
            Welcome back, {userData?.name || "User"}
          </p>


          <div className="flex gap-2">


            <ProjectList className="min-w-48" />
          </div>
        </div>

        <AnalyticsComponent />

      </div>
    </div>
  );
}
