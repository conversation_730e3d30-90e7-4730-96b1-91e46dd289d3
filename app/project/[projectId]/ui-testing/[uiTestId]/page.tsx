"use client";
import React, { useState } from "react";
import { RiTimerLine } from "react-icons/ri";
import { FiChevronLeft, FiPlus } from "react-icons/fi";
import { useParams, useRouter } from "next/navigation";
import ChatScreen from "../components/chatScreen";
import VideoScreen from "../components/videoScreen";

const history = [
    { id: 1, title: "Payment Page" },
    { id: 2, title: "Type Error: next is not a function" },
    { id: 3, title: "Error: This is a delayed error..." },
    { id: 4, title: "TypeError: Cannot read property..." },
    { id: 5, title: "Error: drcode wrapper working..." },
];

const UITestingPage = () => {
    const { projectId, uiTestId } = useParams();
    const router = useRouter();
    const [isSidebarOpen, setIsSidebarOpen] = useState(true);

    const handleOpenUiTest = (uiTest) => {
        router.push(`/project/${projectId}/ui-testing/${uiTest.id}`);
    };

    const handleCreateNewUiTest = () => {
        router.push(`/project/${projectId}/ui-testing`);
    };

    return (
        <div className="h-screen flex bg-[#080814]">
            <div className="flex-1 overflow-auto p-3">
                <div className="h-full bg-[#0D0D22] border border-[#1B1B41] rounded-lg flex">
                    {/* Sidebar (inside card) */}
                    <div className={`${isSidebarOpen ? "w-[250px]" : "w-0"} border-r border-[#1B1B41] bg-[#0D0D22] flex flex-col overflow-hidden transition-all duration-300 ease-in-out`}>
                        <div className="flex items-center justify-between p-4 border-b border-[#1B1B41] h-14">
                            <p className="text-lg font-semibold text-[#D1D1E3]">History</p>
                            <div className="flex items-center gap-1">
                                <button className="flex items-center gap-2 text-white bg-[#875BF8] hover:bg-drcodePurple/80 border border-drcodePurple rounded-md font-semibold text-sm"
                                    onClick={handleCreateNewUiTest}>
                                    <FiPlus size={20} />
                                </button>
                                <button
                                    onClick={() => setIsSidebarOpen(false)}
                                    className="hover:bg-[#1B1B41] p-1 rounded"
                                >
                                    <FiChevronLeft size={20} />
                                </button>
                            </div>
                        </div>
                        <div className="overflow-auto flex-1">
                            {history.map((item) => (
                                <div
                                    key={item.id}
                                    className={`p-3 border-b border-[#1B1B41] cursor-pointer hover:bg-[#1B1B41] text-[#D1D1E3] ${String(item.id) === String(uiTestId) ? 'bg-[#1B1B41]' : ''}`}
                                    onClick={() => handleOpenUiTest(item)}
                                >
                                    {item.title}
                                </div>
                            ))}
                        </div>
                    </div>
                    {/* Main Content (inside card) */}
                    <div className="flex-1 flex flex-col">
                        {/* Header */}
                        <div className="relative w-full flex items-center h-16 cursor-pointer">
                            <div 
                                onClick={() => setIsSidebarOpen(true)}
                                className="ml-4 py-2 px-2 text-[16px] text-drcodePurple font-semibold flex gap-2 items-center">
                                <RiTimerLine size={20} />
                                History
                            </div>
                            <p className="absolute left-1/2 -translate-x-1/2 text-md text-[#D1D1E3] font-bold">
                                {history.find(h => String(h.id) === String(uiTestId))?.title || 'Test'}
                            </p>
                        </div>
                        {/* Divider */}
                        <div className="border-b border-[#1B1B41] w-full"></div>
                        {/* Main Area: ChatScreen and VideoScreen side by side */}
                        <div className="flex flex-1 flex-row justify-center items-start gap-4 p-6">
                            <div className="flex-1 max-w-[50%]"><ChatScreen /></div>
                            <div className="border-r border-[#1B1B41] h-[100%] p-0"></div>
                            <div className="flex-1 max-w-[50%]"><VideoScreen /></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default UITestingPage;
