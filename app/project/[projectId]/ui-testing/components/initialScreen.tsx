import { FaCheckCircle } from "react-icons/fa";

const InitialScreen = () => {
    return (
        <div className="flex flex-col justify-center items-center mt-10">
            <p className="text-[40px]">🧪</p>
            <h1 className="text-xl text-[#D1D1E3] font-semibold mt-4">
                UI Testing (Beta)
            </h1>
            <p className="mt-2 text-[#9494A1] font-normal text-[18px]">
                Test your website’s UI by describing the task. We’ll simulate the flow,
            </p>
            <p className="text-[#9494A1] font-normal text-[18px]">
                show a playback, and give you a detailed QA report.
            </p>
            <div className="mt-3 w-[40vw]">
                <div className="mt-8 w-full p-2">
                    <div className="max-h-[400px] p-5 bg-[#11112C] border border-[#1B1B41] rounded-2xl overflow-auto space-y-3">
                        <p className="text-[#D9D9E8] text-[18px] font-semibold">What Can I Test?</p>
                        <div className="flex justify-between gap-4">
                            <div>
                                <p className="text-[#B2B2C1] text-[16px] font-semibold">You can test:</p>
                                <ul>
                                    {[
                                        "Login or signup flows",
                                        "Form submissions",
                                        "Button clicks and redirection",
                                        "Verifying visible elements on a page",
                                        "Navigation to certain sections",
                                    ].map((text, i) => (
                                        <li key={i}>
                                            <p className="flex items-center gap-2 mt-2">
                                                <FaCheckCircle color="#15B097" />
                                                <span className="text-[#B2B2C1] text-[14px] font-normal">{text}</span>
                                            </p>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                            <div>
                                <p className="text-[#B2B2C1] text-[16px] font-semibold">To get the best results, include:</p>
                                <ul className="space-y-2">
                                    {[
                                        "The website URL",
                                        "User credentials (if required)",
                                        "A clear task in plain English",
                                    ].map((text, i) => (
                                        <li key={i}>
                                            <p className="flex items-center gap-2 mt-2">
                                                <FaCheckCircle color="#15B097" />
                                                <span className="text-[#B2B2C1] text-[14px] font-normal">{text}</span>
                                            </p>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="flex gap-2 items-center w-[40vw] mt-10">
                <div className="relative w-full">
                    <textarea
                        placeholder="Describe the UI task you want to test..."
                        className="w-full p-2 pr-12 text-sm text-[#E2E2ED] bg-[#1B1B41] border border-[#2E2E60] focus:border-[#2E2E60] rounded-md h-[100px] resize-none"
                        autoFocus
                        style={{ boxShadow: "0px 8px 16px 0px #00000066" }}
                    />
                    <button
                        className="absolute bottom-4 right-2 bg-[#875BF8] rounded-lg p-2 text-white text-sm"
                        style={{ lineHeight: 0 }}
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-6 h-6">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M6 12 3.269 3.126A59.768 59.768 0 0 1 21.485 12 59.768 59.768 0 0 1 3.27 20.874L5.999 12Zm0 0h7.5" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    )
}

export default InitialScreen;

