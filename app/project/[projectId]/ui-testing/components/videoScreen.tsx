import { FaCheckCircle } from "react-icons/fa";

const VideoScreen = () => {
    return (
        <div className="flex flex-col gap-4">
            <img
                src="/video_thumbnail2.png"
                alt="Dr Code AI automation for software development lifecycle"
                className="rounded-xl w-full h-full object-cover border-4 border-[#6B6BAA]"
            // onClick={handlePlayPauseClick}
            />
            <div className="flex justify-center items-center gap-2">
                {/* Rewind */}
                <div className="bg-[#2E2E60] rounded-full p-2 cursor-pointer">
                        <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.62488 5.9348C2.43052 6.81168 1.83333 7.25012 1.83333 8C1.83333 8.74988 2.43051 9.18832 3.62488 10.0652C3.95459 10.3073 4.2816 10.5352 4.5821 10.7251C4.84573 10.8917 5.14429 11.064 5.45341 11.2332C6.64497 11.8853 7.24075 12.2114 7.77509 11.8504C8.30944 11.4894 8.35801 10.7336 8.45513 9.22215C8.4826 8.7947 8.5 8.37566 8.5 8C8.5 7.62434 8.4826 7.2053 8.45513 6.77785C8.35801 5.26636 8.30944 4.51061 7.77509 4.1496C7.24075 3.78859 6.64497 4.11466 5.45341 4.76679C5.14429 4.93597 4.84573 5.10831 4.5821 5.27492C4.2816 5.46483 3.95459 5.69274 3.62488 5.9348Z" stroke="#B2B2C1" stroke-width="1.5" />
                            <path d="M8.56244 5.5906C9.00908 5.30819 9.45207 5.0423 9.85914 4.82074C10.2163 4.62636 10.6207 4.42529 11.0394 4.22792C12.6536 3.4671 13.4607 3.08669 14.1845 3.50786C14.9084 3.92904 14.9742 4.81075 15.1057 6.57416C15.1429 7.07285 15.1665 7.56173 15.1665 8C15.1665 8.43827 15.1429 8.92715 15.1057 9.42584C14.9742 11.1893 14.9084 12.071 14.1845 12.4921C13.4607 12.9133 12.6536 12.5329 11.0394 11.7721C10.6207 11.5747 10.2163 11.3736 9.85914 11.1793C9.45207 10.9577 9.00908 10.6918 8.56244 10.4094" stroke="#B2B2C1" stroke-width="1.5" />
                        </svg>
                </div>
                {/* Play */}
                <div className="bg-[#2E2E60] rounded-full p-2 cursor-pointer">
                    <svg width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10.2752 4.55806C12.4251 6.01952 13.5 6.75025 13.5 8.00005C13.5 9.24986 12.4251 9.98059 10.2752 11.442C9.68174 11.8455 9.09312 12.2253 8.55222 12.5419C8.07769 12.8195 7.54027 13.1068 6.98387 13.3887C4.83906 14.4756 3.76666 15.0191 2.80483 14.4174C1.843 13.8157 1.75559 12.5561 1.58076 10.037C1.53132 9.32455 1.5 8.62615 1.5 8.00005C1.5 7.37395 1.53132 6.67555 1.58076 5.96313C1.75559 3.44398 1.843 2.1844 2.80483 1.58272C3.76666 0.981034 4.83906 1.52448 6.98387 2.61137C7.54027 2.89333 8.07769 3.18057 8.55222 3.45825C9.09312 3.77477 9.68174 4.15462 10.2752 4.55806Z" stroke="#B2B2C1" stroke-width="1.5" />
                    </svg>
                </div>
                {/* Forward */}
                <div className="bg-[#2E2E60] rounded-full p-2 cursor-pointer">
                    <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M13.3751 5.9348C14.5695 6.81168 15.1667 7.25012 15.1667 8C15.1667 8.74988 14.5695 9.18832 13.3751 10.0652C13.0454 10.3073 12.7184 10.5352 12.4179 10.7251C12.1543 10.8917 11.8557 11.064 11.5466 11.2332C10.355 11.8853 9.75925 12.2114 9.22491 11.8504C8.69056 11.4894 8.64199 10.7336 8.54487 9.22215C8.5174 8.7947 8.5 8.37566 8.5 8C8.5 7.62434 8.5174 7.2053 8.54487 6.77785C8.64199 5.26636 8.69056 4.51061 9.22491 4.1496C9.75925 3.78859 10.355 4.11466 11.5466 4.76679C11.8557 4.93597 12.1543 5.10831 12.4179 5.27492C12.7184 5.46483 13.0454 5.69274 13.3751 5.9348Z" stroke="#B2B2C1" stroke-width="1.5" />
                        <path d="M8.43756 5.5906C7.99092 5.30819 7.54793 5.0423 7.14086 4.82074C6.78374 4.62636 6.37929 4.42529 5.96055 4.22792C4.34641 3.4671 3.53934 3.08669 2.81549 3.50786C2.09163 3.92904 2.02585 4.81075 1.89428 6.57416C1.85707 7.07285 1.8335 7.56173 1.8335 8C1.8335 8.43827 1.85707 8.92715 1.89428 9.42584C2.02585 11.1893 2.09163 12.071 2.81549 12.4921C3.53934 12.9133 4.34641 12.5329 5.96055 11.7721C6.37929 11.5747 6.78374 11.3736 7.14086 11.1793C7.54793 10.9577 7.99092 10.6918 8.43756 10.4094" stroke="#B2B2C1" stroke-width="1.5" />
                    </svg>
                </div>
                {/* Replay */}
                <div className="bg-[#2E2E60] rounded-full p-2 cursor-pointer">
                    <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6.8335 13.4167C7.24771 13.4167 7.5835 13.0809 7.5835 12.6667C7.5835 12.2525 7.24771 11.9167 6.8335 11.9167V13.4167ZM7.8335 3.33333V4.08333C8.13684 4.08333 8.41032 3.9006 8.52641 3.62035C8.64249 3.34009 8.57832 3.0175 8.36383 2.803L7.8335 3.33333ZM7.03049 1.46967C6.7376 1.17678 6.26273 1.17678 5.96983 1.46967C5.67694 1.76256 5.67694 2.23744 5.96983 2.53033L7.03049 1.46967ZM6.8335 12.6667V11.9167H6.50035V12.6667V13.4167H6.8335V12.6667ZM6.50016 3.33333V4.08333H7.8335V3.33333V2.58333H6.50016V3.33333ZM7.8335 3.33333L8.36383 2.803L7.03049 1.46967L6.50016 2L5.96983 2.53033L7.30317 3.86366L7.8335 3.33333ZM1.8335 8H1.0835C1.0835 10.9916 3.50884 13.4167 6.50035 13.4167V12.6667V11.9167C4.33719 11.9167 2.5835 10.1631 2.5835 8H1.8335ZM1.8335 8H2.5835C2.5835 5.83688 4.33705 4.08333 6.50016 4.08333V3.33333V2.58333C3.50862 2.58333 1.0835 5.00846 1.0835 8H1.8335Z" fill="#B2B2C1" />
                        <path d="M9.1665 12.6667V11.9167C8.86316 11.9167 8.58968 12.0994 8.47359 12.3797C8.35751 12.6599 8.42168 12.9825 8.63617 13.197L9.1665 12.6667ZM9.96951 14.5303C10.2624 14.8232 10.7373 14.8232 11.0302 14.5303C11.3231 14.2374 11.3231 13.7626 11.0302 13.4697L9.96951 14.5303ZM10.1665 2.58333C9.75229 2.58333 9.4165 2.91912 9.4165 3.33333C9.4165 3.74755 9.75229 4.08333 10.1665 4.08333V2.58333ZM10.4998 12.6667V11.9167H9.1665V12.6667V13.4167H10.4998V12.6667ZM9.1665 12.6667L8.63617 13.197L9.96951 14.5303L10.4998 14L11.0302 13.4697L9.69683 12.1363L9.1665 12.6667ZM10.1665 3.33333V4.08333H10.4998V3.33333V2.58333H10.1665V3.33333ZM15.1665 8H14.4165C14.4165 10.1631 12.663 11.9167 10.4998 11.9167V12.6667V13.4167C13.4914 13.4167 15.9165 10.9915 15.9165 8H15.1665ZM15.1665 8H15.9165C15.9165 5.00846 13.4914 2.58333 10.4998 2.58333V3.33333V4.08333C12.663 4.08333 14.4165 5.83688 14.4165 8H15.1665Z" fill="#B2B2C1" />
                    </svg>
                </div>
            </div>

        </div>
    )
}

export default VideoScreen;

