import { useState, useRef, useEffect } from "react";
import { FaCheckCircle } from "react-icons/fa";

const ChatScreen = () => {
    const [message, setMessage] = useState("");
    const [messages, setMessages] = useState<{ sender: "user" | "bot"; text: string }[]>([]);
    const chatEndRef = useRef<HTMLDivElement | null>(null);

    const handleSendMessage = () => {
        if (!message.trim()) return;
        setMessages((prev) => [
            ...prev,
            { sender: "user", text: message }
        ]);
        setMessage("");
    };

    // Send hardcoded bot reply after user message
    useEffect(() => {
        if (messages.length === 0) return;
        const last = messages[messages.length - 1];
        if (last.sender === "user") {
            const timeout = setTimeout(() => {
                setMessages((prev) => [
                    ...prev,
                    { sender: "bot", text: "This is a hardcoded bot reply." }
                ]);
            }, 500);
            return () => clearTimeout(timeout);
        }
    }, [messages]);

    // Scroll to bottom on new message
    useEffect(() => {
        chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, [messages]);

    const handleChangeMessage = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setMessage(e.target.value);
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    return (
        <div className="flex flex-col h-[88vh] relative">
            <div className="flex-1 overflow-y-auto px-2 pb-32">
                {messages.map((msg, idx) => (
                    <div key={idx} className={`flex flex-col gap-2 mb-2 
                    ${msg.sender === "user" ? "items-end" : "items-start"}`}>
                        <div className="flex items-center justify-center">
                            <p className="text-sm text-[#D1D1E3]">
                                {msg.sender === "user" ? "You" : "DrCode"}
                            </p>
                        </div>
                        <div className={`rounded-xl px-4 py-2 max-w-[70%] text-sm 
                            ${msg.sender === "user" ? "bg-[#1B1B41] text-[#D1D1E3]" : "bg-[#131330] border border-[#1B1B41] text-[#D1D1E3]"}`}>
                            {msg.text}
                        </div>
                    </div>
                ))}
                <div ref={chatEndRef} />
            </div>
            <div className="sticky absolute bottom-0 left-0 w-full z-10">
                <div className="flex gap-2 items-center">
                    <div className="relative w-full">
                        <textarea
                            placeholder="Describe the UI task you want to test..."
                            className="w-full p-2 pr-12 text-sm text-[#E2E2ED] bg-[#1B1B41] border border-[#2E2E60] focus:border-[#2E2E60] rounded-xl h-[100px] resize-none"
                            autoFocus
                            style={{ boxShadow: "0px 8px 16px 0px #00000066" }}
                            value={message}
                            onChange={handleChangeMessage}
                            onKeyDown={handleKeyDown}
                        />
                        <button
                            className="absolute bottom-4 right-2 bg-[#875BF8] rounded-lg p-2 text-white text-sm"
                            style={{ lineHeight: 0 }}
                            onClick={handleSendMessage}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-6 h-6 -rotate-45">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M6 12 3.269 3.126A59.768 59.768 0 0 1 21.485 12 59.768 59.768 0 0 1 3.27 20.874L5.999 12Zm0 0h7.5" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default ChatScreen;

