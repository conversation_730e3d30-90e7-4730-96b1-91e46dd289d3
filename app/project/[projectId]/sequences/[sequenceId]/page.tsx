"use client";
import { use<PERSON>arams } from "next/navigation";
import React, { JSX, useEffect, useRef, useState } from "react";
import useSequences from "../hooks/useSequences";
import PrimaryButtonOutlined from "@/components/common/buttons/PrimaryButtonOutlined";
import { CiPlay1 } from "react-icons/ci";
import ApiTestDetailedView from "@/components/test-runner/api-test-detailed-view/ApiTestDetailedView";
import axios from "axios";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import { E2EStep } from "../../group/[groupId]/testSuit/e2e/[e2eId]/page";
import useToast from "@/hooks/useToast";
import { handleSaveE2ERequest } from "@/temp-utils/e2eUtilities";
import {
  AssertionResults,
  TestAssertionEntity,
} from "@/components/test-runner/api-test-detailed-view/blocks/assertion-results/AssertionResults";
import { assert, expect } from "chai";
import { RiDeleteBack2Line, RiLoader2Fill, RiPencilFill } from "react-icons/ri";
import Link from "next/link";
import BottomSheet from "@/components/common/right-bottom-sheet/RightBottomSheet";
import VariableMapping from "../components/VariableMapping";
import PrimaryButton from "@/components/common/buttons/PrimaryButton";
import ToggleSwitch from "@/components/common/toggle-switch/ToggleSwitch";
import LoaderProgress from "@/components/common/loader-progress/LoaderProgress";
import FloatingDropdown from "@/components/common/floating-dropdown/FloatingDropdown";
import { RxGear } from "react-icons/rx";
import NonTechTabs from "../components/NonTechTabs";
import RightNavigation, {
  NavigationItem,
} from "@/components/test-runner/right-navigation/RightNavigation";
import AgentChat from "../components/AgentChat";
import AssertionChat from "@/components/agent-chat/assertions-chat/AssertionChat";
import { MentionsData } from "@/components/agent-chat/assertions-chat/SuggestionsInput";
import ApiRequestResponseOutputView from "@/components/common/api-request-response-output-view/ApiRequestResponseOutputView";
import { getAssertions } from "@/temp-utils/assertionsUtilities";
import TabList from "@/components/test-runner/api-test-detailed-view/blocks/tab-list/TabList";
import CustomAssertion from "../components/CustomAssertion";
import Accordion from "@/components/common/accordion/Accordion";
import { MdDeleteOutline } from "react-icons/md";
import { FaPlayCircle } from "react-icons/fa";

interface CustomAssertion {
  assertion: string;
  assertion_results: any;
  id: number;
}

const Sequence = () => {
  const [loading, setLoading] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const [sequenceData, setSequenceData] = useState(null);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [explainMode, setExplainMode] = useState(false);
  const [summaries, setSummaries] = useState([]);
  const [sequenceSummary, setSequenceSummary] = useState("");
  const [isAgentChatOpen, setIsAgentChatOpen] = useState(false);
  const { sequenceId, projectId } = useParams();
  const { showToast } = useToast();
  const intervalId = useRef<NodeJS.Timeout>(null);
  const summaryIntervalId = useRef<NodeJS.Timeout>(null);
  const [suggestionsOptions, setSuggestionsOptions] = useState<MentionsData[]>(
    []
  );
  const [mentionsData, setMentionsData] = useState<MentionsData[]>([]);
  const [expandedRow, setExpandedRow] = useState<number | null>(null);
  const [customAssertionPrompt, setCustomAssertionPrompt] = useState("");
  const [customAssertionsIds, setCustomAssertionsIds] = useState<string[]>([]);
  const [customAssertions, setCustomAssertions] = useState<CustomAssertion[]>(
    []
  );
  const [additionalMentions, setAdditionalMentions] = useState<MentionsData[]>(
    []
  );
  const [activeFilters, setActiveFilters] = useState<string[]>([
    "PASSED",
    "FAILED",
    "WARNING",
  ]);

  const {
    fetchSequenceById,
    setVariableMapped,
    mappingString,
    setMappingString,
    handleVariableMapping,
    loadingPercentage,
    handleRunOneSequence,
  } = useSequences();

  const getSequenceData = async () => {
    try {
      setLoading(true);
      const sequenceData = await fetchSequenceById(Number(sequenceId));

      let flowOptions: MentionsData[] = [];
      let stepOptions: MentionsData[] = [];

      setIsConnected(sequenceData?.connect);
      setMappingString(sequenceData?.variable_mappings_string || "");
      setVariableMapped(sequenceData?.variable_mappings || []);
      setSequenceData(sequenceData);
      setCustomAssertions(sequenceData?.regressionAssertions || []);
      setLoading(false);

      // extract flowname and flowid and step name and step id within the flows and store as key value pair array, it should be a single array of objects
      sequenceData?.flows?.map((flow) => {
        flowOptions.push({
          id: flow?.data[0].e2e_id,
          text: flow?.data?.[0]?.name,
          displayName: `${flow?.data?.[0]?.name} - ${flow?.data?.[0]?.e2e_id}`,
          type: "flow",
          sequenceId: sequenceData?.id,
        });
        flow?.data?.map((data) => {
          data?.scenarios?.map((scenario) => {
            scenario?.steps?.map((step) => {
              stepOptions.push({
                id: step.id,
                text: step.name,
                displayName: `${flow?.data?.[0]?.name} - ${step.name}`,
                type: "step",
                sequenceId: sequenceData?.id,
                flowId: data?.id,
                stepId: step.id,
              });
            });
          });
        });
      });

      // const formattedOptions = [...flowOptions, ...stepOptions]
      const formattedOptions = [...stepOptions];

      setSuggestionsOptions(formattedOptions);
    } catch (e) {
      console.log("log:: error is ", e);
      setLoading(false);
    }
  };

  useEffect(() => {
    getSequenceData();

    setAdditionalMentions([
      {
        id: sequenceId as string,
        text: sequenceId as string,
        type: "sequence",
        sequenceId: sequenceId as string,
        displayName: sequenceData?.name,
      },
    ]);

    return () => {
      clearInterval(intervalId?.current);
      clearInterval(summaryIntervalId?.current);
      summaryIntervalId.current = null;
    };
  }, [sequenceId]);

  const handleSaveRequest = async (testSuite: E2EStep, status?: string) => {
    setLoading(true);
    const response = await handleSaveE2ERequest(testSuite);
    setLoading(false);
    if (response?.success) {
      showToast("Request saved successfully", "success");
    } else {
      showToast("Failed to save request", "error");
    }
  };

  const modifyAssertionRow = (
    rowIndex: number,
    body: string,
    step: E2EStep,
    flowId?: string
  ) => {
    setSequenceData((prevSequenceData) => {
      return {
        ...prevSequenceData,
        flows: prevSequenceData?.flows?.map((flow) => {
          if (flow.id === flowId) {
            const updatedData = flow?.data?.map((dataEntry) => {
              const updatedScenarios = dataEntry?.scenarios?.map((scenario) => {
                const updatedSteps = scenario?.steps?.map((mStep) => {
                  if (mStep.id === step.id) {
                    return {
                      ...mStep,
                      assertions: body,
                      assertions_results: step.assertions_results || [],
                    };
                  }
                  return mStep;
                });

                return { ...scenario, steps: updatedSteps };
              });

              return { ...dataEntry, scenarios: updatedScenarios };
            });

            return { ...flow, data: updatedData };
          }
          return flow;
        }),
      };
    });
  };

  const modifyRequestBodyRow = (
    rowIndex: number,
    body: string, // stringified request object
    step: any,
    flowId?: number | string // flowId should be number (index)
  ) => {
    let parsedBody;

    try {
      parsedBody = JSON.parse(body); // full request object
    } catch (err) {
      console.error("Invalid JSON in request body", err);
      return;
    }

    const updatedSequenceData = {
      ...sequenceData,
      flows: sequenceData?.flows?.map((flow, flowIdx) => {
        if (flowIdx !== flowId) return flow;

        const updatedData = flow?.data?.map((dataEntry) => {
          const updatedScenarios = dataEntry?.scenarios?.map((scenario) => {
            const updatedSteps = scenario?.steps?.map((mStep) => {
              if (mStep.id === step.id) {
                const updatedData = parsedBody.url
                  ? { ...mStep, request: parsedBody, url: parsedBody.url }
                  : { ...mStep, request: parsedBody };
                // return {
                //   ...mStep,
                //   request: parsedBody, // Update request object
                //   url:parsedBody.url
                // };
                return updatedData;
              }
              return mStep;
            });

            return { ...scenario, steps: updatedSteps };
          });

          return { ...dataEntry, scenarios: updatedScenarios };
        });

        return { ...flow, data: updatedData };
      }),
    };

    setSequenceData(updatedSequenceData);
  };

  const runAssertionsLocally = (
    rowIndex: number,
    response: any,
    row?: any,
    flowId?: number | string
  ) => {
    const testCaseAssertion = (
      (row.assertions as any)?.assertions?.assertions?.assertions?.assertions ??
      (row.assertions as any)?.assertions?.assertions?.assertions ??
      (row.assertions as any)?.assertions?.assertions ??
      (row.assertions as any)?.assertions ??
      (row.assertions as any)
    )?.replace(/```/g, "");

    if (!testCaseAssertion?.trim()) return;

    const assertionResults: TestAssertionEntity[] = [];

    try {
      const it = (description: string, testFn: () => void) => {
        try {
          testFn();
          assertionResults.push({ message: description, passed: true });
        } catch (err: any) {
          assertionResults.push({ message: description, passed: false });
        }
      };

      const describe = (description: string, suiteFn: () => void) => {
        try {
          suiteFn();
        } catch (err) {
          console.error(`Error in describe block "${description}":`, err);
        }
      };

      const before = (fn: () => void) => {};
      const after = (fn: () => void) => {};
      const beforeEach = (fn: () => void) => {};
      const afterEach = (fn: () => void) => {};

      const testRunner = new Function(
        "it",
        "expect",
        "assert",
        "response",
        "describe",
        "before",
        "after",
        "beforeEach",
        "afterEach",
        testCaseAssertion
      );

      testRunner(
        it,
        expect,
        assert,
        response,
        describe,
        before,
        after,
        beforeEach,
        afterEach
      );
    } catch (err) {
      console.error("Test execution failed:", err);
    }

    const updatedSequenceData = {
      ...sequenceData,
      flows: sequenceData?.flows?.map((flow, flowIdx) => {
        if (flowIdx !== Number(flowId)) return flow;

        const updatedData = flow?.data?.map((dataEntry) => {
          const updatedScenarios = dataEntry?.scenarios?.map((scenario) => {
            const updatedSteps = scenario?.steps?.map((mStep) => {
              if (mStep.id === row.id) {
                return {
                  ...mStep,
                  assertions_results: assertionResults,
                  running: false,
                };
              }
              return mStep;
            });

            return { ...scenario, steps: updatedSteps };
          });

          return { ...dataEntry, scenarios: updatedScenarios };
        });

        return { ...flow, data: updatedData };
      }),
    };

    setSequenceData(updatedSequenceData);
    modifyAssertionResultsRow(assertionResults, row, flowId?.toString());
  };

  const modifyAssertionResultsRow = async (
    result: TestAssertionEntity[],
    step: E2EStep,
    flowId?: string
  ) => {
    const updatedSequenceData = {
      ...sequenceData,
      flows: sequenceData?.flows?.map((flow, flowIdx) => {
        if (flowIdx !== Number(flowId)) return flow;

        const updatedData = flow?.data?.map((dataEntry) => {
          const updatedScenarios = dataEntry?.scenarios?.map((scenario) => {
            const updatedSteps = scenario?.steps?.map((mStep) => {
              if (mStep.id === step.id) {
                return {
                  ...mStep,
                  assertions_results: structuredClone(result),
                  running: false,
                };
              }
              return mStep;
            });

            return { ...scenario, steps: updatedSteps };
          });

          return { ...dataEntry, scenarios: updatedScenarios };
        });

        return { ...flow, data: updatedData };
      }),
    };
    // Prepare the updated step data
    const updatedStep = {
      ...step,
      assertions_results: result,
      running: false,
    };

    // Save to server
    try {
      const updateUrl = `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.UPDATE_E2E_BY_E2E_STEP_ID}/${updatedStep.id}`;
      await axios.put(updateUrl, {
        assertions: { assertions: updatedStep.assertions },
        assertions_results: updatedStep.assertions_results.map((item) =>
          JSON.stringify(item)
        ),
      });
      await getSequenceData();
    } catch (error) {
      console.error("Error updating assertions:", error);
    }
  };

  const handleRunAgain = async () => {
    try {
      setIsRunning(true);
      const response = await handleRunOneSequence(sequenceData?.id);
      setIsRunning(false);

      if (response.success) {
        showToast("Sequence run successfully", "success");
      } else {
        showToast(response.message, "error");
      }
      await getSequenceData();
    } catch (error) {
      console.error("Error running sequence:", error);
      setIsRunning(false);
      showToast("Failed to run sequence", "error");
      await getSequenceData();
    }
  };

  const handleEditVariableMappingModal = () => {
    setIsSheetOpen(true);
  };

  const handleUpdateVariableMapping = async () => {
    // await setVariableMapped();
    setIsSheetOpen(false);
    await handleVariableMapping();
  };

  const handleConnectData = async (value) => {
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.TOGGLE_CONNECT_FOR_REGRESSION_TEST}/${sequenceData?.id}`
      );

      if (response?.data?.success) {
        setIsConnected(response?.data?.data?.connect);

        if (response?.data?.data?.connect) {
          showToast("Data connected successfully", "success");
        } else {
          showToast("Data disconnected successfully", "success");
        }
      }
    } catch (error) {
      console.error("Error connecting data:", error);
      showToast("Failed to connect data", "error");
    }
  };

  const fetchSummaries = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GET_STEP_SUMMARIES}?type=regression&id=${sequenceData?.id}`
      );

      // Process summaries
      const stepsSummaries =
        response?.data?.data?.steps_summaries?.map((item) => {
          return {
            id: item.id,
            summary: item?.summary?.text,
            e2eId: item.e2e_id,
            step_number: item.step_number,
          };
        }) || [];

      setSummaries(stepsSummaries);
      setSequenceSummary(
        response.data?.data?.regression_summary?.summary?.text || ""
      );

      // Handle polling
      const status = response.data.data?.summary_status;
      if (status === "GENERATING" && !summaryIntervalId.current) {
        summaryIntervalId.current = setInterval(fetchSummaries, 5000);
      }

      // Stop polling once summaries are ready
      if (status !== "GENERATING" && summaryIntervalId.current) {
        clearInterval(summaryIntervalId.current);
        summaryIntervalId.current = null;
      }
    } catch (error) {
      console.error("Error fetching summaries:", error);
    }
  };

  const handleExplainMode = async (value) => {
    try {
      setExplainMode(value);
      if (value === false) return;

      await fetchSummaries();
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/regressionTest/${sequenceData?.id}/explanations`
      );

      if (response.data.status === "RUNNING") {
        // start Polling fetch sequence in 5s interval
        intervalId.current = setInterval(async () => {
          await fetchSequenceById(sequenceData?.id);
        }, 5000);
      }
    } catch (e) {
      console.log("log:: error is ", e);
    }
  };

  const handleGenerateAssertions = async () => {
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GENERATE_CUSTOM_ASSERTIONS}`,
        {
          regressionTestId: sequenceData?.id,
          apiIds: customAssertionsIds?.map((id) => Number(id)),
          userPrompt: customAssertionPrompt,
        }
      );
      console.log("log:: response is ", response);
      if (response.data.success) {
        setCustomAssertions([
          ...customAssertions,
          {
            id: response.data.data.id,
            assertion: response.data.data.assertions?.assertions,
            assertion_results:
              response.data.data?.assertions?.assertion_results,
          },
        ]);
        setCustomAssertionPrompt("");
        setCustomAssertionsIds([]);
        showToast("Assertions generated successfully", "success");
        setIsAgentChatOpen(false);
      }
    } catch (e) {
      showToast("Failed to generate assertions", "error");
    }
  };

  const handleDeleteAssertion = async (id: number) => {
    try {
      const response = await axios.delete(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.DELETE_CUSTOM_ASSERTION}/${id}`
      );

      if (response.data.success) {
        showToast("Assertion deleted successfully", "success");
        setCustomAssertions(
          customAssertions.filter((assertion) => assertion.id !== id)
        );
      } else {
        showToast("Failed to delete assertion", "error");
      }
    } catch (e) {
      showToast("Failed to delete assertion", "error");
    }
  };

  const navigationItems: NavigationItem[] = [
    {
      icon: "chat-icon.svg",
      title: "DrCode Chat",
      onClick: () => {
        setIsAgentChatOpen(true);
      },
    },
  ];

  const toggleFilter = (status: string) => {
    setActiveFilters((prev) => {
      // If clicking the same status that's already the only one selected, show all
      if (prev.length === 1 && prev.includes(status)) {
        return ["PASSED", "FAILED", "WARNING"];
      }
      // Otherwise, show only the clicked status
      return [status];
    });
  };

  const getFilteredSteps = (steps: any[]) => {
    if (!steps || activeFilters.length === 0) return steps || [];
    return steps.filter((step) => activeFilters.includes(step.status));
  };

  const getTestStatusCounts = (steps: any[]) => {
    if (!steps || steps.length === 0)
      return { passed: 0, failed: 0, warning: 0, total: 0 };

    const counts = steps.reduce(
      (acc, step) => {
        acc.total++;
        if (step.status === "PASSED") acc.passed++;
        else if (step.status === "FAILED") acc.failed++;
        else if (step.status === "WARNING") acc.warning++;
        return acc;
      },
      { passed: 0, failed: 0, warning: 0, total: 0 }
    );

    return counts;
  };

  const getEmptyStateMessage = (steps: any[], currentFilters: string[]) => {
    const counts = getTestStatusCounts(steps);

    // If only one filter is selected
    if (currentFilters.length === 1) {
      const selectedFilter = currentFilters[0];

      if (selectedFilter === "FAILED") {
        if (counts.failed === 0) {
          if (counts.warning > 0) {
            return {
              type: "success",
              message:
                "No failed tests in this flow! All tests either passed or have warnings.",
            };
          } else {
            return {
              type: "success",
              message: "All tests passed in this flow",
            };
          }
        }
      }

      if (selectedFilter === "WARNING") {
        if (counts.warning === 0) {
          if (counts.failed > 0) {
            return {
              type: "info",
              message:
                "This flow doesn't have any warning tests, but it has some failed tests.",
            };
          } else {
            return {
              type: "success",
              message: "All tests passed in this flow",
            };
          }
        }
      }

      if (selectedFilter === "PASSED") {
        if (counts.passed === 0) {
          return {
            type: "info",
            message:
              "This flow doesn't have any passed tests. Check failed or warning filters.",
          };
        }
      }
    }

    // Default filter message
    return {
      type: "default",
      message:
        "No steps match the current filter. Try adjusting your filter settings.",
    };
  };

  const toggleRow = (id: number) => {
    setExpandedRow((prev) => (prev === id ? null : id));
  };

  const handleAssertionChange = async (id: number, assertion: string) => {
    try {
      const response = await axios.put(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.UPDATE_CUSTOM_ASSERTION}`,
        {
          assertionId: id,
          updatedAssertion: assertion,
        }
      );
      if (response.data.success) {
        showToast("Assertion updated successfully", "success");
        await getSequenceData();
      } else {
        showToast("Failed to update assertion", "error");
      }
    } catch (e) {
      showToast("Failed to update assertion", "error");
    }
  };

  if (!sequenceData && !loading) return null;

  return (
    <>
      <div className="flex">
        <div className="py-4 px-8 h-screen w-full overflow-auto">
          {loading && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-10">
              <RiLoader2Fill className="animate-spin text-white text-xl" />
            </div>
          )}

          {isRunning && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-10">
              <LoaderProgress progress={loadingPercentage} />
            </div>
          )}

          {/* Header */}
          <div className="flex justify-between items-center">
            <div className="max-w-[50%]">
              <h1 className="text-[#D1D1E3] font-semibold text-[24px]">
                Sequence: {sequenceData?.name}
              </h1>
              <p className="text-[#B2B2C1] text-[14px] font-normal">
                Detailed view of each step in this sequence including test
                results, data mapping, and shared variables
              </p>
            </div>

            <div className="flex gap-2 items-center h-full">
              <button
                  className="flex items-center gap-2 px-3 py-1.5 text-white bg-[#875BF8] hover:bg-drcodePurple/80 border border-drcodePurple rounded-md font-semibold text-sm"
                  onClick={handleRunAgain}
                >
                <FaPlayCircle />
                Run
              </button>
              <FloatingDropdown
                buttonContent={
                  <button className="button--outlined-primary py-2 px-2 text-[12px] flex gap-2">
                    <RxGear color="#875bf8" size={16} />
                  </button>
                }
              >
                <div className="flex flex-col gap-2">
                  <ToggleSwitch
                    // key={explainMode ? "on" : "off"}
                    disabled={false}
                    value={explainMode}
                    onToggle={(value) => handleExplainMode(value)}
                    label="Explain Mode"
                  />
                  <ToggleSwitch
                    // key={isConnected ? "on" : "off"}
                    disabled={false}
                    value={isConnected}
                    onToggle={(value) => handleConnectData(value)}
                    label="Connect Data"
                  />
                </div>
                {/* <div className="flex gap-2 items-center">

                <Tooltip
                  content="Link your data source to run tests with real values."
                  position="bottom"
                >
                  <IoMdInformationCircleOutline />
                </Tooltip>
              </div> */}
              </FloatingDropdown>
              <PrimaryButtonOutlined
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "5px",
                  padding: "5px 10px",
                  height: "36px",
                }}
                onClick={handleEditVariableMappingModal}
              >
                <RiPencilFill size={15} />
                Edit Mapping
              </PrimaryButtonOutlined>
              
            </div>
          </div>

          {/* Stats */}
          <div className="flex my-4 justify-between items-center gap-4">
            <div className="bg-[#131330] py-3 px-7 rounded-lg border-1 border-[#2E2E60] w-full">
              <div className="text-[#9494A1] text-[14px] font-normal">
                Total Flows
              </div>
              <div className="text-[#D1D1E3] text-[18px] font-semibold">
                {sequenceData?.flows?.length ?? 0}
              </div>
            </div>
            <div className="bg-[#131330] py-3 px-7 rounded-lg border-1 border-[#2E2E60] w-full">
              <div className="text-[#9494A1] text-[14px] font-normal">
                Last Run
              </div>
              <div className="text-[#D1D1E3] text-[18px] font-semibold">
                {sequenceData?.last_run_at
                  ? new Date(sequenceData?.last_run_at).toLocaleString()
                  : "Not Run"}
              </div>
            </div>
            <div className="bg-[#131330] py-3 px-7 rounded-lg border-1 border-[#2E2E60] w-full">
              <div className="text-[#9494A1] text-[14px] font-normal">
                Total Steps
              </div>
              <div className="text-[#D1D1E3] text-[18px] font-semibold">
                {sequenceData?.flows
                  ?.map((flow) =>
                    flow.data?.map((flowData) => flowData.steps?.length)
                  )
                  ?.flat()
                  ?.reduce((a, b) => a + b, 0) ?? 0}{" "}
              </div>
            </div>
          </div>

          {/* Filter Controls */}
          <div className="flex justify-between items-center my-4">
            <div className="text-[#D9D9E8] text-[16px] font-semibold">
              Filter Steps by Status:
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => toggleFilter("PASSED")}
                className={`px-3 py-1 rounded text-sm border transition-colors ${
                  activeFilters.includes("PASSED")
                    ? "bg-[#15B0974D] text-[#A4F4E7] border-[#15B097]"
                    : "bg-transparent text-[#9494A1] border-[#2E2E60] hover:border-[#15B097]"
                }`}
              >
                Passed
              </button>
              <button
                onClick={() => toggleFilter("FAILED")}
                className={`px-3 py-1 rounded text-sm border transition-colors ${
                  activeFilters.includes("FAILED")
                    ? "bg-[#C037444D] text-[#E4626F] border-[#C03744]"
                    : "bg-transparent text-[#9494A1] border-[#2E2E60] hover:border-[#C03744]"
                }`}
              >
                Failed
              </button>
              <button
                onClick={() => toggleFilter("WARNING")}
                className={`px-3 py-1 rounded text-sm border transition-colors ${
                  activeFilters.includes("WARNING")
                    ? "bg-[#EDA1454D] text-[#F4C790] border-[#EDA145]"
                    : "bg-transparent text-[#9494A1] border-[#2E2E60] hover:border-[#EDA145]"
                }`}
              >
                Warning
              </button>
              <button
                onClick={() =>
                  setActiveFilters(["PASSED", "FAILED", "WARNING"])
                }
                className="px-3 py-1 rounded text-sm border bg-transparent text-[#875bf8] border-[#875bf8] hover:bg-[#875bf8] hover:text-white transition-colors"
              >
                Show All
              </button>
            </div>
          </div>

          {/* Summary */}
          {explainMode && (
            <div className="p-2 rounded-md bg-drCodeDarkBlue">
              <div className="text-white">Regression Summary:</div>
              <div
                className="my-2 rounded-md whitespace-pre-wrap text-sm text-white bg-drcodeBlue p-2"
                dangerouslySetInnerHTML={{
                  __html:
                    sequenceSummary?.replace(/\n/g, "<br/>") ||
                    "Summary is being generated",
                }}
              >
                {/* {sequenceSummary} */}
              </div>
            </div>
          )}

          {/* Custom Assertions */}
          <>
            {customAssertions?.length > 0 && (
              <div className="my-4">
                <div className="text-[#D9D9E8] text-[16px] font-semibold">
                  Custom Assertions
                </div>
                {customAssertions?.map((assertion, idx) => {
                  const assertionResults = JSON.parse(
                    assertion.assertion_results ?? "[]"
                  );
                  return (
                    <div className="flex gap-2 items-center">
                      <div className="flex-1">
                        <Accordion
                          items={[
                            {
                              id: assertion.id?.toString() || "",
                              title: `Assertion #${idx + 1}`,
                              content: (
                                <CustomAssertion
                                  assertionId={assertion.id}
                                  assertion={assertion.assertion}
                                  assertionResults={assertion.assertion_results}
                                  handleAssertionChange={handleAssertionChange}
                                />
                              ),
                              statusColor:
                                assertionResults?.length === 0
                                  ? "bg-gray-400"
                                  : assertionResults?.some(
                                      (item: any) => item.success === false
                                    )
                                  ? "bg-red-400"
                                  : "bg-green-400",
                              deleteAssertion: (
                                <MdDeleteOutline
                                  cursor={"pointer"}
                                  onClick={() => handleDeleteAssertion(assertion.id)}
                                  size={16}
                                />
                              )
                            },
                          ]}
                        />
                      </div>

                      
                    </div>
                  );
                })}
              </div>
            )}
          </>

          {/* SEQUENCES */}

          {sequenceData?.flows?.map((flow, flowIdx) => {
            return (
              <div className="my-4" key={flowIdx}>
                {flow?.data?.map((flowData, flowDataIdx) => {
                  return (
                    <div key={flowDataIdx}>
                      <div className="">
                        <div className="flex gap-2 items-center">
                          <div className="text-[#D9D9E8] text-[16px] font-semibold">
                            Flow #{flowIdx + 1} {flowData?.name}
                          </div>
                          <Link
                            className=" text-[14px] font-normal underline"
                            target="_blank"
                            href={`/project/${projectId}/group/${flowData?.group_id}/testSuit/e2e/${flowData?.e2e_id}`}
                          >
                            (
                            {
                              getFilteredSteps(flowData?.scenarios?.[0]?.steps)
                                .length
                            }{" "}
                            Steps{" "}
                            {activeFilters.length < 3 ? "filtered" : "total"})
                          </Link>
                        </div>

                        <div className="mt-4 ml-4 border-1 border-[#1B1B41] rounded-lg overflow-hidden shadow">
                          <table className="w-full text-left border-1 border-[#1B1B41]">
                            <thead className="bg-transparent hidden">
                              <tr>
                                <th className="p-3"></th>
                                <th className="p-3"></th>
                                <th className="p-3"></th>
                              </tr>
                            </thead>
                            <tbody className="bg-[#0D0D22] border-1 border-[#1B1B41]">
                              {getFilteredSteps(flowData?.scenarios?.[0]?.steps)
                                .length === 0 ? (
                                <tr>
                                  <td colSpan={3} className="py-8 text-center">
                                    {(() => {
                                      const emptyMessage = getEmptyStateMessage(
                                        flowData?.scenarios?.[0]?.steps,
                                        activeFilters
                                      );

                                      if (emptyMessage.type === "success") {
                                        return (
                                          <div className="inline-flex items-center gap-2 px-4 py-2 bg-[#15B0974D] text-[#A4F4E7] rounded-md text-sm border border-[#15B097]">
                                            <div className="w-3 h-3 bg-[#15B097] rounded-full"></div>
                                            {emptyMessage.message}
                                          </div>
                                        );
                                      } else if (emptyMessage.type === "info") {
                                        return (
                                          <div className="inline-flex items-center gap-2 px-4 py-2 bg-[#EDA1454D] text-[#F4C790] rounded-md text-sm border border-[#EDA145]">
                                            <div className="w-3 h-3 bg-[#EDA145] rounded-full"></div>
                                            {emptyMessage.message}
                                          </div>
                                        );
                                      } else {
                                        return (
                                          <span className="text-[#9494A1]">
                                            {emptyMessage.message}
                                          </span>
                                        );
                                      }
                                    })()}
                                  </td>
                                </tr>
                              ) : (
                                getFilteredSteps(
                                  flowData?.scenarios?.[0]?.steps
                                ).map((step, index) => (
                                  <React.Fragment key={step.id}>
                                    <tr
                                      className="cursor-pointer hover:bg-[#131330] border-1 border-[#1B1B41] transition-colors"
                                      onClick={() => toggleRow(step.id)}
                                    >
                                      <td className="py-4 px-2 w-[70%]">
                                        <div className="text-white font-medium text-sm">
                                          Step #{step.step_number} {step.name}
                                        </div>
                                        <div className="text-[#9494A1] text-sm">
                                          {step.description}
                                        </div>
                                      </td>
                                      <td className="px-3">
                                        <>
                                          {step.status === "PASSED" ? (
                                            <div className="inline-block ml-2 px-2 py-1 bg-[#15B0974D] text-[#A4F4E7] rounded-md text-sm">
                                              Passed
                                            </div>
                                          ) : step.status === "FAILED" ? (
                                            <div className="inline-block ml-2 px-2 py-1 bg-[#C037444D] text-[#E4626F] rounded-md text-sm">
                                              Failed
                                            </div>
                                          ) : step.status === "WARNING" ? (
                                            <div className="inline-block ml-2 px-2 py-1 bg-[#EDA1454D] text-[#F4C790] rounded-md text-sm">
                                              Warning
                                            </div>
                                          ) : (
                                            <div className="inline-block ml-2 px-2 py-1 bg-vscode-editor-background text-gray-500 rounded-md text-sm">
                                              Not Run
                                            </div>
                                          )}
                                        </>
                                      </td>
                                      <td className="px-3">
                                        {!explainMode && (
                                          <span className="text-white bg-[#1B1B41] px-2 py-1 rounded text-sm">
                                            {step.response?.statusCode}
                                          </span>
                                        )}
                                      </td>
                                    </tr>
                                    {expandedRow === step.id && (
                                      <tr className="bg-[#131330] border-t overflow-auto">
                                        {explainMode ? (
                                          <td colSpan={3}>
                                            <NonTechTabs
                                              explanation={step.explanation}
                                              summary={
                                                summaries
                                                  ?.filter(
                                                    (item) =>
                                                      item.e2eId ===
                                                      flowData?.e2e_id
                                                  )
                                                  ?.filter(
                                                    (item) =>
                                                      item.step_number ===
                                                      step?.step_number
                                                  )?.[0]?.summary ??
                                                (step.status === "Passed"
                                                  ? "This API passed successfully"
                                                  : step.status === "Warning"
                                                  ? "API passed with some warnings, Please check the assertions result"
                                                  : "Summary is being generated")
                                              }
                                            />
                                          </td>
                                        ) : (
                                          <td
                                            colSpan={3}
                                            className="text-[12px]"
                                          >
                                            <ApiTestDetailedView
                                              apiTestDetails={{
                                                ...step,
                                                request: JSON.stringify(
                                                  {
                                                    ...step.request,
                                                    url: step.url,
                                                  },
                                                  null,
                                                  2
                                                ),
                                              }}
                                              generateAssertions={() => {}}
                                              handleSaveRequest={
                                                handleSaveRequest as any
                                              }
                                              idx={step.id}
                                              modifyAssertionRow={
                                                modifyAssertionRow as any
                                              }
                                              modifyRequestBodyRow={
                                                modifyRequestBodyRow
                                              }
                                              modifyResponseBodyRow={() => {}}
                                              runAssertionsLocally={
                                                runAssertionsLocally
                                              }
                                              running={false}
                                              isE2E={true}
                                              flowId={flowIdx}
                                              key={step}
                                            />
                                          </td>
                                        )}
                                      </tr>
                                    )}
                                  </React.Fragment>
                                ))
                              )}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            );
          })}

          {/* EDIT VARIABLE MAPPING */}
          {isSheetOpen && (
            <BottomSheet
              isOpen={isSheetOpen}
              onClose={() => setIsSheetOpen(false)}
            >
              <h3 className="my-3 text-[#D1D1E3] text-[16px] font-semibold">
                Update Variable Mapping
              </h3>
              <VariableMapping
                currentFlowsAndStepsData={sequenceData?.scenario_chain?.map(
                  (sc, index) => {
                    return {
                      order: index,
                      id: sc,
                    };
                  }
                )}
                data={sequenceData}
                setVariableMapped={setVariableMapped}
                mappingString={mappingString}
                setMappingString={setMappingString}
              />

              <div className="flex my-2 gap-2">
                <PrimaryButton onClick={handleUpdateVariableMapping}>
                  Save
                </PrimaryButton>
                <PrimaryButtonOutlined onClick={() => setIsSheetOpen(false)}>
                  Cancel
                </PrimaryButtonOutlined>
              </div>
            </BottomSheet>
          )}
        </div>
        <div className="min-w-[6%]">
          <RightNavigation navigationItems={navigationItems} />
        </div>
      </div>

      {isAgentChatOpen && (
        <BottomSheet
          widthInPercentage="35%"
          isOpen={isAgentChatOpen}
          onClose={() => setIsAgentChatOpen(false)}
        >
          {/* <AssertionChat
            suggestionsOptions={suggestionsOptions}
            prompt={customAssertionPrompt}
            setPrompt={setCustomAssertionPrompt}
            setPromptIds={setCustomAssertionsIds}
            customAssertionsIds={customAssertionsIds}
            handleGenerateAssertions={handleGenerateAssertions}
            setMentionsData={setMentionsData}
          /> */}
          <AgentChat
            handleCloseChat={() => setIsAgentChatOpen(false)}
            additionalMentions={additionalMentions}
          />
        </BottomSheet>
      )}
    </>
  );
};

export default Sequence;
