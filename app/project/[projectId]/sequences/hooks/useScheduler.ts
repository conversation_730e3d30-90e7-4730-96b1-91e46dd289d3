import useToast from "@/hooks/useToast";
import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import axios from "axios";
import { useParams } from "next/navigation";
import React, { useState } from "react";

const useScheduler = () => {
  const { showToast } = useToast();
  const { projectId } = useParams();

  const [emails, setEmails] = useState<string[]>([]);
  const [scheduleTime, setScheduleTime] = useState<string>("");
  const [scheduleType, setScheduleType] = useState<
    "DAILY" | "WEEKLY" | "MONTHLY" | "CUSTOM"
  >("DAILY");
  const [scheduleTypeOptions, setScheduleTypeOptions] = useState<
    { value: string; label: string }[]
  >([]);
  const [cronExpression, setCronExpression] = useState<string>("");
  const [dayOfMonth, setDayOfMonth] = useState<number>(1);
  const [dayOfWeek, setDayOfWeek] = useState<number>(1);
  const [enableScheduler, setEnableScheduler] = useState<boolean>(false);
  const [methodType, setMethodType] = useState<"CREATE" | "UPDATE">("CREATE");
  const [schedulerId, setSchedulerId] = useState<string>("");

  const weekOptions = [
    { value: "0", label: "Sunday" },
    { value: "1", label: "Monday" },
    { value: "2", label: "Tuesday" },
    { value: "3", label: "Wednesday" },
    { value: "4", label: "Thursday" },
    { value: "5", label: "Friday" },
    { value: "6", label: "Saturday" },
  ];

  function convertLocalTimeToGMT(time: string, timeZone?: string): string {
    // Fallback to user's current timezone if not provided
    const tz = timeZone || Intl.DateTimeFormat().resolvedOptions().timeZone;

    const [hours, minutes] = time.split(":").map(Number);

    // Use today's date to create a DateTime in the user's time zone
    const now = new Date();
    const dateInLocal = new Date(
      Date.UTC(now.getFullYear(), now.getMonth(), now.getDate(), hours, minutes)
    );

    // Convert local time to UTC by using the offset
    const localOffsetInMinutes = -new Date(
      dateInLocal.toLocaleString("en-US", { timeZone: tz })
    ).getTimezoneOffset();

    // Subtract the offset from the input time
    const utcDate = new Date(
      dateInLocal.getTime() - localOffsetInMinutes * 60 * 1000
    );

    // Get UTC hours and minutes
    const utcHours = utcDate.getUTCHours().toString().padStart(2, "0");
    const utcMinutes = utcDate.getUTCMinutes().toString().padStart(2, "0");

    return `${utcHours}:${utcMinutes}`;
  }

  function convertGMTToLocalTime(gmtTime: string, timeZone?: string): string {
    // Use user's current timezone if not provided
    const tz = timeZone || Intl.DateTimeFormat().resolvedOptions().timeZone;
  
    const [hours, minutes] = gmtTime.split(":").map(Number);
  
    // Create a UTC Date with today's date and provided GMT time
    const utcDate = new Date(Date.UTC(
      new Date().getFullYear(),
      new Date().getMonth(),
      new Date().getDate(),
      hours,
      minutes
    ));
  
    // Convert to local time using toLocaleString
    const localString = utcDate.toLocaleString("en-US", {
      timeZone: tz,
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
    });

    console.log("log:: local string",localString)
  
    return localString;
  }
  

  const handleSchedule = async ({
    user_id,
    project_id,
    schedule_type,
    time,
    emails,
    cron_expression,
    dayOfWeek,
    dayOfMonth,
    methodType,
  }: {
    user_id: number;
    project_id: string;
    schedule_type: "DAILY" | "WEEKLY" | "MONTHLY" | "CUSTOM";
    time: string;
    emails: string[];
    cron_expression: string;
    dayOfWeek: number;
    dayOfMonth: number;
    methodType: "CREATE" | "UPDATE";
  }) => {
    try {
      const gmtTime = convertLocalTimeToGMT(time);

      console.log("log:: gmt time is ", gmtTime);

      const scheduleBody = {
        user_id: user_id,
        project_id: project_id,
        schedule_type: schedule_type,
        schedule_config: {
          time: gmtTime ?? time,
          cronExpression: cron_expression,
          dayOfWeek: dayOfWeek,
          dayOfMonth: dayOfMonth,
        },
        emails: emails,
      };

      if (methodType === "UPDATE") {
        const response = await axios.put(
          `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.CREATE_SCHEDULER}/${schedulerId}`,
          scheduleBody
        );
        if (response.data.success) {
          showToast("Scheduler updated successfully", "success");
        }
        return {
          success: true,
        };
      } else {
        const response = await axios.post(
          `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.CREATE_SCHEDULER}`,
          scheduleBody
        );
        if (response.data.success) {
          showToast("Scheduler created successfully", "success");
        }
        return {
          success: true,
        };
      }
    } catch (error) {
      console.log("log:: error", error);
      showToast(
        error?.response?.data?.errors?.[0] || "Something went wrong",
        "error"
      );
      return {
        success: false,
      };
    }
  };

  const getScheduler = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/scheduler?project_id=${projectId}`
      );

      const schedulerData = response.data?.data?.[0];

      if (schedulerData) {
        setScheduleType(schedulerData.schedule_type);
        setScheduleTime(convertGMTToLocalTime(schedulerData.schedule_config.time));
        setCronExpression(schedulerData.schedule_config.cronExpression);
        setDayOfMonth(schedulerData.schedule_config.dayOfMonth);
        setDayOfWeek(schedulerData.schedule_config.dayOfWeek);
        setEmails(schedulerData.emails ?? []);
        setEnableScheduler(schedulerData.status === "ACTIVE");
        setMethodType("UPDATE");
        setSchedulerId(schedulerData.id);
      }
    } catch (error) {
      console.log("log:: error", error);
    }
  };

  const toggleScheduler = async () => {
    try {
      const response = await axios.put(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/scheduler/project/${projectId}/toggle`
      );
      if (response.data.success) {
        showToast("Scheduler toggled successfully", "success");
      }
    } catch (error) {
      console.log("log:: error", error);
      showToast("Something went wrong", "error");
    }
  };

  const getTypes = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GET_SCHEDULER_TYPES}`
      );

      const types = response.data?.data?.types;
      const typeOptions = Object.entries(types).map(([key, value]) => ({
        value: value as string,
        label: key,
      }));
      setScheduleTypeOptions(typeOptions);
    } catch (error) {
      setScheduleTypeOptions([
        { value: "DAILY", label: "DAILY" },
        { value: "WEEKLY", label: "WEEKLY" },
        { value: "MONTHLY", label: "MONTHLY" },
        { value: "CUSTOM", label: "CUSTOM" },
      ]);
    }
  };

  return {
    emails,
    scheduleTime,
    scheduleType,
    handleSchedule,
    setEmails,
    setScheduleTime,
    setScheduleType,
    getTypes,
    scheduleTypeOptions,
    cronExpression,
    setCronExpression,
    dayOfMonth,
    setDayOfMonth,
    weekOptions,
    dayOfWeek,
    setDayOfWeek,
    getScheduler,
    enableScheduler,
    setEnableScheduler,
    methodType,
    setMethodType,
    schedulerId,
    setSchedulerId,
    toggleScheduler,
  };
};

export default useScheduler;
