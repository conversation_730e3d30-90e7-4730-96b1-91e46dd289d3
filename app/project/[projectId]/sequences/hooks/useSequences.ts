"use client";
import { useState } from "react";
import { Flow } from "../types/sequences";
import axios from "axios";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import { useParams } from "next/navigation";
import useToast from "@/hooks/useToast";
import { useGlobalStore } from "@/stores/globalstore";

const LOADER_TIME = 400;

const useSequences = () => {
  const { projectId, sequenceId } = useParams();
  const { showToast } = useToast();

  const [flowName, setFlowName] = useState("");
  const [sequences, setSequences] = useState([]);
  const [flowOptions, setFlowOptions] = useState([]);
  const [flows, setFlows] = useState<Flow[]>([
    {
      id: "",
      order: 0,
    },
    {
      id: "",
      order: 1,
    },
  ]);
  const [variableMapped, setVariableMapped] = useState([]);
  const [mappingString, setMappingString] = useState("");
  const [creationStep, setCreationStep] = useState<"flow" | "variableMapping">(
    "flow"
  );
  const [regressionTestId, setRegressionTestId] = useState(null);
  const [loadingPercentage, setLoadingPercentage] = useState(0);
  const [envModal, setEnvModal] = useState(false);
  const [runTimeSettingsModal, setRunTimeSettingsModal] = useState(false);
  const [waitTime, setWaitTime] = useState(40000);

  const { setEnvironment } = useGlobalStore();

  const settingsOptionsList = [
    {
      name: "Edit Environment",
      id: 1,
      onClick: () => {
        // router.push(`/environment/${environment}`);
        setEnvModal(true);
      },
    },
    {
      name: "Edit Run time Settings",
      id: 2,
      onClick: () => setRunTimeSettingsModal(true),
    },
  ];

  const createSequences = async () => {
    try {
      const flowsIds = flows.map((flow) => flow.id);
      const flowBody = {
        projectId: projectId,
        name: flowName,
        description: "",
        scenario_chain: flowsIds,
      };

      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.CREATE_SEQUENCE}`,
        flowBody
      );

      // const response = createRegressionResponse;
      if (response.data.success) {
        showToast("Sequence created successfully", "success");
        setRegressionTestId(response.data?.data.id);

        setCreationStep("variableMapping");
      } else {
        showToast(response.data.error || "Something went wrong", "error");
      }

      // console.log(response.data);
    } catch (err) {
      console.log(err);
      showToast(err.response?.data?.error || "Something went wrong", "error");
    }
  };

  const handleVariableMapping = async () => {
    try {
      const requestBody = {
        regressionTestId: regressionTestId ?? sequenceId,
        variable_mappings: variableMapped,
        variable_mappings_string: mappingString,
      };

      console.log("log:: request body", requestBody);
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.VARIABLE_MAPPING}`,
        requestBody
      );
      if (response.data.success) {
        showToast("Variable mapping created successfully", "success");
        setCreationStep("flow");
        return response.data;
      } else {
        showToast(response.data.error || "Something went wrong", "error");
        return {
          success: false,
          data: null,
        };
      }
    } catch (e) {
      console.log(e);
      showToast("Something went wrong", "error");
      return {
        success: false,
        data: null,
      };
    }
  };

  const fetchAllFlows = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GET_E2E_BY_PROJECT_ID}/${projectId}`
      );

      setFlowOptions(response.data?.data ?? []);
    } catch (err) {
      console.log(err);
      setFlowOptions([]);
    }
  };

  const fetchAllSequences = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GET_ALL_SEQUENCES_BY_PROJECT_ID}/${projectId}`
      );

      if (response.data.success) {
        setSequences(response.data?.data);
        setEnvironment(response.data?.environment?.id);
      } else {
        showToast(response.data.error || "Something went wrong", "error");
      }
    } catch (err) {
      console.log(err);
      showToast(err.response?.data?.error || "Something went wrong", "error");
    }
  };

  const fetchSequenceById = async (sequenceId: number) => {
    try {
      if (!sequenceId) {
        showToast("Sequence ID is required", "error");
        return null;
      }
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GET_SEQUENCE_BY_ID}/${sequenceId}`
      );

      if (response.data.success) {
        return response.data?.data;
      } else {
        showToast(response.data.error || "Something went wrong", "error");
        return null;
      }
    } catch (err) {
      console.log(err);
      showToast(err.response?.data?.error || "Something went wrong", "error");
      return null;
    }
  };

  const handleRunAllSequencesByProjectId = async () => {
    setLoadingPercentage(0); // Reset
  
    let fakeProgress = 0;
    const progressInterval = setInterval(() => {
      fakeProgress += Math.random() * 10; // Random increment
      setLoadingPercentage((prev) => {
        const next = Math.min(95, Math.floor(fakeProgress)); // Max 95%
        return next;
      });
    }, LOADER_TIME); // Adjust speed
  
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.RUN_ALL_SEQUENCES}`,
        { projectId, waitTime }
      );
  
      clearInterval(progressInterval);
      setLoadingPercentage(100);
  
      if (response.data.success) {
        showToast("All sequences run successfully", "success");
      } else {
        showToast(response.data.error || "Something went wrong", "error");
      }
    } catch (err) {
      clearInterval(progressInterval);
      setLoadingPercentage(100);
      showToast(err.response?.data?.error || "Something went wrong", "error");
    } finally {
      // Optionally hide loader after a short delay
      setLoadingPercentage(100);
    }
  };
  
  const handleRunOneSequence = async (sequenceId) => {
    setLoadingPercentage(0);
    let fakeProgress = 0;
    let reachedFifty = false;
  
    const progressInterval = setInterval(() => {
      // Slow progress until 50%
      if (fakeProgress < 100) {
        fakeProgress += Math.random() * 2; // Slow speed
      } else {
        reachedFifty = true;
        fakeProgress += Math.random() * 10; // Faster after 50%
      }
  
      setLoadingPercentage(Math.min(95, Math.floor(fakeProgress)));
    }, LOADER_TIME);
  
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.RUN_ONE_SEQUENCE}`,
        {
          regressionTestId: sequenceId,
          waitTime: waitTime,
        }
      );
  
      clearInterval(progressInterval);
  
      if (!reachedFifty) {
        // If we haven't reached 50%, speed up quickly to 100%
        const fastProgress = setInterval(() => {
          fakeProgress += 10;
          setLoadingPercentage(Math.min(100, Math.floor(fakeProgress)));
          if (fakeProgress >= 100) clearInterval(fastProgress);
        }, 30);
      } else {
        setLoadingPercentage(100);
      }
  
      return response.data;
    } catch (e) {
      clearInterval(progressInterval);
      setLoadingPercentage(100);
      showToast(e.response?.data?.error || "Something went wrong", "error");
      return {
        success: false,
        data: null,
      };
    }
  };
  
  
  const fetchFlowsAndStepsByProjectId = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GET_FLOWS_AND_STEPS_BY_PROJECT_ID}/${projectId}`
      );
      return response.data?.data;
    } catch (err) {
      console.log(err);
      return [];
    }
  };

  return {
    createSequences,
    setFlows,
    sequences,
    flows,
    fetchAllFlows,
    fetchAllSequences,
    setSequences,
    flowOptions,
    handleVariableMapping,
    variableMapped,
    setVariableMapped,
    fetchSequenceById,
    flowName,
    setFlowName,
    creationStep,
    setCreationStep,
    regressionTestId,
    handleRunAllSequencesByProjectId,
    fetchFlowsAndStepsByProjectId,
    mappingString,
    setMappingString,
    loadingPercentage,
    handleRunOneSequence,
    envModal,
    setEnvModal,
    runTimeSettingsModal,
    setRunTimeSettingsModal,
    settingsOptionsList,
    waitTime,
    setWaitTime,
  };
};

export default useSequences;
