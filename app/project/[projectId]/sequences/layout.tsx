import TopLoader from "@/components/common/top-loader/TopLoader";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "All Sequences",
  description: "Dr Code: Experience AI-powered automation API Testing Platform",
  icons: {
    icon: "/logo.png",
    apple: "/logo.png",
  },
  manifest: "/manifest.json",
};

export default function SequencesLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: any;
}>) {
  return (
    <>
      <TopLoader />

      {children}
    </>
  );
}
