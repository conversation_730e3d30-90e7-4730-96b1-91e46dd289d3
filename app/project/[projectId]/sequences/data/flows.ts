export const flowResponse = {
  success: true,
  message: "Flows and steps retrieved successfully",
  data: [
    {
      flow_id: 1,
      flow_uuid: "a38fb022-24ab-4f8d-9281-6c754abfa986",
      flow_name: "User Login",
      flow_steps: [
        {
          step_number: 1,
          step_id: 1,
          step_name: "User Login",
        },
        {
          step_number: 2,
          step_id: 2,
          step_name: "Retrieve User Data",
        },
      ],
    },
    {
      flow_id: 2,
      flow_uuid: "9ca413ee-a02e-4afc-b09b-24b349c998db",
      flow_name: "Retrieve Options",
      flow_steps: [
        {
          step_number: 1,
          step_id: 3,
          step_name: "Retrieve Options",
        },
        {
          step_number: 2,
          step_id: 4,
          step_name: "Get User Locations",
        },
        {
          step_number: 3,
          step_id: 5,
          step_name: "Add Asset",
        },
        {
          step_number: 4,
          step_id: 6,
          step_name: "Add Asset API Call",
        },
        {
          step_number: 5,
          step_id: 7,
          step_name: "Retrieve Assets",
        },
        {
          step_number: 6,
          step_id: 8,
          step_name: "Step 6",
        },
        {
          step_number: 7,
          step_id: 9,
          step_name: "Retrieve Assets",
        },
      ],
    },
  ],
};


export const variableMappingFlowsData = {
  flow1: {
    step1: {
      response: {
        session: { token: "", id: "" },
        data: { token: "", value: "" },
      },
      request: {
        headers: { key: "", authorization: "" },
        body: { param1: "", param2: "" },
      },
    },
    step2: {
      response: { result: { status: "", code: "" } },
      request: { headers: { auth: "", "content-type": "" } },
    },
  },
  flow2: {
    step1: {
      response: { session: { token: "", expiry: "" } },
      request: { headers: { key: "", authorization: "" } },
    },
  },
};