import ApiRequestResponseOutputView from "@/components/common/api-request-response-output-view/ApiRequestResponseOutputView";
import { AssertionResults } from "@/components/test-runner/api-test-detailed-view/blocks/assertion-results/AssertionResults";
import TabList from "@/components/test-runner/api-test-detailed-view/blocks/tab-list/TabList";
import React, { useState } from "react";

const CustomAssertion = ({
  assertionId,
  assertion,
  assertionResults,
  handleAssertionChange,
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [assertionString, setAssertionString] = useState(assertion);
  return (
    <div>
      <TabList
        tabs={[
          { id: 1, name: "Assertion" },
          { id: 2, name: "Results" },
        ]}
        activeIndex={activeTab}
        onTabClickHandler={(tab, index) => setActiveTab(index)}
        tabSecondary={false}
      />

      {activeTab === 0 && (
        <div className="relative">
          <button
            className="button--outlined-primary py-[6px] px-2 mx-2 text-sm absolute right-5 bottom-2 z-10"
            onClick={() => handleAssertionChange(assertionId, assertionString)}
          >
            Update and Save
          </button>
          <ApiRequestResponseOutputView
            onChange={(e) => {
              setAssertionString(e);
            }}
            readonly={false}
            value={assertionString}
            responseType="js"
          />
        </div>
      )}

      {activeTab === 1 && (
        <div className="text-sm">
          <AssertionResults list={assertionResults} />
        </div>
      )}
    </div>
  );
};

export default CustomAssertion;
