import TabList from "@/components/test-runner/api-test-detailed-view/blocks/tab-list/TabList";
import React, { useState } from "react";

const NonTechTabs = ({ explanation = "", summary = "" }) => {
  const [activeTab, setActiveTab] = useState(0);

  console.log("log:: summary is ", summary);
  return (
    <div>
      <TabList
        tabs={[
          { id: 1, name: "Explanation" },
          { id: 2, name: "Summary" },
        ]}
        activeIndex={activeTab}
        onTabClickHandler={(tab: any, index: number) => {
          setActiveTab(index);
        }}
        tabSecondary={false}
      />
      <div className="p-2">
        {activeTab === 0 && (
          <>
            <div
              className="whitespace-pre-wrap  text-sm text-white"
              dangerouslySetInnerHTML={{
                __html: explanation?.replace(/\n/g, "<br/>"),
              }}
            />
          </>
        )}
        {activeTab === 1 && <div className="whitespace-pre-wrap  text-sm text-white">{summary}</div>}
      </div>
    </div>
  );
};

export default NonTechTabs;
