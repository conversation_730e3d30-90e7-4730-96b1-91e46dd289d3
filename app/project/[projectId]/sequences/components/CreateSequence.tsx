import React, { useState } from "react";
import { Flow } from "../types/sequences";
import CustomSelect from "@/components/common/custom-select/CustomSelect";
import PrimaryButtonOutlined from "@/components/common/buttons/PrimaryButtonOutlined";
import { RxCross1 } from "react-icons/rx";
import PrimaryButton from "@/components/common/buttons/PrimaryButton";
import PrimaryTextarea from "@/components/common/primary-textarea/PrimaryTextarea";
import VariableMapping from "./VariableMapping";
import { variableMappingFlowsData } from "../data/flows";
import PrimaryInput from "@/components/common/primary-input/PrimaryInput";

interface CreateSequenceProps {
  flows: Flow[];
  setFlows: (flows: Flow[]) => void;
  flowOptions: any[];
  flowName: string;
  setFlowName: (flowName: string) => void;
  onClose: () => void;
  handleCreateSequence: () => void;
  setVariableMapped: (variableMapped: any[]) => void;
  variableMapped: any[];
  creationStep: "flow" | "variableMapping";
  setCreationStep: (creationStep: "flow" | "variableMapping") => void;
  handleVariableMapping: (
    variableObject: any[],
    regressionTestId: string
  ) => void;
  regressionTestId: string;
  mappingString: string;
  setMappingString: (mappingString: string) => void;
}

const CreateSequence = ({
  flows,
  setFlows,
  flowOptions,
  flowName,
  setFlowName,
  onClose,
  handleCreateSequence,
  setVariableMapped,
  variableMapped,
  creationStep,
  setCreationStep,
  handleVariableMapping,
  regressionTestId,
  mappingString,
  setMappingString,
}: CreateSequenceProps) => {
  const handleCreateMore = () => {
    setFlows([
      ...flows,
      {
        id: "",
        order: flows.length,
      },
    ]);
  };

  const options = flowOptions?.map((flow) => {
    return {
      value: flow.e2e_id,
      label: flow.name,
      // description: flow?.flow_steps?.map((item) => item.step_name).join(" -> "),
    };
  });

  const handleDeleteFlow = (index: number) => {
    const updatedFlows = [...flows];
    updatedFlows.splice(index, 1);
    setFlows(updatedFlows);
  };

  return (
    <div className="p-2 px-1 h-full">
      <h2 className="text-[#D1D1E3] text-[16px] font-bold mb-1">
        Create New Sequence
      </h2>
      <p className="text-[#B2B2C1] font-normal text-sm">
        Select the E2E flows in the order they should run
      </p>
      {creationStep === "flow" ? (
        <>
          <PrimaryInput
            value={flowName}
            onChange={(e) => setFlowName(e.target.value)}
            className="mt-2"
            placeholder="Enter Sequence Name"
          />
          <div className="h-[60%] overflow-auto border-dashed border-[#1B1B41] border-2 py-4 px-2 rounded-md mt-4">
            <div className="space-y-3">
              {flows.map((flow, index) => {
                return (
                  <div className="flex  justify-between items-center">
                    <div className="">Flow {flow.order}</div>
                    <div className="w-[70%] flex items-center gap-2">
                      <CustomSelect
                        options={options}
                        value={flow.id}
                        onChange={(val) => {
                          const updatedFlows = [...flows];
                          updatedFlows[index] = {
                            ...updatedFlows[index],
                            id: val,
                          };
                          setFlows(updatedFlows);
                        }}
                        renderOption={(option) => (
                          <div className="flex flex-col">
                            <span className="text-white font-semibold">
                              {option.label}
                            </span>
                            <span className="text-xs text-gray-400">
                              {option.description}
                            </span>
                          </div>
                        )}
                        renderSingleValue={(option) => (
                          <div className="flex items-center gap-2 text-white">
                            {option.label}
                          </div>
                        )}
                      />
                      <RxCross1
                        cursor={"pointer"}
                        onClick={() => handleDeleteFlow(index)}
                        size={16}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
            <PrimaryButtonOutlined
              style={{
                width: "100%",
                marginTop: "20px",
              }}
              onClick={handleCreateMore}
            >
              + Add another
            </PrimaryButtonOutlined>
          </div>
          <div className="flex justify-end gap-3 mt-3 w-full">
            <PrimaryButtonOutlined onClick={onClose}>
              Cancel
            </PrimaryButtonOutlined>
            <PrimaryButton onClick={handleCreateSequence}>
              Create Sequence
            </PrimaryButton>
          </div>
        </>
      ) : (
        <>
          <div className="mt-[10%] w-full">
            <label className="flex items-center justify-between text-sm font-medium text-gray-300 mb-1">
              <span>Enter Variable Mapping</span>
              <a
                href="/variable-mapping-rules"
                target="_blank"
                className="text-xs text-blue-400 hover:underline"
              >
                Check rules
              </a>
            </label>

            <VariableMapping
              data={variableMappingFlowsData}
              setVariableMapped={setVariableMapped}
              currentFlowsAndStepsData={flows}
              mappingString={mappingString}
              setMappingString={setMappingString}
            />
          </div>

          <div className="flex justify-end gap-3 mt-3 w-full">
            <PrimaryButtonOutlined onClick={onClose}>
              Cancel
            </PrimaryButtonOutlined>
            <PrimaryButton onClick={() => handleVariableMapping(variableMapped, regressionTestId)}>
              Next
            </PrimaryButton>
          </div>
        </>
      )}
    </div>
  );
};

export default CreateSequence;
