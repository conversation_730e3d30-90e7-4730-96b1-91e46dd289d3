"use client";

import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import { io, Socket } from 'socket.io-client';
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL, NEXT_PUBLIC_DR_CODE_SOCKET_URL } from '@/temp-utils/Constants/globalVariables';
import useToast from '@/hooks/useToast';
import { useParams } from 'next/navigation';
import FloatingChatButton from './FloatingChatButton';

// Import components
import { ChatHeader } from '@/components/agent-chat/ChatHeader';
import { ChatInput } from '@/components/agent-chat/ChatInput';
import { ChatMessage } from '@/components/agent-chat/ChatMessage';
import { ChatSidebar } from '@/components/agent-chat/ChatSidebar';
import { SocketDebugger } from '@/components/agent-chat/SocketDebugger';
import { Message, MentionData, StructuredMention, SelectableItem, ChatSession, Sequence } from '@/components/agent-chat/types';
import WelcomeMessage from '@/components/agent-chat/WelcomeMessage';
import { MentionsData } from '@/components/agent-chat/assertions-chat/SuggestionsInput';

export const AgentChat: React.FC<{
    handleCloseChat: () => void
    additionalMentions?: MentionsData[]
}> = ({
    handleCloseChat=()=>{},
    additionalMentions=[]
}) => {
        const [messages, setMessages] = useState<Message[]>([]);
        const [streamingMessage, setStreamingMessage] = useState<string>('');
        const [input, setInput] = useState('');
        const [isLoading, setIsLoading] = useState(false);
        const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
        const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
        const [isStreaming, setIsStreaming] = useState(false);
        const [sidebarOpen, setSidebarOpen] = useState(false);
        const [pendingToolCall, setPendingToolCall] = useState<Message | null>(null);
        const [showDebug, setShowDebug] = useState(false);
        const [socketEvents, setSocketEvents] = useState<Array<{ event: string; data: unknown; timestamp: number }>>([]);
        const [isSocketConnected, setIsSocketConnected] = useState(false);
        const [mentionsData, setMentionsData] = useState<MentionsData[]>([]);
        const [selectedMentionsData, setSelectedMentionsData] = useState<MentionsData[]>([]);
        const socketRef = useRef<Socket | null>(null);
        const messagesEndRef = useRef<HTMLDivElement>(null);
        const { showToast } = useToast();
        const { projectId , sequenceId} = useParams();

        // Hardcoded user ID for now - in a real app, you'd get this from your auth system
        const userId = "1";

        // Add these new state variables after the existing ones
        const [mentionState, setMentionState] = useState<{
            isOpen: boolean;
            searchTerm: string;
            triggerIdx: number;
        }>({
            isOpen: false,
            searchTerm: '',
            triggerIdx: -1
        });
        const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
        const [sequences, setSequences] = useState<Sequence[]>([]);
        const [selectedMentions, setSelectedMentions] = useState<MentionData[]>([]);
        const [isChatOpen, setIsChatOpen] = useState(true);

        // Add a ref for the input box
        const inputBoxRef = useRef<HTMLDivElement>(null);

        // Scroll to bottom of messages
        const scrollToBottom = () => {
            messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
        };

        useEffect(() => {
            scrollToBottom();
        }, [messages, pendingToolCall, streamingMessage]);

    // Move loadChatHistory outside of useCallback since it's causing dependency cycles
    const loadChatHistory = async () => {
        // Prevent unnecessary calls if projectId or userId is missing
        if (!projectId || !userId) {
            console.log('Skipping chat history load - missing projectId or userId');
            return;
        }

        try {
            console.log('Loading chat history for project:', projectId, 'user:', userId);
            const response = await axios.get(
                `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/agent/history/${projectId}/${userId}`
            );
            if (response.data.success) {
                setChatSessions(response.data.history);
            }
        } catch (error) {
            console.error('Error loading chat history:', error);
            showToast('Failed to load chat history', 'error');
        }
    };

    // Socket management functions
    const setupSocketEventListeners = useCallback((socket: Socket) => {
        if (!socket) return;

        socket.on('connect', () => {
            console.log('Socket connected with ID:', socket.id);
            setIsSocketConnected(true);
            setSocketEvents(prev => [...prev, {
                event: 'connect',
                data: { id: socket.id },
                timestamp: Date.now()
            }]);
            showToast('Connected to agent service', 'success');
        });

        socket.on('disconnect', (reason) => {
            console.log('Socket disconnected:', reason);
            setIsSocketConnected(false);
            setSocketEvents(prev => [...prev, {
                event: 'disconnect',
                data: { reason },
                timestamp: Date.now()
            }]);
        });

        // Handle ping from agent to verify connection
        socket.on('agent-ping', (data, callback) => {
            console.log('Received ping from agent:', data);
            setSocketEvents(prev => [...prev, {
                event: 'agent-ping',
                data,
                timestamp: Date.now()
            }]);
            if (typeof callback === 'function') {
                try {
                    callback({ success: true, timestamp: Date.now() });
                } catch (error) {
                    console.error('Error responding to ping:', error);
                }
            }
        });

        // Handle tool call requests
        socket.on('agent-tool-call', (data) => {
            console.log('\n=== Tool Call Request Debug ===');
            console.log('Tool call request received:', data);
            console.log('Has diffData:', !!data.diffData);
            if (data.diffData) {
                console.log('Diff Data Structure:', {
                    type: data.diffData.type,
                    hasBeforeData: !!data.diffData.before,
                    hasAfterData: !!data.diffData.after,
                    title: data.diffData.title
                });
            }
            
            // Show toast to user about pending tool approval
            showToast(`Tool "${data.tool}" requires your approval`, 'info');
            
            // Create a tool call message
            const toolCallMessage: Message = {
                role: 'tool-call',
                content: `Tool Request: ${data.tool}\nDescription: ${data.description}\nArgs: ${JSON.stringify(data.args, null, 2)}`,
                created_at: new Date().toISOString(),
                toolName: data.tool,
                toolArgs: data.args,
                toolDescription: data.description,
                comparisonMessage: data.comparisonMessage,
                diffData: data.diffData, // Add the diff data
                pending: true,
                approvalHandler: (approved: boolean) => {
                    console.log('\n=== Tool Approval Debug ===');
                    console.log(`User ${approved ? 'approved' : 'rejected'} tool call:`, {
                        tool: data.tool,
                        args: data.args,
                        description: data.description,
                        hasDiffData: !!data.diffData
                    });
                    
                    // Update the message to show it's no longer pending
                    setMessages(prevMessages => {
                        const updatedMessages = prevMessages.map(msg => {
                            if (msg === toolCallMessage) {
                                return {
                                    ...msg,
                                    pending: false,
                                    content: `${msg.content}\n\nStatus: ${approved ? '✅ Approved' : '❌ Rejected'}`
                                };
                            }
                            return msg;
                        });
                        return updatedMessages;
                    });
                    
                    // Send the response via a socket event
                    try {
                        socket.emit('tool-call-response', { 
                            approved,
                            tool: data.tool,
                            args: data.args
                        });
                        console.log(`Tool call response sent successfully:`, {
                            approved,
                            tool: data.tool,
                            args: data.args
                        });
                        showToast(`Tool ${approved ? 'approved' : 'rejected'}: ${data.tool}`, approved ? 'success' : 'warning');
                    } catch (error) {
                        console.error(`Error sending tool call response:`, error);
                        showToast('Error processing tool approval', 'error');
                    }
                }
            };
            
            // Add to messages and set as pending
            setMessages(prevMessages => [...prevMessages, toolCallMessage]);
            setPendingToolCall(toolCallMessage);
        });
        
        // Handle tool execution
        socket.on('agent-tool-execution', (data) => {
            console.log('Tool execution started:', data);
            
            if (!data.tool) {
                console.error('Tool execution event missing tool name:', data);
                return;
            }
            
            // Clear any pending tool call
            setPendingToolCall(null);
            
            // Add the execution message
            setMessages(messages => [
                ...messages, 
                {
                    role: 'tool-execution',
                    content: `Executing: ${data.tool}`,
                    created_at: new Date().toISOString(),
                    toolName: data.tool,
                    toolArgs: data.args,
                    toolDescription: data.description
                }
            ]);
        });
        
        // Handle tool results
        socket.on('agent-tool-result', (data) => {
            console.log('Tool result received:', data);
            
            if (!data.tool) {
                console.error('Tool result missing tool name:', data);
                return;
            }
            
            // Prevent empty tool results
            if (!data.result) {
                console.warn(`Empty result received for tool ${data.tool}`);
                data.result = `[No result data from ${data.tool}]`;
            }
            
            setMessages(messages => [
                ...messages, 
                {
                    role: 'tool-result',
                    content: data.result,
                    created_at: new Date().toISOString(),
                    toolName: data.tool
                }
            ]);
        });
        
        // Handle tool errors
        socket.on('agent-tool-error', (data) => {
            console.log('Tool error:', data);
            
            setMessages(messages => [
                ...messages, 
                {
                    role: 'tool-error',
                    content: `Error using tool ${data.tool}: ${data.error}`,
                    created_at: new Date().toISOString(),
                    toolName: data.tool
                }
            ]);
        });

        // Update the socket event handler for streaming chunks
        socket.on('agent-response-chunk', ({ chunk }) => {
            if (!chunk) {
                console.warn('Received empty chunk');
                return;
            }
            
            console.log('Received chunk:', chunk, 'length:', chunk.length);
            setSocketEvents(prev => [...prev, {
                event: 'agent-response-chunk',
                data: { chunkLength: chunk.length },
                timestamp: Date.now()
            }]);
            
            // Force a re-render by creating a new string
            setStreamingMessage(prev => {
                const newMessage = prev + chunk;
                return newMessage;
            });
            setIsStreaming(true);
        });

        socket.on('agent-response-complete', ({ chatSessionId, fullResponse }) => {
            console.log('Response complete, session ID:', chatSessionId);
            console.log('Full response length:', fullResponse?.length || 0);
            console.log('Streaming message length:', streamingMessage.length);
            
            // Add the final message to the messages array
            const finalContent = streamingMessage || fullResponse || '';
            
            if (finalContent) {
                setMessages(prevMessages => [
                    ...prevMessages,
                    {
                        role: 'agent',
                        content: finalContent,
                        created_at: new Date().toISOString()
                    }
                ]);
            } else {
                console.warn('Empty response received');
            }
            
            // Reset streaming state
            setStreamingMessage('');
            setIsStreaming(false);
            setIsLoading(false);
            setCurrentSessionId(chatSessionId);
            
            // Only refresh chat history if we have a new session ID
            if (chatSessionId) {
                loadChatHistory();
            }
        });

        socket.on('agent-error', (data) => {
            console.error('Agent error:', data);
            
            // Add error message to the chat
            setMessages(prevMessages => [
                ...prevMessages, 
                {
                    role: 'tool-error',
                    content: `Error: ${data.error || 'An unknown error occurred'}`,
                    created_at: new Date().toISOString()
                }
            ]);
            
            // Reset states
            setStreamingMessage('');
            setIsStreaming(false);
            setIsLoading(false);
            
            showToast(data.error || 'An error occurred during streaming', 'error');
        });

        socket.on('connect_error', (err) => {
            console.error('Socket connection error:', err);
            showToast('Connection error: ' + err.message, 'error');
        });

        socket.on('reconnect', (attemptNumber) => {
            console.log(`Socket reconnected after ${attemptNumber} attempts`);
            showToast('Reconnected to agent service', 'success');
        });

        socket.on('reconnect_error', (error) => {
            console.error('Socket reconnection error:', error);
        });

        socket.on('reconnect_failed', () => {
            console.error('Socket reconnection failed after all attempts');
            showToast('Failed to reconnect to agent service', 'error');
        });

        // Debug event to log all socket events
        socket.onAny((event, ...args) => {
            if (!event.startsWith('agent-response-chunk')) { // Don't log every chunk
                console.log(`Socket event: ${event}`, args);
            }
        });

        // Add handler for agent-action events
        socket.on('agent-action', (data) => {
            console.log('Agent action received:', data);
            
            if (!data.action) {
                console.error('Agent action missing action text:', data);
                return;
            }
            
            setMessages(messages => [
                ...messages, 
                {
                    role: 'agent-action',
                    content: data.action,
                    created_at: new Date().toISOString()
                }
            ]);
        });
    }, []); // Keep empty dependency array since we pass socket as parameter

    const initSocket = useCallback(() => {
        try {
            // Clean up existing socket if any
            if (socketRef.current) {
                socketRef.current.removeAllListeners();
                socketRef.current.disconnect();
            }
            const socketUrl = NEXT_PUBLIC_DR_CODE_SOCKET_URL;
            console.log('Initializing socket connection to:', socketUrl);

            const socket = io(socketUrl, {
                path: "/testgpt/socket.io", // 👈 must match this path exactly
                transports: ['websocket', 'polling'],
                reconnection: true,
                reconnectionAttempts: 10,
                reconnectionDelay: 1000,
                reconnectionDelayMax: 5000,
                timeout: 20000,
                forceNew: true,
              });
              

            socketRef.current = socket;
            setupSocketEventListeners(socket);
            return socket;
        } catch (error) {
            console.error('Error initializing socket:', error);
            showToast('Failed to connect to agent service', 'error');
            return null;
        }
    }, [setupSocketEventListeners]);

    // Initialize socket connection only once when component mounts
    useEffect(() => {
        console.log('Initializing socket connection');
        const socket = initSocket();
        
        // Load chat history once on mount
        loadChatHistory();
        
        return () => {
            console.log('Cleaning up socket connection');
            if (socket) {
                socket.removeAllListeners();
                socket.disconnect();
            }
            socketRef.current = null;
            setIsSocketConnected(false);
        };
    }, [initSocket]); // Remove loadChatHistory from dependencies

    const handleNewChat = useCallback(() => {
        console.log('Starting new chat');
        
        // Reinitialize socket
        initSocket();
        
        // Reset chat state
        setMessages([]);
        setCurrentSessionId(null);
        setSidebarOpen(false);
        setStreamingMessage('');
        setIsStreaming(false);
        setIsLoading(false);
        setPendingToolCall(null);
    }, [initSocket]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!input.trim() || !projectId) return;

        // Check socket connection
        if (!socketRef.current?.connected) {
            console.log('Socket not connected, attempting to connect...');
            const socket = initSocket();
            
            if (!socket) {
                showToast('Failed to establish connection. Please try again.', 'error');
                return;
            }

            // Wait for connection
            const connected = await new Promise<boolean>((resolve) => {
                const timeout = setTimeout(() => resolve(false), 5000);
                socket.once('connect', () => {
                    clearTimeout(timeout);
                    resolve(true);
                });
            });

            if (!connected) {
                showToast('Failed to establish connection. Please try again.', 'error');
                return;
            }
        }

        const userMessage: Message = { 
            role: 'user', 
            content: input,
            created_at: new Date().toISOString()
        };
        
        setStreamingMessage('');
        setMessages([...messages, userMessage]);
        setInput('');
        setIsLoading(true);
        setIsStreaming(true);

        try {
            // const structuredMentions: StructuredMention[] = selectedMentions.map(mention => {
            //     const pathParts = mention.path.split('/');
            //     const structured: StructuredMention = {
            //         type: mention.type,
            //         id: mention.id,
            //         name: mention.name
            //     };

            //     if (mention.type === 'step') {
            //         structured.sequenceId = pathParts[0];
            //         structured.flowId = pathParts[1];
            //         structured.stepId = Number(mention.id);
            //     } else if (mention.type === 'flow') {
            //         structured.sequenceId = pathParts[0];
            //         structured.flowId = String(mention.id);
            //     } else if (mention.type === 'sequence') {
            //         structured.sequenceId = String(mention.id);
            //     }

            //     return structured;
            // });

            const structuredMentions = selectedMentionsData.map((mention) => {
                return {
                    type: mention.type,
                    id: mention.id,
                    name: mention.text
                };
            });
            console.log("log:: structuredMentions is ", selectedMentionsData);

            if (!socketRef.current?.connected) {
                showToast('Socket not connected. Reconnecting...', 'warning');
                socketRef.current?.connect();
                
                const reconnected = await new Promise<boolean>((resolve) => {
                    const timeout = setTimeout(() => resolve(false), 3000);
                    socketRef.current?.once('connect', () => {
                        clearTimeout(timeout);
                        resolve(true);
                    });
                });
                
                if (!reconnected) {
                    showToast('Could not reconnect. Falling back to non-streaming mode.', 'error');
                    setIsStreaming(false);
                }
            }


            // if inside sequence page, then add that sequence id to the structured mentions
           if(additionalMentions.length > 0){
            const additionalMentionsData = additionalMentions.map((mention) => {
                return {
                    type: mention.type,
                    id: mention.id,
                    name: mention.text
                };
            });
            structuredMentions.push(...additionalMentionsData);
           }

            const response = await axios.post(
                `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/agent/interact`,
                {
                    input: input,
                    projectId: projectId,
                    userId: userId,
                    chatSessionId: currentSessionId,
                    socketId: socketRef.current?.connected ? socketRef.current?.id : null,
                    mentions: structuredMentions
                }
            );

            if (!response.data.success) {
                setIsStreaming(false);
                setIsLoading(false);
                showToast(response.data.error || "Something went wrong", "error");
            } else if (!response.data.streaming) {
                setIsStreaming(false);
                setMessages(prev => [...prev, {
                    role: 'agent',
                    content: response.data.response,
                    created_at: new Date().toISOString()
                }]);
                setCurrentSessionId(response.data.chatSessionId);
                setIsLoading(false);
            }
            
        } catch (error) {
            console.error('Error:', error);
            setStreamingMessage('');
            setIsStreaming(false);
            setIsLoading(false);
            showToast(error.response?.data?.error || "Something went wrong", "error");
        }
    };

    const handleToolApproval = (approved: boolean) => {
        if (pendingToolCall && pendingToolCall.approvalHandler) {
            pendingToolCall.approvalHandler(approved);
            setPendingToolCall(null);
        }
    };

    const loadChatSession = async (sessionId: string) => {
        try {
            setIsLoading(true);
            const response = await axios.get(
                `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/agent/session/${sessionId}`
            );
            if (response.data.success) {
                // Process messages to ensure they have the right format
                const processedMessages = response.data.messages.map((msg: Message) => ({
                    role: msg.role,
                    content: msg.content,
                    created_at: msg.created_at,
                    toolName: msg.toolName,
                    toolArgs: msg.toolArgs,
                    toolDescription: msg.toolDescription
                }));
                
                setMessages(processedMessages);
                setCurrentSessionId(sessionId);
                setSidebarOpen(false); // Close sidebar after selecting a session
            }
        } catch (error) {
            console.error('Error loading chat session:', error);
            showToast('Failed to load chat session', 'error');
        } finally {
            setIsLoading(false);
        }
    };

        // Add this useEffect to fetch the sequences data
        useEffect(() => {
            const fetchSequences = async () => {
                try {
                    console.log('Fetching sequences for project:', projectId);
                    const response = await axios.get(`${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/sequences/hierarchy/${projectId}`);
                    if (response.data.success) {
                        console.log('Sequences loaded successfully:', response.data.sequences.length, 'sequences');
                        setSequences(response.data.sequences);
                    } else {
                        console.error('Failed to load sequences:', response.data.error);
                        showToast('Failed to load sequences data: ' + response.data.error, 'error');
                    }
                } catch (error) {
                    console.error('Error fetching sequences hierarchy:', error);
                    showToast('Failed to load sequences data: ' + (error.response?.data?.error || error.message), 'error');
                }
            };

            if (projectId) {
                fetchSequences();
            } else {
                console.warn('No projectId available for fetching sequences');
            }
        }, [projectId]);

        const handleInputChange = (newValue: string) => {
            setInput(newValue);

            if (mentionState.isOpen) {
                const sel = window.getSelection();
                if (!sel?.anchorNode) return;
                const offset = sel.anchorOffset;

                const text = newValue.slice(0, offset);
                const match = /@([\w\-]*)$/.exec(text);
                const term = match ? match[1] : '';
                setMentionState(ms => ({ ...ms, searchTerm: term }));
            }
        };

        const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
            if (e.key === '@') {
                // Always position dropdown above the input box
                if (inputBoxRef.current) {
                    const rect = inputBoxRef.current.getBoundingClientRect();
                    setMentionState({
                        isOpen: true,
                        searchTerm: '',
                        triggerIdx: 0 // not used for dropdown position now
                    });
                    setDropdownPosition({
                        top: rect.top, // 40px above input
                        left: rect.left + window.scrollX
                    });
                }
            } else if (mentionState.isOpen) {
                if (e.key === 'Escape') {
                    setMentionState(prev => ({ ...prev, isOpen: false }));
                }
            }
        };

        const handleMentionSelect = (item: SelectableItem) => {
            const mentionText = `@${item.name}`;
            const newValue = input.slice(0, mentionState.triggerIdx) +
                mentionText +
                ' ' +
                input.slice(mentionState.triggerIdx + mentionState.searchTerm.length + 1);

            setInput(newValue);

            const newMention: MentionData = {
                id: item.id,
                type: item.type,
                name: item.name,
                path: item.path
            };

            setSelectedMentions(prev => [...prev, newMention]);
            setMentionState(ms => ({ ...ms, isOpen: false, searchTerm: '' }));
        };

   

        if (!isChatOpen) {
            return <FloatingChatButton setIsChatOpen={setIsChatOpen} />;
        }

    return (
        <div 
            className="flex flex-col h-full w-full bg-[#0D0D22] text-gray-100 fixed bottom-0 right-0 z-40 rounded-t-xl shadow-2xl border border-[#1E1F42] transition-all duration-300"
            style={{ transform: isChatOpen ? 'translateY(0)' : 'translateY(100%)', opacity: isChatOpen ? 1 : 0 }}
        >
            <ChatHeader
                currentSessionId={currentSessionId}
                onNewChat={handleNewChat}
                onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
                onToggleDebug={() => setShowDebug(!showDebug)}
                onClose={handleCloseChat}
                showDebug={showDebug}
            />

                {
                    sidebarOpen && (
                        <ChatSidebar
                            isOpen={sidebarOpen}
                            onClose={() => setSidebarOpen(false)}
                            onNewChat={handleNewChat}
                            chatSessions={chatSessions}
                            currentSessionId={currentSessionId}
                            onSelectSession={(sessionId) => {
                                loadChatSession(sessionId);
                                setSidebarOpen(false);
                            }}
                        />
                    )
                }

                <div className="flex flex-1 overflow-hidden">

                <div className="flex-1 flex flex-col bg-[#0A0B1A] rounded-lg overflow-hidden">
                    <div className="flex-1 overflow-y-auto p-4">
                        {messages.length === 0 && !isLoading && !isStreaming && !currentSessionId && (
                            <WelcomeMessage/>
                        )}
                        <div className="space-y-4">
                            {messages.map((message, index) => (
                                <ChatMessage
                                    key={index}
                                    message={message}
                                    onToolApproval={message === pendingToolCall ? handleToolApproval : undefined}
                                />
                            ))}
                            
                            {isStreaming && streamingMessage && (
                                <ChatMessage
                                    message={{
                                        role: 'agent',
                                        content: streamingMessage,
                                        created_at: new Date().toISOString()
                                    }}
                                />
                            )}
                            
                            {isLoading && !isStreaming && !streamingMessage && (
                                <div className="flex justify-start">
                                    <div className="bg-gray-800 rounded-lg p-3 text-gray-300 border border-gray-700">
                                        <div className="flex items-center space-x-2">
                                            <div className="animate-pulse">⏳</div>
                                            <div>Processing your request...</div>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                        <div ref={messagesEndRef} />
                    </div>

                        <ChatInput
                            input={input}
                            onInputChange={handleInputChange}
                            onSubmit={handleSubmit}
                            isLoading={isLoading}
                            hasPendingToolCall={!!pendingToolCall}
                            mentionState={mentionState}
                            dropdownPosition={dropdownPosition}
                            sequences={sequences}
                            onMentionSelect={handleMentionSelect}
                            onMentionClose={() => setMentionState(prev => ({ ...prev, isOpen: false }))}
                            selectedMentions={new Set(selectedMentions.map(m => m.name))}
                            onKeyDown={handleKeyDown}
                            inputRef={inputBoxRef}
                            setSelectedMentions={setSelectedMentionsData}
                        />
                    </div>
                </div>

                {showDebug && (
                    <SocketDebugger
                        socketId={socketRef.current?.id}
                        events={socketEvents}
                        isConnected={isSocketConnected}
                    />
                )}
            </div>
        );
    };

export default AgentChat; 