import React, { useEffect, useState } from "react";
import { Editor } from "@monaco-editor/react";
import useSequences from "../hooks/useSequences";

// here data is used for auto completion of the variable mapping
const VariableMapping = ({
  data,
  setVariableMapped,
  currentFlowsAndStepsData,
  mappingString = "",
  setMappingString,
}) => {
  const [flowsAndSteps, setFlowsAndSteps] = useState([]);
  const { fetchFlowsAndStepsByProjectId } = useSequences();

  const getKeysAtPath = (obj, parts) => {
    let current = obj;
    for (const part of parts) {
      if (!current || typeof current !== "object") return [];
      current = current[part];
    }
    if (!current || typeof current !== "object") return [];
    return Object.keys(current);
  };

  const handleEditorDidMount = (editor, monaco) => {
    console.log("✅ Monaco is ready");

    monaco.languages.registerCompletionItemProvider("javascript", {
      triggerCharacters: ["."],
      provideCompletionItems: (model, position) => {
        const lineContent = model.getLineContent(position.lineNumber);
        const textUntilPosition = lineContent.slice(0, position.column - 1);

        // Match expressions ending in dot, e.g. flow1.step1.
        const match = textUntilPosition.match(/([\w$]+(?:\.[\w$]+)*)\.$/);
        if (!match) {
          return { suggestions: [] };
        }

        const pathStr = match[1]; // e.g., flow1.step1
        const parts = pathStr.split(".");

        // Resolve keys at that path
        const keys = getKeysAtPath(data, parts);
        if (!keys.length) return { suggestions: [] };

        const suggestions = keys.map((key) => ({
          label: key,
          kind: monaco.languages.CompletionItemKind.Field,
          insertText: key,
          range: {
            startLineNumber: position.lineNumber,
            startColumn: position.column,
            endLineNumber: position.lineNumber,
            endColumn: position.column,
          },
        }));

        return { suggestions };
      },
    });
  };

  const parseVariableMapping = (input: string) => {
    setMappingString(input);
    const lines = input
      .split(",")
      .map((line) => line.trim())
      .filter(Boolean);

    const mappings = [];

    const parseSide = (expression: string) => {
      const trimmed = expression.trim();

      // Handle string concatenation cases
      if (trimmed.includes("+")) {
        const parts = trimmed.split("+").map(part => part.trim());
        const concatenatedParts = parts.map(part => {
          // If part is a string literal (wrapped in quotes)
          if ((part.startsWith('"') && part.endsWith('"')) || 
              (part.startsWith("'") && part.endsWith("'"))) {
            return {
              flowId: NaN,
              stepId: NaN,
              path: part.slice(1, -1), // Remove quotes
              isLiteral: true
            };
          }
          // Otherwise treat it as a variable reference
          return {
            ...parseSide(part),
            isLiteral: false
          };
        });

        // Combine all parts into a single path with special syntax
        const combinedPath = concatenatedParts
          .map(part => part.isLiteral ? `"${part.path}"` : part.path)
          .join(" + ");

        return {
          flowId: concatenatedParts.find(part => !part.isLiteral)?.flowId || NaN,
          stepId: concatenatedParts.find(part => !part.isLiteral)?.stepId || NaN,
          path: combinedPath,
          isConcatenation: true
        };
      }

      const flowStepRegex = /^flow(\d+)\.step(\d+)\.(.+)$/;
      const match = trimmed.match(flowStepRegex);

      if (match) {
        const [, flowId, stepId, path] = match;

        const flows = flowsAndSteps.find(
          (flow, index) => index === Number(flowId) - 1
        );

        const flowUuid = flows?.flow_uuid;

        const step_id = flows?.flow_steps.find(
          (step) => step.step_number === Number(stepId)
        )?.step_id;

        return {
          flowId: flowUuid,
          stepId: step_id,
          path,
          isConcatenation: false
        };
      }

      // Fallback for flowX.steps.path
      const altMatch = trimmed.match(/^flow(\d+)\.steps\.(.+)$/);
      if (altMatch) {
        const [, flowId, path] = altMatch;
        const flows = flowsAndSteps.find(
          (flow, index) => index === Number(flowId) - 1
        );

        // Get all step IDs for this flow
        const stepIds = flows?.flow_steps?.map(step => step.step_id) || [];
        
        return {
          flowId: flows?.flow_uuid,
          // If there are steps, use the first step ID, otherwise NaN
          stepId: stepIds.length > 0 ? stepIds[0] : NaN,
          path,
          isConcatenation: false,
          applyToAllSteps: true  // New flag to indicate this applies to all steps
        };
      }

      // Fallback for flows.steps.path or unrecognized input
      const globalMatch = trimmed.match(/^flows\.steps?\.(.+)$/);
      if (globalMatch) {
        return {
          flowId: NaN,
          stepId: NaN,
          path: globalMatch[1],
          isConcatenation: false
        };
      }

      // Not matching any valid pattern
      return {
        flowId: NaN,
        stepId: NaN,
        path: trimmed,
        isConcatenation: false
      };
    };

    for (const line of lines) {
      try {
        const [lhsRaw, rhsRaw] = line.split("=").map((part) => part.trim());

        if (!lhsRaw || !rhsRaw) {
          console.warn(
            `Skipping malformed line (missing LHS or RHS): "${line}"`
          );
          continue;
        }

        const lhs = parseSide(lhsRaw);
        const rhs = parseSide(rhsRaw);

        let target_scope = "FLOW";

        if (/^flows\.steps?\./i.test(lhsRaw)) {
          target_scope = "ALL";
        } else if (/^flow\d+\.step\d+\./i.test(lhsRaw)) {
          target_scope = "STEP";
        } else if (/^flow\d+\.steps\./i.test(lhsRaw)) {
          target_scope = "FLOW";
        }

        // If either side has applyToAllSteps, create a mapping for each step in that flow
        if (lhs.applyToAllSteps || rhs.applyToAllSteps) {
          const flow = flowsAndSteps.find(
            (f) => f.flow_uuid === (lhs.applyToAllSteps ? lhs.flowId : rhs.flowId)
          );
          
          const stepIds = flow?.flow_steps?.map(step => step.step_id) || [];
          
          for (const stepId of stepIds) {
            mappings.push({
              from_flow_id: rhs.flowId,
              from_step_id: rhs.applyToAllSteps ? stepId : rhs.stepId,
              from_path: rhs.path,
              to_flow_id: lhs.flowId,
              to_step_id: lhs.applyToAllSteps ? stepId : lhs.stepId,
              to_path: lhs.path,
              target_scope,
              is_concatenation: rhs.isConcatenation || false
            });
          }
        } else {
          mappings.push({
            from_flow_id: rhs.flowId,
            from_step_id: rhs.stepId,
            from_path: rhs.path,
            to_flow_id: lhs.flowId,
            to_step_id: lhs.stepId,
            to_path: lhs.path,
            target_scope,
            is_concatenation: rhs.isConcatenation || false
          });
        }
      } catch (error) {
        console.error(`Failed to parse line: "${line}"`, error);
      }
    }

    setVariableMapped(mappings);

    return mappings;
  };

  function mergeAndSortFlows(currentFlowsAndStepsData, allFlows) {
    return currentFlowsAndStepsData
      ?.map((entry) => {
        const matchedFlow = allFlows.find(
          (flow) => flow.flow_uuid === entry.id
        );
        return matchedFlow
          ? {
              order: entry.order,
              flow_uuid: matchedFlow.flow_uuid,
              flow_name: matchedFlow.flow_name,
              flow_steps: matchedFlow.flow_steps,
            }
          : null;
      })
      .filter(Boolean)
      .sort((a, b) => a.order - b.order);
  }

  const getFlowsAndStepsData = async () => {
    const response = await fetchFlowsAndStepsByProjectId();

    if (response.length > 0) {
      // filter those flows which are similar in currentFlowsAndStepsData using id property
      const filteredFlows = mergeAndSortFlows(
        currentFlowsAndStepsData,
        response
      );

      setFlowsAndSteps(filteredFlows);
    }
  };

  useEffect(() => {
    getFlowsAndStepsData();
  }, []);

  return (
    <Editor
      defaultLanguage="javascript"
      defaultValue={mappingString}
      height={200}
      theme="vs-dark"
      onMount={handleEditorDidMount}
      options={{
        lineNumbers: "off",
        wordWrap: "on",
        minimap: { enabled: false },
        scrollBeyondLastLine: false,
        roundedSelection: false,
        glyphMargin: false,
        folding: false,
        lineNumbersMinChars: 3,
        scrollbar: { verticalScrollbarSize: 8 },
      }}
      onChange={parseVariableMapping}
    />
  );
};

export default VariableMapping;
