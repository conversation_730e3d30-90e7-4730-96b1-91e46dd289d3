import React, { useEffect } from "react";
import CustomSelect from "@/components/common/custom-select/CustomSelect";
import PrimaryInput from "@/components/common/primary-input/PrimaryInput";
import useScheduler from "../hooks/useScheduler";
import ToggleSwitch from "@/components/common/toggle-switch/ToggleSwitch";

interface SchedulerSettingsProps {
  setEmails: (emails: string[]) => void;
  setScheduleTime: (scheduleTime: string) => void;
  setScheduleType: (
    scheduleType: "DAILY" | "WEEKLY" | "MONTHLY" | "CUSTOM"
  ) => void;
  emails: string[];
  scheduleTime: string;
  scheduleType: "DAILY" | "WEEKLY" | "MONTHLY" | "CUSTOM";
  cronExpression: string;
  setCronExpression: (cronExpression: string) => void;
  dayOfMonth: number;
  setDayOfMonth: (dayOfMonth: number) => void;
  weekOptions: { value: string; label: string }[];
  dayOfWeek: number;
  setDayOfWeek: (dayOfWeek: number) => void;
  enableScheduler: boolean;
  setEnableScheduler: (enableScheduler: boolean) => void;
  toggleScheduler: () => void;
}

const SchedulerSettings = ({
  setEmails,
  setScheduleTime,
  setScheduleType,
  emails,
  scheduleTime,
  scheduleType,
  cronExpression,
  setCronExpression,
  dayOfMonth,
  setDayOfMonth,
  weekOptions,
  dayOfWeek,
  setDayOfWeek,
  enableScheduler,
  setEnableScheduler,
  toggleScheduler
}: SchedulerSettingsProps) => {
  const { getTypes, scheduleTypeOptions, getScheduler } = useScheduler();

  const handleEmailChange = (index: number, value: string) => {
    const updatedEmails = [...emails];
    updatedEmails[index] = value;
    setEmails(updatedEmails);
  };

  const renderScheduleInput = () => {
    switch (scheduleType) {
      case "CUSTOM":
        return (
          <div>
            <label className="block mb-2 font-semibold text-white">
              Cron Expression
            </label>
            <PrimaryInput
              value={cronExpression}
              onChange={(e) => setCronExpression(e.target.value)}
              placeholder="Enter cron expression"
            />
          </div>
        );

      case "MONTHLY":
        return (
          <div>
            <label className="block mb-2 font-semibold text-white">
              Day of month
            </label>
            <CustomSelect
              options={Array.from({ length: 31 }, (_, i) => i + 1).map(
                (day) => ({
                  value: day.toString(),
                  label: day.toString(),
                })
              )}
              value={dayOfMonth?.toString()}
              onChange={(e) => setDayOfMonth(Number(e))}
            />
          </div>
        );

      case "WEEKLY":
        return (
          <div>
            <label className="block mb-2 font-semibold text-white">
              Day of week
            </label>
            <CustomSelect
              options={weekOptions}
              value={dayOfWeek?.toString()}
              onChange={(e) => setDayOfWeek(Number(e))}
            />
          </div>
        );
    }
  };

  useEffect(() => {
    async function getSchedulerTypes() {
      await getTypes();
    }
    getSchedulerTypes();
  }, []);

  return (
    <div className="flex flex-col gap-6 shadow rounded-md">
      <ToggleSwitch
        label="Enable Scheduler"
        value={enableScheduler}
        onToggle={() => {
          setEnableScheduler(!enableScheduler);
          toggleScheduler();
        }}
      />

      {/* Schedule Type */}
      <div>
        <label className="block mb-2 font-semibold text-white">
          Schedule Type
        </label>
        <CustomSelect
          onChange={(e) =>
            setScheduleType(e as "DAILY" | "WEEKLY" | "MONTHLY" | "CUSTOM")
          }
          value={scheduleType}
          options={scheduleTypeOptions}
        />
      </div>

      {/* Schedule Time */}
      {renderScheduleInput()}

      {scheduleType !== "CUSTOM" && (
        <div>
          <label className="block mb-2 font-semibold text-white">
            Schedule Time
          </label>
          <input
            type="time"
            value={scheduleTime}
            onChange={(e) => setScheduleTime(e.target.value)}
            className="w-full px-3 py-2 border border-[#1B1B41] rounded-md bg-transparent"
          />
        </div>
      )}

      {/* Emails */}
      <div>
        <label className="block mb-2 font-semibold text-white">Emails</label>
        <div className="flex flex-col gap-3">
          {emails?.map((email, index) => (
            <PrimaryInput
              key={index}
              type="email"
              value={email}
              placeholder="Enter email address"
              onChange={(e) => handleEmailChange(index, e.target.value)}
            />
          ))}
        </div>
        <button
          onClick={() => setEmails([...emails, ""])}
          className="mt-3 px-4 py-2 bg-[#1B1B41] text-white text-sm font-medium rounded hover:bg-[#1B1B41]/80 transition"
        >
          Add Email
        </button>
      </div>
    </div>
  );
};

export default SchedulerSettings;
