"use client";
import useToast from "@/hooks/useToast";
import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import axios from "axios";
import { useParams, useRouter } from "next/navigation";
import React, { useState } from "react";
import { FiMoreHorizontal } from "react-icons/fi";
import { RiLoader2Fill } from "react-icons/ri";
import Accordion from "@/components/common/accordion/Accordion";

const statusColors: Record<string, string> = {
  passed: "bg-[#15B0974D] text-[#A4F4E7]",
  failed: "bg-[#C037444D] text-[#E4626F]",
  processing: "bg-yellow-100 text-yellow-800",
  "not-run": "bg-vscode-editor-background text-gray-500",
  "warning": "bg-[#EDA1454D] text-[#F4C790]"
};

type Sequence = {
  title: string;
  flowOrder: string;
  status: "passed" | "failed" | "processing" | "not-run";
  date: string;
};

// Internal component for rendering the actual table
const SequenceTableContent = ({
  sequences,
  setSequences,
  refetchSequences,
}: {
  sequences: any[];
  setSequences: React.Dispatch<React.SetStateAction<any[]>>;
  refetchSequences: () => void;
}) => {
  const router = useRouter();
  const { projectId } = useParams();
  const { showToast } = useToast();
  const [menuOpenIndex, setMenuOpenIndex] = useState<number | null>(null);
  const [runningRows, setRunningRows] = useState<number[]>([]);

  const handleRunSequence = async (seq: any) => {
    setRunningRows((prev) => [...prev, seq.id]);
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.RUN_ONE_SEQUENCE}`,
        {
          regressionTestId: seq.id,
        }
      );
      console.log(response.data);
      setRunningRows((prev) => prev.filter((id) => id !== seq.id));
    } catch (error) {
      console.log(error);
      setRunningRows((prev) => prev.filter((id) => id !== seq.id));
    }
    refetchSequences();
  };

  const handleViewSteps = (seq: any) => {
    router.push(`/project/${projectId}/sequences/${seq.id}`);
  };

  const handleDeleteSequence = async (seq: any) => {
    try {
      const response = await axios.delete(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.DELETE_SEQUENCE}/${seq.id}`,
        {}
      );
      console.log(response.data);
      if (response.data.success) {
        showToast("Sequence deleted successfully", "success");
        setSequences((prev: any[]) => prev.filter((s: any) => s.id !== seq.id));
      }
    } catch (error) {
      console.log(error);
      showToast("Something went wrong", "error");
    }
  };

  const handleSequenceStatus = async (seq, activeStatus: boolean) => {
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.SET_SEQUENCE_STATUS}`,
        {
          activeStatus: activeStatus,
          regressionTestId: seq.id,
        }
      );
      console.log(response.data);
      if (response.data.success) {
        showToast(
          `Sequence ${activeStatus ? "Activated" : "Deactivated"} successfully`,
          "success"
        );

        const updatedSequence = sequences.map((item) => {
          if (item.id === seq.id) {
            return {
              ...item,
              is_active: activeStatus,
            };
          }
          return item;
        });

        console.log(
          "log:: updated sequence ",
          seq,
          updatedSequence,
          activeStatus
        );

        setSequences(updatedSequence);
      }
    } catch (error) {
      console.log(error);
      showToast("Something went wrong", "error");
    }
  };

  return (
    <div className="overflow-x-auto rounded-xl bg-[#0D0D22] p-4 w-full">
      <table className="w-full text-sm text-left text-white">
        <thead className="text-[14px] text-gray-400">
          <tr>
            <th className="px-4 py-3 font-medium">Sequence Title</th>
            <th className="px-4 py-3 font-medium">Status</th>
            <th className="px-4 py-3 font-medium">Date Last Run</th>
            <th className="px-4 py-3"></th>
          </tr>
        </thead>
        <tbody className="divide-y divide-[#1a1a2e]">
          {sequences?.map((seq, index) => (
            <React.Fragment key={index}>
              {runningRows.includes(seq.id) && (
                <tr className="relative">
                  <td colSpan={5} className="p-0">
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-10">
                      <RiLoader2Fill className="animate-spin text-white text-xl" />
                    </div>
                  </td>
                </tr>
              )}

              <tr
                className={`hover:bg-[#1a1a2e] transition cursor-pointer ${
                  runningRows.includes(seq.id) ? "opacity-50" : ""
                } ${
                  seq.is_active === false
                    ? "bg-[#1b1b2e]/40 italic text-gray-400"
                    : ""
                }`}
                onClick={() => handleViewSteps(seq)}
              >
                <td className="px-4 py-3 whitespace-nowrap">{seq.name}</td>
                <td className="px-4 py-3">
                  <span
                    className={
                      "text-xs font-medium px-2 py-1 rounded-md " +
                      statusColors[
                        seq.last_run_status?.toLowerCase() || "not-run"
                      ]
                    }
                  >
                    {seq.last_run_status || "Not-Run"}
                  </span>
                </td>
                <td className="px-4 py-3">
                  {seq.last_run_at
                    ? new Date(seq.last_run_at).toLocaleString()
                    : "-"}
                </td>
                <td className="relative px-4 py-3">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setMenuOpenIndex(menuOpenIndex === index ? null : index);
                    }}
                    className="p-1 rounded hover:bg-white/10"
                  >
                    <FiMoreHorizontal size={18} />
                  </button>
                  {menuOpenIndex === index && (
                    <div className="absolute right-4 top-10 z-40 w-40 bg-[#1e1e2d] border border-[#2e2e3e] rounded-md shadow-md">
                      {[
                        "Run Sequence",
                        "View steps",
                        "Delete",
                        seq.is_active === true
                          ? "Make Inactive"
                          : "Make Active",
                      ].map((action) => (
                        <button
                          key={action}
                          className="w-full text-left px-4 py-2 text-sm hover:bg-white/10"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (action === "Delete") {
                              handleDeleteSequence(seq);
                            } else if (action === "Run Sequence") {
                              handleRunSequence(seq);
                            } else if (action === "View steps") {
                              handleViewSteps(seq);
                            } else if (action === "Make Inactive") {
                              handleSequenceStatus(seq, false);
                            } else if (action === "Make Active") {
                              handleSequenceStatus(seq, true);
                            }

                            setMenuOpenIndex(null);
                          }}
                        >
                          {action}
                        </button>
                      ))}
                    </div>
                  )}
                </td>
              </tr>
            </React.Fragment>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Main component with accordion logic
export default function SequenceTable({
  sequences,
  setSequences,
  refetchSequences = () => {},
}: {
  sequences: any[];
  setSequences: React.Dispatch<React.SetStateAction<any[]>>;
  refetchSequences: () => void;
}) {
  const activeSequences = sequences?.filter(seq => seq.is_active !== false) || [];
  const inactiveSequences = sequences?.filter(seq => seq.is_active === false) || [];

  return (
    <div className="flex flex-col gap-6 max-h-[85vh] overflow-y-auto">
      {/* Active Sequences */}
      {activeSequences.length > 0 && (
        <div>
          <h2 className="text-lg font-semibold text-[#D1D1E3] mb-4">Active Sequences</h2>
          <SequenceTableContent
            sequences={activeSequences}
            setSequences={setSequences}
            refetchSequences={refetchSequences}
          />
        </div>
      )}

      {/* Inactive Sequences Accordion */}
      {inactiveSequences.length > 0 && (
        <div>
          <Accordion
            items={[
              {
                id: "inactive-sequences",
                title: `Inactive Sequences (${inactiveSequences.length})`,
                statusColor: "bg-gray-500",
                content: (
                  <SequenceTableContent
                    sequences={inactiveSequences}
                    setSequences={setSequences}
                    refetchSequences={refetchSequences}
                  />
                )
              }
            ]}
          />
        </div>
      )}
    </div>
  );
}
