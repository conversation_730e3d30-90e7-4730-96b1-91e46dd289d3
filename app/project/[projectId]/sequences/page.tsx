"use client";
import PrimaryButtonOutlined from "@/components/common/buttons/PrimaryButtonOutlined";
import React, { useEffect, useState } from "react";
import useSequences from "./hooks/useSequences";
import Image from "next/image";
import BottomSheet from "@/components/common/right-bottom-sheet/RightBottomSheet";
import CreateSequence from "./components/CreateSequence";
import SequenceTable from "./components/SequenceTable";
import { RiChat1Line, RiLoader2Fill } from "react-icons/ri";
import LoaderProgress from "@/components/common/loader-progress/LoaderProgress";
import EnvList from "@/components/test-runner/env-list/EnvList";
import { useGlobalStore } from "@/stores/globalstore";
import { PiPencilLine } from "react-icons/pi";
import Modal from "@/components/common/modal/Modal";
import FloatingDropdown from "@/components/common/floating-dropdown/FloatingDropdown";
import { RxGear } from "react-icons/rx";
import RuntimeSettings from "@/components/test-runner/runtime-settings/RuntimeSettings";
import PrimaryInput from "@/components/common/primary-input/PrimaryInput";
import SchedulerSettings from "./components/SchedulerSettings";
import useScheduler from "./hooks/useScheduler";
import { useParams } from "next/navigation";
import { getDataFromLocalStorage } from "@/temp-utils/globalUtilities";
import {
  LOCAL_STORAGE_DATA_KEYS,
  UserData,
} from "@/temp-utils/Constants/localStorageDataModels";
import { AgentChat } from './components/AgentChat';
import RightNavigation, { NavigationItem } from "@/components/test-runner/right-navigation/RightNavigation";
import { FaPlayCircle } from "react-icons/fa";

const SequencesPage = () => {
  const {
    sequences,
    flows,
    flowOptions,
    createSequences,
    setFlows,
    fetchAllFlows,
    fetchAllSequences,
    setSequences,
    handleVariableMapping,
    setVariableMapped,
    flowName,
    setFlowName,
    creationStep,
    setCreationStep,
    variableMapped,
    regressionTestId,
    handleRunAllSequencesByProjectId,
    mappingString,
    setMappingString,
    loadingPercentage,
    settingsOptionsList,
    envModal,
    runTimeSettingsModal,
    setEnvModal,
    setRunTimeSettingsModal,
    waitTime,
    setWaitTime,
  } = useSequences();

  const {
    emails,
    scheduleTime,
    scheduleType,
    handleSchedule,
    setEmails,
    setScheduleTime,
    setScheduleType,
    cronExpression,
    setCronExpression,
    dayOfMonth,
    setDayOfMonth,
    weekOptions,
    dayOfWeek,
    setDayOfWeek,
    getScheduler,
    enableScheduler,
    setEnableScheduler,
    methodType,
    toggleScheduler,
  } = useScheduler();

  const [isAgentChatOpen, setIsAgentChatOpen] = useState(false);

  const { projectId } = useParams();

  const userData: UserData = getDataFromLocalStorage(
    LOCAL_STORAGE_DATA_KEYS.USER_DATA
  ) as UserData;

  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const [schedulerModal, setSchedulerModal] = useState(false);
  const [isEditEnvironmentModalOpen, setIsEditEnvironmentModalOpen] =
    useState(false);

  const { setEnvironment, environment, triggerRevalidateTestsExplorer } =
    useGlobalStore();

  const [selectedEnvOptions, setSelectedEnvOptions] = useState<
    { value: string; label: string }[]
  >([]);

  const handleCreateSequence = async () => {
    setLoading(true);
    await createSequences();
    setLoading(false);
  };

  const handleCreateVariableMapping = async () => {
    const response = await handleVariableMapping();
    if (response.success) {
      setIsSheetOpen(false);
      await handleRefreshSequences();
    }
  };

  const handleOpenCreateSequenceSheet = () => {
    setIsSheetOpen(true);
  };

  const handleRefreshSequences = async () => {
    await fetchAllSequences();
  };

  const handleRunAllSequences = async () => {
    setIsRunning(true);
    await handleRunAllSequencesByProjectId();
    setIsRunning(false);
    await fetchAllSequences();
  };

  const fetchData = async () => {
    setLoading(true);

    await fetchAllFlows();
    await fetchAllSequences();
    setLoading(false);
  };

  const handleEditEnvironmentModal = () => {
    setIsEditEnvironmentModalOpen(true);
  };

  const getSchedulerData = async () => {
    await getScheduler();
  };

  useEffect(() => {
    getSchedulerData();
    fetchData();
  }, []);

  const navigationItems: NavigationItem[] = [{
    icon: 'chat-icon.svg',
    title: 'DrCode Chat',
    onClick: () => {
      setIsAgentChatOpen(true);
    }
  }]

  return (
    <div className="p-4">
      {loading && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-10">
          <RiLoader2Fill className="animate-spin text-white text-xl" />
        </div>
      )}

      {isRunning && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-10">
          <LoaderProgress progress={loadingPercentage} />
        </div>
      )}
      <div className="flex">

        <div className="w-full">
          <div className="flex justify-between items-center">
            <div className="">
              <h1 className="text-2xl font-bold text-[#D1D1E3]">All Sequences</h1>
              <p className="text-[#B2B2C1] font-normal text-sm">
                Create and manage flow sequences to test complete multi-step
                journeys.
              </p>
            </div>
            {sequences?.length !== 0 && (
              <div className="flex gap-5 items-center">
                <button
                  className="flex items-center gap-2 px-3 py-1.5 text-white bg-[#875BF8] hover:bg-drcodePurple/80 border border-drcodePurple rounded-md font-semibold text-sm"
                  onClick={handleRunAllSequences}
                >
                  <FaPlayCircle />
                  Run All Sequences
                </button>
                <FloatingDropdown
                  buttonContent={
                    <button className="button--outlined-primary py-2 px-2 text-[12px] flex gap-2">
                      <RxGear color="#875bf8" size={16} />
                    </button>
                  }
                >
                  <ul>
                    {[
                      ...settingsOptionsList,
                      {
                        id: 3,
                        name: "Scheduler Settings",
                        onClick: () => setSchedulerModal(true),
                      },
                    ].map((settingsOption) => {
                      return (
                        <p
                          onClick={settingsOption.onClick}
                          className="font-semibold text-[11px] rounded-md p-1 cursor-pointer hover:bg-drcodePurple/20 text-white"
                        >
                          {settingsOption.name}
                        </p>
                      );
                    })}
                  </ul>
                </FloatingDropdown>
                <PrimaryButtonOutlined onClick={handleOpenCreateSequenceSheet}>
                  + Create Sequences
                </PrimaryButtonOutlined>
              </div>
            )}
          </div>


          {sequences?.length === 0 && !loading ? (
            <div className="h-[80vh] flex items-center justify-center">
              <div className="flex flex-col items-center justify-center gap-4">
                <Image
                  src="/sequence_create.svg"
                  alt="Sequence Create"
                  height={100}
                  width={100}
                />
                <p className="text-[#B2B2C1] font-normal text-sm">
                  Create and manage flow sequences to test complete multi-step
                  journeys.
                </p>
                <PrimaryButtonOutlined onClick={handleOpenCreateSequenceSheet}>
                  + Create Sequences
                </PrimaryButtonOutlined>
              </div>
            </div>
          ) : (
            <>
              <div className="my-8">
                <SequenceTable
                  sequences={sequences}
                  setSequences={setSequences}
                  refetchSequences={handleRefreshSequences}
                />
              </div>

              {/* Add Agent Chat */}
              {/* <div className=""> <AgentChat /></div> */}

            </>
          )}
        </div>
        <div className="min-w-[6%]">
          <RightNavigation navigationItems={navigationItems} />
        </div>
      </div>




      {isSheetOpen && (
        <BottomSheet
          widthInPercentage="40%"
          isOpen={isSheetOpen}
          onClose={() => setIsSheetOpen(false)}
        >
          <CreateSequence
            flows={flows}
            setFlows={setFlows}
            flowOptions={flowOptions}
            onClose={() => {
              setIsSheetOpen(false);
              setCreationStep("flow");
            }}
            handleCreateSequence={handleCreateSequence}
            setVariableMapped={setVariableMapped}
            flowName={flowName}
            setFlowName={setFlowName}
            creationStep={creationStep}
            setCreationStep={setCreationStep}
            handleVariableMapping={handleCreateVariableMapping}
            variableMapped={variableMapped}
            regressionTestId={regressionTestId}
            mappingString={mappingString}
            setMappingString={setMappingString}
          />
        </BottomSheet>
      )}

      {envModal && (
        <Modal isOpen={envModal} onClose={() => setEnvModal(false)}>
          <EnvList
            environmentId={environment?.toString()}
            handleClose={() => setEnvModal(false)}
            setSelectedEnvOptions={setSelectedEnvOptions}
          />
        </Modal>
      )}

      {runTimeSettingsModal && (
        <Modal
          isOpen={runTimeSettingsModal}
          title={
            <>
              <div className="font-bold text-xl text-vscode-editor-foreground">
                Advanced Settings
              </div>
              <p className="text-gray-500 text-sm">
                Configure the test execution parameters.
              </p>
            </>
          }
        >
          <>
            <label htmlFor="waitTime" className="mb-1 text-[12px]">
              Timeout Duration for Request Completion (ms)
            </label>
            <PrimaryInput
              type="number"
              id="waitTime"
              min={0}
              value={waitTime}
              onChange={(e) => setWaitTime(Number(e.target.value))}
              // className="border border-drcodePurple/50 px-3 py-2 focus:outline-drcodePurple rounded-md bg-vscode-editor-background text-white"
              placeholder="e.g., 1000"
            />

            <div className="flex gap-2 items-end justify-end">
              <button
                onClick={() => setRunTimeSettingsModal(false)}
                className="button--primary mt-4 py-2 px-8 text-[14px]"
                style={{
                  fontSize: "14px",
                }}
              >
                Save Settings
              </button>
            </div>
          </>
        </Modal>
      )}

      {schedulerModal && (
        <Modal
          isOpen={schedulerModal}
          onClose={() => setSchedulerModal(false)}
          title="Scheduler Settings"
          onSave={async () => {
            const response = await handleSchedule({
              user_id: +1032,
              project_id: projectId as string,
              schedule_type: scheduleType,
              time: scheduleTime,
              emails: emails,
              cron_expression: cronExpression,
              dayOfWeek: dayOfWeek,
              dayOfMonth: dayOfMonth,
              methodType: methodType,
            });

            if (response.success) {
              setSchedulerModal(false);
            }
          }}
          onCancel={() => setSchedulerModal(false)}
          showFooterButtons={true}
        >
          <SchedulerSettings
            key={schedulerModal.toString()}
            setEmails={setEmails}
            setScheduleTime={setScheduleTime}
            setScheduleType={setScheduleType}
            emails={emails}
            scheduleTime={scheduleTime}
            scheduleType={scheduleType}
            cronExpression={cronExpression}
            setCronExpression={setCronExpression}
            dayOfMonth={dayOfMonth}
            setDayOfMonth={setDayOfMonth}
            weekOptions={weekOptions}
            dayOfWeek={dayOfWeek}
            setDayOfWeek={setDayOfWeek}
            enableScheduler={enableScheduler}
            setEnableScheduler={setEnableScheduler}
            toggleScheduler={toggleScheduler}
          />
        </Modal>
      )}

      {isAgentChatOpen && (
        <BottomSheet
          widthInPercentage="35%"
          isOpen={isAgentChatOpen}
          onClose={() => setIsAgentChatOpen(false)}
        >
          <div className="p-2">
            <AgentChat handleCloseChat={() => setIsAgentChatOpen(false)} />
          </div>
        </BottomSheet>
      )}
    </div>
  );
};

export default SequencesPage;
