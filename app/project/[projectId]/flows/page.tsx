"use client";

import { Checkbox } from "@mui/material";
import { styled } from "@mui/material/styles";
import { ButtonOutlinedPrimary } from "@/components/common/button-outlined-primary/ButtonOutlinedPrimary";
import DrCodeTable from "@/components/common/drcode-table/DrCodeTable";
import { DrCodeTableColumn } from "@/components/common/drcode-table/DrCodeTableTypes";
import Loader from "@/components/common/loader/Loader";
import EnvList from "@/components/test-runner/env-list/EnvList";
import StatList from "@/components/test-runner/stat-list/StatList";
import { useGlobalStore } from "@/stores/globalstore";
import axios from "axios";
import Image from "next/image";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import styles from "../../project.module.scss";
import { produce } from "immer";
import { toast } from "react-toastify";
import ProjectList from "@/components/common/project-list/ProjectList";
import AddApiPostman from "@/components/add-api-postman/AddApiPostman";
import { ButtonPrimary } from "@/components/common/button-primary/ButtonPrimary";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import { getDataFromLocalStorage } from "@/temp-utils/globalUtilities";
import { Tooltip } from "@/components/common/tooltip/Tooltip";
import {
  LOCAL_STORAGE_DATA_KEYS,
  UserData,
} from "@/temp-utils/Constants/localStorageDataModels";
import {
  MAX_PAYLOAD_SIZE,
  samplePostmanCollection,
} from "@/temp-utils/Constants/samplePostmanCollection";
import {
  ApiInfo,
  loadPostmanCollection,
  PostmanCollection,
} from "@/temp-utils/Services/parsingPostmanCollection";
import { KeyValuePair } from "tailwindcss/types/config";
import Modal from "@/components/common/modal/Modal";
import PrimaryInput from "@/components/common/primary-input/PrimaryInput";
import useToast from "@/hooks/useToast";
import "../customCheck.scss";
import { LuUserRound } from "react-icons/lu";
import { IoChevronDownOutline } from "react-icons/io5";

import Sidenav from "@/components/common/sidenav/Sidenav";
import { CustomCheckedIcon, CustomIcon, CustomIndeterminateIcon } from "@/temp-utils/stylesIcons";
import { PiPencilLine } from "react-icons/pi";

export interface GroupTableData {
  id: number;
  group_name: string;
  description: string;
  env: any;
  project_id: number;
  created_at: string;
  updated_at: string;
  env_id: number;
}
export interface projectDetails {
  id: number;
  project_name: string;
  user_id: string;
  created_at: string;
  updated_at: string;
}

export interface ProjectStats {
  total: number;
  executed: number;
  passed: number;
  failed: number;
}

export interface EnvironmentVariable {
  key: string;
  value: string;
}


export default function ProjectDetailsPage({ params }: any) {
  const { showToast } = useToast();
  const { projectId } = useParams();
  const [projectDetail, setProjectDetail] = useState<projectDetails>();
  const [groupDetail, setGroupDetails] = useState<GroupTableData>({
    created_at: "",
    description: "",
    env: null,
    env_id: -1,
    group_name: "",
    id: -1,
    project_id: -1,
    updated_at: "",
  });
  const [groupList, setGroupList] = useState<GroupTableData[]>([]);
  const { showLoader, triggerRevalidateTestsExplorer, setEnvironment } =
    useGlobalStore();
  const [groupName, setGroupName] = useState("");
  const [groupDescription, setGroupDescription] = useState("");
  const [showAddGroupPopup, setShowAddGroupPopup] = useState(false);
  const [showDeletePopup, setShowDeletePopup] = useState(false);
  const router = useRouter();
  const [postmanCollection, setPostmanCollection] = useState<any[]>([]);
  const [sampleCollection, setSampleCollection] = useState<string>(
    JSON.stringify(samplePostmanCollection, null, 4)
  );
  const [selectedApis, setSelectedApis] = useState<any[]>([]);
  const [showAddImportPopup, setShowAddImportPopup] = useState<boolean>(false);
  const [
    showExtractedEnvironmentVariables,
    setShowExtractedEnvironmentVariables,
  ] = useState<boolean>(false);
  const [projectStats, setProjectStats] = useState<ProjectStats>({
    total: 0,
    executed: 0,
    passed: 0,
    failed: 0,
  });
  const [keyValuePairs, setKeyValuePairs] = useState<EnvironmentVariable[]>([]);
  const [createdGroupEnvironmentId, setCreatedGroupEnvironmentId] =
    useState<number>(-1);
  const [isEdit, setIsEdit] = useState(false);
  const [showSelectApiModal, setShowSelectApiModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [generateModal, setGenerateModal] = useState(false);
  const [apiList, setApiList] = useState<[]>([]);
  const [refreshKey, setRefreshKey] = useState(0);

  const userData: UserData = getDataFromLocalStorage(
    LOCAL_STORAGE_DATA_KEYS.USER_DATA
  ) as UserData;

  const columns: DrCodeTableColumn<GroupTableData>[] = [
    { displayName: "Name", accessor: "group_name" },
    { displayName: "Description", accessor: "description" },
    {
      displayName: "Actions",
      accessor: "actions",
      dataRender: (row: GroupTableData) => (
        <div className="flex gap-2">
          {/* <button className='button--outlined-primary p-2 min-w-32 gap-2' onClick={(e) => { e.stopPropagation(); }}><Image src={'/play.png'} alt='run' width={15} height={15} quality={100} /> Run Folder</button> */}
          {/* <ButtonOutlinedPrimary text='View' size='small' onClick={(e) => { e.stopPropagation(); openGroupHandler(row.id) }} /> */}
          <ButtonOutlinedPrimary
            text="Edit"
            size="small"
            onClick={(e) => {
              handleEditClick(e, row);
            }}
          />
          <ButtonOutlinedPrimary
            text="Delete"
            size="small"
            onClick={(e) => {
              handleDeleteClick(e, row);
            }}
          />
        </div>
      ),
    },
  ];

  const getProject = async () => {
    try {
      //showLoader(true)
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/projects/${projectId}`
      );
      if (response.data.success) {
        setProjectDetail(response.data.data);
      }
    } catch (err) {
      //showLoader(false);
      console.error("Error:", err);
    } finally {
      //showLoader(true)
    }
  };

  const createEnvironmentVariables = async (data: any) => {
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables`,
        data
      );
      return response;
    } catch (error) {
      console.error("Error in creating environment variables:", error);
    }
  };

  const createGroup = async () => {
    setIsEdit(false);
    setShowAddGroupPopup(false);
    try {
      //showLoader(true)
      const envResponse = await createEnvironmentVariables({
        name: groupName,
        env: {},
        user_id: userData.user_id,
      });
      let environmentVariable = envResponse.data.id;

      let groupRequestObj = {
        groupName: groupName,
        projectId: projectDetail?.id,
        description: groupDescription,
        env: environmentVariable,
        env_id: environmentVariable,
      };

      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/groups`,
        groupRequestObj
      );
      if (response.data.success) {
        setCreatedGroupEnvironmentId(environmentVariable);
        setShowAddImportPopup(true);
        setGroupDescription("");
        setGroupName("");
        setGroupList((prev) => [...prev, response.data.data]);
        setGroupDetails(response.data.data);
        // toast.success("Folder created");
        showToast("Folder created", "success");
      }
    } catch (err) {
      //showLoader(false);
      console.error("Error:", err);
    } finally {
      //showLoader(false)
    }
  };

  const getGropupByProjectId = async () => {
    try {
      //showLoader(true)
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/groups/project/${projectId}`
      );
      if (response.data.success) {
        const e2eData = response.data.data?.filter((item)=>item.description === "This group has been created by chrome extension")
        // setGroupList(response.data.data);
        setGroupList(e2eData)
      }
    } catch (err) {
      //showLoader(false);
      console.error("Error:", err);
    } finally {
      //showLoader(true)
    }
  };

  const getProjectStats = async () => {
    try {
      //showLoader(true)
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/stats?projectId=${projectId}`
      );
      setProjectStats(response.data);
    } catch (err) {
      //showLoader(false);
      console.error("Error:", err);
    } finally {
      //showLoader(true)
    }
  };

  const updateGroup = async (groupDetail: GroupTableData) => {
    setShowAddGroupPopup(false);
    try {
      //showLoader(true)
      let groupRequestObj = {
        groupName: groupName,
        projectId: projectDetail?.id,
        description: groupDescription,
        env: 1,
      };

      const response = await axios.put(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/groups/${groupDetail.id}`,
        groupRequestObj
      );
      if (response.data.success) {
        // toast.success("Folder Update");
        showToast("Folder Updated", "success");

        setGroupList(
          produce(groupList, (draft) => {
            const index = draft.findIndex((item) => item.id === groupDetail.id);
            if (index !== -1) {
              draft[index].group_name = groupName;
              draft[index].description = groupDescription;
            }
          })
        );
      }
    } catch (err) {
      //showLoader(false);
      console.error("Error:", err);
    } finally {
      //showLoader(false);
    }
  };

  const clearStates = () => {
    setIsEdit(false);
    setGroupName("");
    setGroupDescription("");
    setGroupDetails({
      created_at: "",
      description: "",
      env: null,
      env_id: -1,
      group_name: "",
      id: -1,
      project_id: -1,
      updated_at: "",
    });
  };

  const handleDeleteClick = (event: any, row: GroupTableData) => {
    event.stopPropagation();
    if (row.group_name === "Default Folder") {
      // toast.error("Cannot delete Default Folder");
      showToast("Cannot delete Default Folder", "error");
      return;
    }
    setGroupDetails(row);
    setShowDeletePopup(true);
  };

  const handleEditClick = (event: any, row: GroupTableData) => {
    event.stopPropagation();
    if (row.group_name === "Default Folder") {
      // toast.error("Cannot edit Default Folder");
      showToast("Cannot edit Default Folder", "error");
      return;
    }
    setIsEdit(true);
    setShowAddGroupPopup(true);
    setGroupName(row.group_name);
    setGroupDescription(row.description);
    setGroupDetails(row);
  };

  const deleteGroup = async (groupDetail: GroupTableData) => {
    setShowDeletePopup(false);
    try {
      showLoader(true);
      const response = await axios.delete(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/groups/${groupDetail.id}`
      );
      if (response.status == 204) {
        // toast.error("Folder deleted");
        showToast("Folder deleted", "error");
      }
      let filterGroupList = groupList?.filter(
        (item, index) => item.id !== groupDetail.id
      );
      setGroupList(filterGroupList);
      await getProjectStats();
    } catch (err) {
      showLoader(false);
      console.error("Error:", err);
    } finally {
      showLoader(false);
    }
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    if (!groupName) {
      // toast.error("Please enter folder name");
      showToast("Please enter folder name", "error");
      return;
    }
    if (!isEdit) {
      createGroup();
    } else {
      updateGroup(groupDetail);
    }
  };

  const onAddApi = async (postmanData = []) => {
    const postman_collection = postmanCollection
      .map((item) => (selectedApis.includes(item.id) ? item : null))
      .filter((item) => item);

    console.log(
      "postman collection is ",
      postman_collection.length,
      selectedApis.length
    );
    if (postmanCollection.length === 0 && postmanData.length === 0) {
      // toast.error("Please Add postman collection");
      showToast("Please Add postman collection", "error");
      return;
    }

    const playgroundPayload: {
      projectId: string | string[];
      groupId: string | string[];
      method: string;
      testSuiteName: string;
      url: string;
      request: {
        headers: any;
        path_params: {};
        query_params: {};
        request_body: any;
      };
    }[] = [];

    if (postmanData.length > 0) {
      playgroundPayload.push(...postmanData);
    } else {
      playgroundPayload.push(...postman_collection);
    }

    const data = playgroundPayload.map((api: any) => ({
      groupId: groupDetail.id,
      method: api.method,
      testSuiteName:
        api.controller === api.name
          ? api.name
          : `${api.controller} - ${api.name}`,
      url: api.url,
      request: api.details,
      description: api.description,
    }));

    try {
      //showLoader(true);

      const jsonString = JSON.stringify(data);

      const payloadSize = new Blob([jsonString]).size; // Browser

      if (payloadSize > MAX_PAYLOAD_SIZE) {
        setErrorMessage(
          `Payload size exceeds the  limit. Please reduce the number of selected APIs or optimize your data.`
        );
        return;
      }

      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testSuites/bulk`,
        data
      );
      if (response.data.success) {
        setShowExtractedEnvironmentVariables(true);
        handleLoadCollection();
        // toast.success("Added successfully");
        showToast("APIs added successfully", "success");
        triggerRevalidateTestsExplorer();
        setShowSelectApiModal(false);
        setShowAddImportPopup(false);
        setApiList(response.data.data);
      } else {
        setErrorMessage(
          response.data.message +
            "Error : " +
            (response.data.errors && response.data.errors[0]
              ? response.data.errors[0]
              : "")
        );
      }
    } catch (err: any) {
      //showLoader(false);

      // toast.success(err.response?.data?.message || "Something went wrong");
      showToast(err.response?.data?.message || "Something went wrong", "error");
      console.log(err);
    } finally {
      //showLoader(false);
    }
  };

  useEffect(() => {
    showLoader(true);

    Promise.all([getProject(), getGropupByProjectId()])
      .then(() => {
        showLoader(false);
      })
      .catch(() => {
        showLoader(false);
      });

    getProjectStats().then(() => {
    });
  }, []);

  const openPlaygroundHandler = async () => {
    const defaultGroup = groupList.find(
      (group: GroupTableData) => group.group_name === "Default Folder"
    );
    router.push(
      `/project/${projectId}/group/${defaultGroup.id}/testSuit/playground`
    );
  };

  const openPrdToWorkflowHandler = () => {
    router.push(`/project/${projectId}/prd-to-workflow`);
  };

  const openApiAuditHandler = () => {
    router.push(`/project/${projectId}/api-audit`);
  };

  const openGroupHandler = async (groupId: number) => {
    const envId = groupList.find(
      (group: GroupTableData) => group.id === groupId
    )?.env_id;
    setEnvironment(envId);
    router.push(`/project/${projectId}/group/${groupId}`);
  };

  const handleLoadCollection = async () => {
    if (sampleCollection) {
      try {
        const result = await loadPostmanCollection(
          JSON.parse(sampleCollection)
        );
        const envArray: EnvironmentVariable[] = result.env;
        setKeyValuePairs([...envArray, { key: "", value: "" }]);
      } catch (error) {
        console.error("Error loading collection:", error);
        throw error; // Propagate the error
      }
    } else {
      console.warn("No Postman collection data provided.");
      throw new Error("No Postman collection data provided.");
    }
  };

  const handleInputChange = (
    index: number,
    field: keyof KeyValuePair,
    value: string
  ) => {
    const updatedPairs = [...keyValuePairs];
    updatedPairs[index][field] = value;
    if (
      index === keyValuePairs.length - 1 &&
      (updatedPairs[index].key || updatedPairs[index].value)
    ) {
      updatedPairs.push({ key: "", value: "" });
    }
    setKeyValuePairs(updatedPairs);
  };

  const deleteKeyValuePair = (index: number) => {
    const filterArray = keyValuePairs.filter(
      (value, keyValueindex) => index != keyValueindex
    );
    setKeyValuePairs(filterArray);
  };

  const updateEnvironment = async () => {
    try {
      //showLoader(true)
      let environmentRequestObj: any = {
        env: {},
        user_id: userData.user_id,
      };
      let emptyObject = environmentRequestObj?.env;
      keyValuePairs.pop();
      keyValuePairs.map((item) => {
        emptyObject[item.key] = item.value;
      });
      const response = await axios.put(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables/${createdGroupEnvironmentId}`,
        environmentRequestObj
      );
      if (response.status == 200) {
        // toast.success("Environment Updated");
        showToast("Environment Updated", "success");
        setShowExtractedEnvironmentVariables(false);
        // setGenerateModal(true);
        generateTestCases();
      }
    } catch (err) {
      //showLoader(false);
      console.error("Error:", err);
    } finally {
      //showLoader(false)
    }
  };

  const generateTestCases = async () => {
    try {
      const data = apiList.map((api: any) => ({
        method: api.method,
        test_suite_id: api.id,
        testSuiteName: `${api.test_suite_name}`,
        url: api.url,
        request: api.request,
        // environment_variables:,
        path_params: api.request?.path_params,
        query_parmas: api.request?.query_params,
        headers: api.request?.headers,
        json_body: api.request?.request_body,
        user_input: api.user_input || {},
        description: api.description,
        requires_auth:
          "Authorization" in api || "authorization" in api
            ? api.request?.headers
            : "",
      }));

      const payloadData = {
        apiList: data,
        userId: userData.user_id,
      };

      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testCases/bulkGenerateTestCases`,
        payloadData
      );

      if (response.data.success) {
        // toast.success("Test cases generated successfully");
        showToast("Test cases generation started", "success");
        router.push(`/project/${projectId}/group/${groupDetail.id}`);
      }
    } catch (error) {
      // toast.error("Something went wrong, please try again later.");
      showToast("Something went wrong, please try again later.", "error");
    }
  };
  useEffect(() => {
    setSelectedApis(postmanCollection.map((item) => item.id));
  }, [postmanCollection]);

  const getMethodClasses = (method: string) => {
    switch (method.toUpperCase()) {
      case "GET":
        return "text-green-500 bg-green-500/20"; // Increased contrast
      case "POST":
        return "text-orange-500 bg-orange-500/20"; // More vibrant
      case "PUT":
        return "text-purple-600 bg-purple-600/20"; // Darker shade for better visibility
      case "PATCH":
        return "text-indigo-400 bg-indigo-400/20"; // Using indigo for better contrast
      case "DELETE":
        return "text-red-500 bg-red-500/20"; // More visible red
      default:
        return "";
    }
  };

  const handleCheckboxChange = (id: string, isChecked: boolean) => {
    setSelectedApis((prevSelected) =>
      isChecked
        ? [...prevSelected, id]
        : prevSelected.filter((apiId) => apiId !== id)
    );
  };

  const handleSelectAllChange = (isChecked: boolean) => {
    setSelectedApis(isChecked ? postmanCollection.map((api) => api.id) : []);
  };

  return (
    <div className="h-screen flex w-full">
      {/* <div className="flex justify-between items-center p-4 py-1 bg-drCodeDarkBlue">
        <Image
          className="cursor-pointer"
          src={`/primary_logo.svg`}
          alt="dr-code-logo"
          width={120}
          height={120}
        />

        <FloatingDropdown
          buttonContent={
            <div className="flex items-center gap-1 cursor-pointer">
              <div className="h-10 w-10 rounded-full bg-drcodeBlue flex items-center justify-center">
                <LuUserRound />
              </div>
              <IoChevronDownOutline />
            </div>
          }
        >
          <LogoutButton />
        </FloatingDropdown>
      </div> */}
      {/* <Sidenav /> */}

      <div className="p-6 h-screen overflow-auto w-full">
        <div className="mb-8 flex justify-between items-center">
          <p className="text-[#D1D1E3] title--large font-bold text-2xl">
            Welcome back, {userData.name}
          </p>

          <div className="flex gap-2">
            {/* <div
              className="underline py-2 cursor-pointer text-[14px] font-semibold"
              onClick={() => {
                router.push(`/project/${projectId}/sequences`);
              }}
            >
              All Sequences
            </div>

            <div
              className="underline px-4 py-2 cursor-pointer text-[14px] font-semibold"
              onClick={openApiAuditHandler}
            >
              API Audit
            </div>

            <div
              className="underline px-4 py-2 cursor-pointer text-[14px] font-semibold"
              onClick={openPlaygroundHandler}
            >
              Test Single API
            </div> */}

            {/* <ProjectList className="min-w-48" /> */}
          </div>
        </div>

        <div className="px-0">
          {/* <StatList
            allTestCount={projectStats.total}
            executedCount={projectStats.executed}
            failedCount={projectStats.failed}
            passedCount={projectStats.passed}
            selectedFilter=""
            onFilter={() => {}}
            isProjectView={true}
          /> */}
          <div className="flex justify-between items-center">
            <p className="text-xl font-bold my-4">
              Folders ({groupList ? groupList.length : ""})
            </p>
            {/* <ButtonPrimary
              onClick={() => {
                setShowAddGroupPopup(true);
                clearStates();
              }}
              text="Import API Collection"
              size="medium"
            /> */}
          </div>
        </div>

        <DrCodeTable
          rowExpandedRender={() => <></>}
          onRowExpanded={(id) => {
            openGroupHandler(groupList[id].id);
          }}
          className="w-full"
          columns={columns}
          data={groupList}
        />

        {showAddGroupPopup && (
          <Modal
            isOpen={showAddGroupPopup}
            onClose={() => setShowAddGroupPopup(false)}
            title={
              <>
                <p className="text-background text-xl font-inter font-semibold">
                  {`${!isEdit ? "Create a" : "Update"}`} Folder
                </p>
                <span
                  className="text-[#9494A1] text-sm"
                  style={{ lineHeight: "unset" }}
                >
                  {!isEdit
                    ? "Keep your APIs in line — because even code deserves acozy home!"
                    : "Rename it till you are out of ideas!"}
                </span>
              </>
            }
            className="max-w-[600px]"
            showFooterButtons={true}
            saveLabel="Continue"
            cancelLabel="Close"
            onSave={handleSubmit}
            onCancel={() => {
              setShowAddGroupPopup(false);
              clearStates();
            }}
          >
            <div className="relative min-w-[520px]">
              <form>
                <p className="title--small" style={{ fontSize: "14px" }}>
                  Name
                </p>
                {/* <input
                value={groupName}
                onChange={(e) => setGroupName(e.target.value)}
                className="primary-input w-full"
                required
                placeholder="e.g., Secret Mission"
                type="text"
              /> */}
                <PrimaryInput
                  value={groupName}
                  onChange={(e) => setGroupName(e.target.value)}
                  placeholder="e.g., Secret Mission"
                />
                <p className="title--small mt-3" style={{ fontSize: "14px" }}>
                  Description (Optional)
                </p>
                {/* <input
                value={groupDescription}
                onChange={(e) => setGroupDescription(e.target.value)}
                className="primary-input w-full "
                placeholder="e.g., What’s the mission, Chief?"
              /> */}
                <PrimaryInput
                  value={groupDescription}
                  onChange={(e) => setGroupDescription(e.target.value)}
                  placeholder="e.g., What’s the mission, Chief?"
                />
                {/* <div className="flex justify-end gap-4 items-center">
                <button
                  type="submit"
                  className="button--primary mt-4 py-2 px-8 text-[14px]"
                  style={{ fontSize: "14px" }}
                >
                  {isEdit ? "Update" : "Continue"}
                </button>
                <button
                  type="button"
                  onClick={() => setShowAddGroupPopup(false)}
                  className="button--outlined-primary mt-4 py-2 px-8 text-[14px]"
                  style={{ fontSize: "14px" }}
                >
                  Close
                </button>
              </div> */}
              </form>
            </div>
          </Modal>
        )}

        {showAddImportPopup && (
          <Modal
            isOpen={showAddImportPopup}
            onClose={() => setShowAddImportPopup(false)}
            title={
              <>
                <div className="font-inter">
                  <p className="  text-background text-2xl font-inter font-semibold ">
                    Drop in your APIs
                  </p>
                  <span className="text-[#9494A1] text-sm">
                    Import your APIs to launch the testing mission!
                  </span>
                </div>
              </>
            }
            showFooterButtons={true}
            showBackButton={true}
            saveLabel="Continue"
            cancelLabel="Close"
            backLabel="Back"
            onSave={() => {
              setShowSelectApiModal(true);
            }}
            onCancel={() => {
              setShowAddImportPopup(false);
            }}
            onBack={() => {
              setShowAddImportPopup(false);
              setShowAddGroupPopup(true);
            }}
            isDisabled={postmanCollection.length === 0}
          >
            <div className="relative">
              <AddApiPostman
                setSampleCollection={setSampleCollection}
                setPostmanCollection={setPostmanCollection}
                groupId={groupList[groupList.length - 1]?.id}
                setShowSelectApiModal={setShowSelectApiModal}
                key={refreshKey}
                setErrorMessage={setErrorMessage}
              />

              {errorMessage && (
                <div className="text-red-500  mt-2 text-[12px]">
                  {errorMessage}
                </div>
              )}
              <div
                onClick={() => {
                  loadPostmanCollection(
                    samplePostmanCollection as PostmanCollection
                  ).then((data) => {
                    console.log("data", data);
                    setPostmanCollection(data.apis);
                    const selectedApisIds = data.apis.map((item) => item.id);
                    setSelectedApis(selectedApisIds);
                    setShowSelectApiModal(true);
                    // onAddApi(data.apis);
                  });
                }}
                className="text-xs text-drcodePurple mt-2 hover:underline cursor-pointer"
              >
                Don't have an API ready? Try with a sample API instead!
              </div>
              {/* <div className="flex justify-between">
              <button
                type="button"
                onClick={() => {
                  setShowAddImportPopup(false);
                  setShowAddGroupPopup(true);
                }}
                className="button--outlined-primary mt-4 py-2 px-8 text-[14px]"
                style={{ fontSize: "14px" }}
              >
                Back
              </button>
              <div className="flex justify-end gap-4 items-center">
                <button
                  onClick={() => {
                    setShowSelectApiModal(true);
                  }}
                  className="button--primary mt-4 py-2 px-8 text-[14px]"
                  style={{ fontSize: "14px" }}
                  disabled={postmanCollection.length === 0}
                >
                  Continue
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowAddImportPopup(false);
                  }}
                  className="button--outlined-primary mt-4 py-2 px-8 text-[14px]"
                  style={{ fontSize: "14px" }}
                >
                  Close
                </button>
              </div>
            </div> */}
            </div>
          </Modal>
        )}

        {showSelectApiModal && (
          <Modal
            isOpen={showSelectApiModal}
            onClose={() => {
              setShowSelectApiModal(false);

              setSelectedApis([]);
              setErrorMessage("");
            }}
            title={
              <>
                <p className="text-background text-2xl font-inter font-semibold">
                  Select APIs
                </p>
                <span className="text-[#9494A1] text-sm">
                  Ready for testing? Pick your targets for the mission!
                </span>
              </>
            }
            // footer={
            //   <div className="flex justify-between">
            //     <button
            //       type="button"
            //       onClick={() => {
            //         setShowSelectApiModal(false);
            //         setSelectedApis([]);
            //         setPostmanCollection([])
            //         setErrorMessage("");
            //         setRefreshKey(refreshKey + 1);
            //       }}
            //       className="button--outlined-primary mt-4 py-2 px-8 text-[14px]"
            //       style={{ fontSize: "14px" }}
            //     >
            //       Back
            //     </button>
            //     <div className="flex justify-end gap-4 items-center">
            //       <button
            //         onClick={() => {
            //           onAddApi();
            //         }}
            //         className="button--primary mt-4 py-2 px-8 text-[14px]"
            //         style={{ fontSize: "14px" }}
            //         disabled={selectedApis.length === 0}
            //       >
            //         Continue
            //       </button>
            //       <button
            //         type="button"
            //         onClick={() => {
            //           setShowSelectApiModal(false);
            //           setShowAddGroupPopup(false);
            //           setShowAddImportPopup(false);
            //           setSelectedApis([]);
            //           setErrorMessage("");
            //         }}
            //         className="button--outlined-primary mt-4 py-2 px-8 text-[14px]"
            //         style={{ fontSize: "14px" }}
            //       >
            //         Close
            //       </button>
            //     </div>
            //   </div>
            // }
            showFooterButtons={true}
            showBackButton={true}
            saveLabel="Continue"
            cancelLabel="Close"
            backLabel="Back"
            onSave={() => {
              onAddApi();
            }}
            onCancel={() => {
              setShowSelectApiModal(false);
              setSelectedApis([]);
              setPostmanCollection([]);
              setErrorMessage("");
            }}
            onBack={() => {
              setShowSelectApiModal(false);
              setShowAddImportPopup(true);
            }}
            isDisabled={selectedApis.length === 0}
          >
            <div className="flex justify-between items-center text-[14px]">
              <div className="mb-2 flex items-center gap-2">
                {/* <input
                type="checkbox"
                name=""
                id=""
                className=" h-[15px] w-[15px] bg-[#2E2E60] border-[1px] checked:border-white border-[#565F6B] cursor-pointer rounded-[3px]"
                checked={
                  postmanCollection.length > 0 &&
                  selectedApis.length === postmanCollection.length
                }
                onChange={(e) => handleSelectAllChange(e.target.checked)}
              /> */}

                <Checkbox
                  checked={
                    postmanCollection.length > 0 &&
                    selectedApis.length === postmanCollection.length
                  }
                  onChange={(e) => handleSelectAllChange(e.target.checked)}
                  icon={<CustomIcon />}
                  checkedIcon={
                    <CustomCheckedIcon>
                      <svg className="font-bold" viewBox="0 0 24 24">
                        <path
                          d="M9 16.2l-4.2-4.2L4 13l5 5 12-12-1.4-1.4z"
                          fill="#FFFFFF"
                        />
                      </svg>
                    </CustomCheckedIcon>
                  }
                  indeterminateIcon={<CustomIndeterminateIcon />}
                />
                {/* <span>Select All</span> */}
                <label className="text-sm">Select all</label>
              </div>

              <div className="flex">
                <span>
                  {selectedApis.length} / {postmanCollection.length} selected
                </span>
              </div>
            </div>
            <div className="bg-transparent border-1 border-white rounded-md p-2">
              <div className="max-h-[270px] overflow-y-auto">
                {postmanCollection.map((api) => {
                  return (
                    <div className="flex item-center gap-2 mb-4">
                      <label className="flex items-center space-x-2 cursor-pointer">
                        {/* <input
                        type="checkbox"
                        checked={selectedApis.includes(api.id)}
                        onChange={(e) =>
                          handleCheckboxChange(api.id, e.target.checked)
                        }
                      /> */}

                        <Checkbox
                          checked={selectedApis.includes(api.id)}
                          onChange={(e) =>
                            handleCheckboxChange(api.id, e.target.checked)
                          }
                          icon={<CustomIcon />}
                          checkedIcon={
                            <CustomCheckedIcon>
                              <svg viewBox="0 0 24 24">
                                <path
                                  d="M9 16.2l-4.2-4.2L4 13l5 5 12-12-1.4-1.4z"
                                  fill="#FFFFFF"
                                />
                              </svg>
                            </CustomCheckedIcon>
                          }
                        />
                      </label>

                      <div>
                        <p className="text-[11px] font-medium text-[#B2B2C1] font-sans">
                          {api.controller === api.name
                            ? api.name
                            : `${api.controller} - ${api.name} `}
                        </p>
                        <div className="text-[11px] mt-[6px] leading-[11px] text-[#EBEBF3] font-medium font-sans text-wrap">
                          <span
                            className={`text-[9px] font-medium p-[6px] mr-[10px] rounded-[4px] ${getMethodClasses(
                              api.method ? api.method : ""
                            )}`}
                          >
                            {api.method}
                          </span>
                          {api.url}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {errorMessage && (
              <div className="text-red-500 text-sm mt-4">{errorMessage}</div>
            )}
          </Modal>
        )}

        {showExtractedEnvironmentVariables && (
          <Modal
            isOpen={showExtractedEnvironmentVariables}
            onClose={() => {
              setShowExtractedEnvironmentVariables(false);
              setKeyValuePairs([]);
            }}
            size="large"
            title={
              <>
                <p className="text-background text-xl font-inter font-semibold">
                  Review Environment Variables
                </p>
                <span className="text-[#9494A1] text-sm">
                  Customize auto-fetched API variables for your tests.
                </span>
              </>
            }
            showFooterButtons={true}
            saveLabel="Generate"
            cancelLabel="Close"
            onSave={() => {
              updateEnvironment();
            }}
            onCancel={() => {
              setShowExtractedEnvironmentVariables(false);
              setKeyValuePairs([]);
            }}
          >
            <div className="relative min-w-[670px] max-h-[360px] overflow-auto">
              <div className={`${styles["key-value-table"]}`}>
                <div className="flex w-full gap-4 justify-between mb-2">
                  <span className="text-left text-[#9494A1] text-[14px] w-[48%]">
                    Variable
                  </span>
                  <span className="text-left text-[#9494A1] text-[14px] w-[52%]">
                    Value
                  </span>
                </div>
                {keyValuePairs.length > 0 &&
                  keyValuePairs.map((pair, index) => (
                    <div
                      key={index}
                      className={`${styles["key-value-container"]} flex gap-2 items-center  mb-2  `}
                    >
                      {/* <input
                      value={pair.key}
                      onChange={(event) =>
                        handleInputChange(index, "key", event.target.value)
                      }
                      className="primary-input !rounded-none text-white"
                      placeholder="Enter Variable"
                    /> */}
                      <PrimaryInput
                        value={pair.key}
                        onChange={(e) =>
                          handleInputChange(index, "key", e.target.value)
                        }
                        placeholder="Enter Variable"
                      />
                      {/* <input
                      value={pair.value}
                      onChange={(event) =>
                        handleInputChange(index, "value", event.target.value)
                      }
                      className="primary-input !rounded-none  text-white w-[90%]"
                      placeholder="Enter value"
                    /> */}
                      <PrimaryInput
                        value={pair.value}
                        onChange={(e) =>
                          handleInputChange(index, "value", e.target.value)
                        }
                        placeholder="Enter value"
                      />
                      <div
                        className={`${styles["trash-bin-container"]}`}
                        onClick={() => deleteKeyValuePair(index)}
                      >
                        <Image
                          src="/trashbin.svg"
                          alt=""
                          width={35}
                          height={35}
                        />
                      </div>
                    </div>
                  ))}
              </div>
              {/* <div className="flex gap-4 justify-end mt-6">
           
              <button
                onClick={() => {
                  updateEnvironment();
                }}
                className="button--primary mt-4 py-2 px-8 text-[14px]"
                style={{ fontSize: "14px" }}
              >
                Contnue
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowExtractedEnvironmentVariables(false);
                }}
                className="button--outlined-primary mt-4 py-2 px-8 text-[14px]"
                style={{ fontSize: "14px" }}
              >
                Close
              </button>
            </div> */}
            </div>
          </Modal>
        )}

        {generateModal && (
          <Modal isOpen={generateModal}>
            <div className="mt-0 text-center flex flex-col items-center justify-center">
              {/* SVG Icon */}
              <svg
                width="50"
                height="50"
                viewBox="0 0 50 50"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M25.009 3.1377C37.0882 3.1377 46.8841 12.9337 46.8841 25.0129C46.8841 37.0921 37.0882 46.888 25.009 46.888C12.9298 46.888 3.13379 37.0921 3.13379 25.0129C3.13379 12.9337 12.9298 3.1377 25.009 3.1377ZM20.4663 32.1181L15.1107 26.7581C14.1983 25.8451 14.1981 24.3564 15.1107 23.4436C16.0235 22.531 17.5189 22.5367 18.425 23.4436L22.2007 27.2222L31.5932 17.8297C32.506 16.9169 33.9949 16.9169 34.9075 17.8297C35.8203 18.7423 35.819 20.2325 34.9075 21.144L23.8552 32.1964C22.9437 33.1078 21.4535 33.1091 20.5409 32.1964C20.5152 32.1707 20.4905 32.1447 20.4663 32.1181Z"
                  fill="#15B097"
                />
              </svg>
              <div className="flex flex-col justify-center items-center">
                <div className="text-2xl leading-7 text-[#D1D1E3] font-bold font-sans">
                  APIs powered up! 🎉
                </div>
                <div className="text-[#D1D1E3] font-normal text-base leading-6 text-center my-4">
                  Your APIs are in, ready to rock the tests?
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-4 items-center">
              <button
                onClick={() => {
                  generateTestCases();
                }}
                className="button--primary mt-4 py-2 px-8 text-[14px]"
                style={{ fontSize: "14px" }}
              >
                Generate
              </button>
              <button
                type="button"
                onClick={() => {
                  setGenerateModal(false);
                }}
                className="button--outlined-primary mt-4 py-2 px-8 text-[14px]"
                style={{ fontSize: "14px" }}
              >
                Close
              </button>
            </div>
          </Modal>
        )}

        {showDeletePopup && (
          <Modal
            isOpen={showDeletePopup}
            onClose={() => setShowDeletePopup(false)}
            className="w-[400px]"
            size="small"
            title={
              <p className="text-background text-xl font-inter font-semibold">
                Are you sure you want to delete?
              </p>
            }
          >
            <div className="flex gap-2 justify-end">
              <button
                onClick={() => deleteGroup(groupDetail)}
                className="button--primary mt-4 py-2 px-8 text-[14px]"
                style={{ fontSize: "14px" }}
              >
                Yes
              </button>
              <button
                onClick={() => setShowDeletePopup(false)}
                className="button--outlined-primary  mt-4 py-2 px-8 text-[14px]"
                style={{ fontSize: "14px" }}
              >
                No
              </button>
            </div>
          </Modal>
        )}
      </div>
    </div>
  );
}
