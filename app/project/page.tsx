'use client'
import { ButtonOutlinedPrimary } from '@/components/common/button-outlined-primary/ButtonOutlinedPrimary';
import DrCodeTable from '@/components/common/drcode-table/DrCodeTable';
import { DrCodeTableColumn } from '@/components/common/drcode-table/DrCodeTableTypes';
import { ApiTestDetails } from '@/components/test-runner/tests-table/TestsTable';
import Image from 'next/image';
import Link from 'next/link';
import styles from "./project.module.scss"
import { ButtonPrimary } from '@/components/common/button-primary/ButtonPrimary';
import { useEffect, useState } from 'react';
import axios from 'axios';
import { produce } from 'immer';
import { useGlobalStore } from '@/stores/globalstore';
import { toast } from 'react-toastify';
import { useRouter } from 'next/navigation';
import { LOCAL_STORAGE_DATA_KEYS, UserData } from '@/temp-utils/Constants/localStorageDataModels';
import { getDataFromLocalStorage } from '@/temp-utils/globalUtilities';
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from '@/temp-utils/Constants/globalVariables';

export interface ProjectTableData {
	id: number;
	project_name: string;
}

export interface IProjectList {
	id: number
	project_name: string
	user_id: string
	created_at: string
	updated_at: string
}

export default function ProjectPage() {

	const [projectList, setProjectList] = useState<IProjectList[]>([])
	const { showLoader } = useGlobalStore();
	const router = useRouter();
	const userData: UserData = getDataFromLocalStorage(LOCAL_STORAGE_DATA_KEYS.USER_DATA) as UserData;


	const createProject = async () => {
		const payload = {
			"projectName": 'Default Project',
			"userId": userData.user_id
		}
		try {
			//showLoader(true)
			const response = await axios.post(`${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/projects`, payload);
			return response.data.data.id;
		} catch (err) {
			console.error('Error:', err);
		} finally {
			//showLoader(false)
		}
	};

	const createEnvironmentVariables = async (data: any) => {
		try {
			const response = await axios.post(`${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables`, data);
			return response
		} catch (error) {
			console.error("Error in creating environment variables:", error)
		}
	}

	const createGroup = async (projectId: number) => {
		const response = await createEnvironmentVariables({
			name: "Default Environment",
			env: {},
			user_id: userData.user_id,
		})
		let environmentVariable = response.data.id

		const payload = {
			groupName: "Default Folder",
			projectId: projectId,
			description: "This is a Default Folder",
			env: environmentVariable,
			env_id: environmentVariable,
		}

		try {
			// Create new group
			const response = await axios.post(`${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/groups`, payload)
		} catch (error) {
			console.error(error)
		}
	}

	const getProjectsByUser = async () => {
		try {
			const response = await axios.get(`${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/projects/user/${userData.user_id}`);
			if (response.data.success) {
				let responseData = response.data.data;
	
				// If no projects exist, create one and get the ID
				if (responseData.length === 0) {
					const projectId = await createProject();
					await createGroup(projectId);
					
					// Ensure project list is updated and then navigate
					setProjectList(responseData);
					router.push(`/project/${projectId}`);
				} else {
					// If projects exist, navigate to the first project
					router.push(`/project/${responseData[0].id}`);
					setProjectList(responseData);
				}
			}
		} catch (err) {
			console.error('Error:', err);
		}
	};

	useEffect(() => {
    const fetchProjects = async () => {
        try {
            showLoader(true);
            await getProjectsByUser();
        } catch (error) {
            console.error("Error fetching projects:", error);
        } finally {
            showLoader(false);
        }
    };

    fetchProjects();
}, []);


	return (
		<>
		</>
	);
}
