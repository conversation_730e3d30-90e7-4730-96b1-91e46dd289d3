"use client";
import { EnvironmentList } from "@/app/environment/page";
import Breadcrumbs from "@/components/common/breadcrumbs/Breadcrumbs";
import { ButtonPrimary } from "@/components/common/button-primary/ButtonPrimary";
import { ButtonSoftPrimary } from "@/components/common/button-soft-primary/ButtonSoftPrimary";
import LoaderGif from "@/components/common/loader-gif/LoaderGif";
import Loader from "@/components/common/loader/Loader";
import Modal from "@/components/common/modal/Modal";
import PrimaryInput from "@/components/common/primary-input/PrimaryInput";
import TabView from "@/components/common/tabview/Tabview";
import TestCaseLoader from "@/components/common/test-case-loader/TestCaseLoader";
import useToast from "@/hooks/useToast";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import axios from "axios";
import { useParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import { FaRegTrashAlt, FaTrash } from "react-icons/fa";
import { toast } from "react-toastify";

const ProjectSettings = () => {
  const {showToast} = useToast();
  const [loading, setLoading] = useState(false);
  const { projectId } = useParams();
  const [projectName, setProjectName] = useState("");
  const [projectDetail, setProjectDetail] = useState<any>({});
  const [groupList, setGroupList] = useState([]);

  const handleUpdateEnvs = async ({ environmentId, environmentRequestObj }) => {
    try {
      await axios.put(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables/${environmentId}`,
        environmentRequestObj
      );

      // toast.success("Environment updated successfully");
      showToast("Environment updated successfully", "success");
    } catch (error) {
      // toast.error("Something went wrong!");
      showToast("Something went wrong!", "error");
    }
  };

  const updateProject = async () => {
    try {
      const response = await axios.put(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/projects/${projectId}`,
        {
          project_name: projectName,
          user_id: projectDetail?.user_id,
        }
      );
    } catch (error) {}
  };

  const getProjectGroups = async () => {
    try {
      //showLoader(true)
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/groups/project/${projectId}`
      );
      if (response.data.success) {
        setGroupList(response.data.data);
      }
    } catch (err) {
      //showLoader(false);
      console.error("Error:", err);
    } finally {
      //showLoader(true)
    }
  };

  const getProject = async () => {
    try {
      setLoading(true);
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/projects/${projectId}`
      );
      if (response.data.success) {
        setProjectDetail(response.data.data);
        setProjectName(response.data.data.project_name);
      }
    } catch (err) {
      console.error("Error:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getProjectGroups();
    getProject();
  }, []);

  const tabs = [
    {
      label: "General",
      content: (
        <GeneralSettings
          setProjectName={setProjectName}
          projectName={projectName}
          handleSave={updateProject}
        />
      ),
    },
    {
      label: "Enviroment",
      content: (
        <EnviromentSettings
          groupList={groupList}
          handleSave={handleUpdateEnvs}
        />
      ),
    },
  ];

  const items = [
    { label: "Home", href: "/" },
    { label: "Project Settings", href: "/project-settings" },
    {
      label: `Project #${projectId}` || "",
      href: "/blog/article",
    },
  ];

  if (loading) {
    return (
      <div className="h-screen flex justify-center items-center">
        <LoaderGif />
      </div>
    );
  }

  return (
    <div className="p-4">
      {/* Bread crumbs */}
      <Breadcrumbs items={items} />

      {/* Heading */}
      <div className="my-4">
        <h4 className=" text-[24px] text-[#D1D1E3] font-semibold leading-[120%] tracking-[-2%]">
          Project settings
        </h4>
        <p className="font-inter text-[14px] text-[#B2B2C1] font-normal leading-[140%] tracking-[0%]">
          You can manage your workspace-related information like API keys,
          environment variables, etc. from this page
        </p>
      </div>

      {/* Settings tab container */}

      <div className="rounded-[16px] min-h-[600px] bg-drcodeBlue p-[30px]">
        <TabView tabs={tabs} />
      </div>
    </div>
  );
};

export default ProjectSettings;

const GeneralSettings = ({
  projectName = "",
  setProjectName = () => {},
  handleSave = () => {},
}: {
  projectName: string;
  setProjectName: any;
  handleSave: () => void;
}) => {
  return (
    <div className="max-w-sm">
      <PrimaryInput
        border="primary--border"
        placeholder="Project name"
        value={projectName}
        onChange={(e) => setProjectName(e.target.value)}
        className="mb-4"
      />

      <ButtonPrimary size="small" text="Save" onClick={handleSave} />
    </div>
  );
};

const EnviromentSettings = ({
  groupList = [],
  handleSave = () => {},
}: {
  groupList: any[];
  handleSave: ({
    environmentId,
    environmentRequestObj,
  }: {
    environmentId: number;
    environmentRequestObj: EnvironmentList;
  }) => void;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeEnvId, setActiveEnvId] = useState<number | null>(null);
  const [activeEnv, setActiveEnv] = useState<EnvironmentList | null>(null);
  const [envPairs, setEnvPairs] = useState<[string, string][]>([["", ""]]);
  const [loading, setLoading] = useState(false);

  const handleEnvUpdate = ({
    value = "",
    type = "key",
    index = 0,
  }: {
    value: string;
    type: "key" | "value";
    index: number;
  }) => {
    const updatedEnvPairs = [...envPairs];

    if (type === "key") {
      updatedEnvPairs[index] = [value, updatedEnvPairs[index][1]];
    } else {
      updatedEnvPairs[index] = [updatedEnvPairs[index][0], value];
    }

    setEnvPairs(updatedEnvPairs);
  };

  const addNewRow = () => {
    setEnvPairs([...envPairs, ["", ""]]);
  };

  const removeRow = (index: number) => {
    if (envPairs.length > 1) {
      setEnvPairs(envPairs.filter((_, i) => i !== index));
    }
  };

  const handleConfirm = () => {
    const envObject = Object.fromEntries(
      envPairs.filter(([k]) => k.trim() !== "")
    );
    handleSave({
      environmentId: activeEnvId!,
      environmentRequestObj: {
        ...activeEnv,
        env: envObject,
      },
    });
    setIsOpen(false);
  };

  const getEnvironmentDetails = async (activeEnvId: number) => {
    try {
      setLoading(true);
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables/${activeEnvId}`
      );
      let responseData = response.data;
      setActiveEnv(responseData);
      // @ts-ignore
      setEnvPairs(Object.entries(responseData.env).concat([["", ""]]));
    } catch (err) {
      console.error("Error:", err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <p className="font-inter text-[14px] leading-[140%] tracking-[0] font-normal text-[#B2B2C1] mb-2">
        Environment is a set of one or more variables that you can reference
        when sending requests.
      </p>

      <ul>
        {groupList.map((group) => (
          <li
            className="mb-2 flex justify-between items-center border border-gray-700 shadow-sm shadow-gray-800 px-4 py-2 rounded-md"
            key={group.id}
          >
            <span className="text-[#B2B2C1] text-[14px]">
              {group?.group_name}
            </span>
            <ButtonSoftPrimary
              text="Edit"
              size="small"
              onClick={() => {
                setIsOpen(true);
                setActiveEnvId(group.env_id);
                getEnvironmentDetails(group.env_id);
              }}
            />
          </li>
        ))}
      </ul>

      {isOpen && (
        <Modal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          title={`Review ${activeEnv?.name}`}
          footer={
            <div className="flex justify-end space-x-2">
              <ButtonPrimary
                size="small"
                text="Confirm"
                onClick={handleConfirm}
              />
              <ButtonSoftPrimary
                text="Cancel"
                size="small"
                onClick={() => {
                  setIsOpen(false)
                  setEnvPairs([["", ""]])
                }}
              />
            </div>
          }
        >
          {loading ? (
            <>
              <Loader />
            </>
          ) : (
            <>
              <ul className="mb-2">
                {envPairs.map((item, idx) => (
                  <li className="flex items-center" key={idx}>
                    <div className="flex items-center w-[95%]">
                      <PrimaryInput
                        value={item[0]}
                        className="rounded-none text-white"
                        placeholder={idx === envPairs.length - 1 && "Enter Key"}
                        onChange={(e) =>
                          handleEnvUpdate({
                            index: idx,
                            type: "key",
                            value: e.target.value,
                          })
                        }
                      />
                      <PrimaryInput
                        value={item[1]}
                        className="rounded-none text-white"
                        placeholder={
                          idx === envPairs.length - 1 && "Enter Value"
                        }
                        onChange={(e) =>
                          handleEnvUpdate({
                            index: idx,
                            type: "value",
                            value: e.target.value,
                          })
                        }
                      />
                    </div>
                    {envPairs.length - 1 === idx && (
                      <button
                        className="text-[#B2B2C1] ml-2"
                        onClick={() => removeRow(idx)}
                      >
                        <FaRegTrashAlt />
                      </button>
                    )}
                  </li>
                ))}
              </ul>

              <ButtonSoftPrimary
                text="  + Add Row"
                size="small"
                onClick={addNewRow}
              />
            </>
          )}
        </Modal>
      )}
    </>
  );
};
