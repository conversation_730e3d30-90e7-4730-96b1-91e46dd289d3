"use client";
import React, { useEffect, useMemo, useState } from "react";
import styles from "./ProjectSetting.module.scss";
import TabList from "@/components/test-runner/api-test-detailed-view/blocks/tab-list/TabList";
import { ButtonPrimary } from "@/components/common/button-primary/ButtonPrimary";
import { ButtonOutlinedPrimary } from "@/components/common/button-outlined-primary/ButtonOutlinedPrimary";
import DrCodeTable from "@/components/common/drcode-table/DrCodeTable";
import { DrCodeTableColumn } from "@/components/common/drcode-table/DrCodeTableTypes";
import { useGlobalStore } from "@/stores/globalstore";
import axios from "axios";
import { produce } from "immer";
import Link from "next/link";
import { toast } from "react-toastify";
import { IProjectList, ProjectTableData } from "../project/page";
import Image from "next/image";
import { getDataFromLocalStorage } from "@/temp-utils/globalUtilities";
import {
  LOCAL_STORAGE_DATA_KEYS,
  UserData,
} from "@/temp-utils/Constants/localStorageDataModels";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import { useRouter } from "next/navigation";
import Modal from "@/components/common/modal/Modal";
import PrimaryInput from "@/components/common/primary-input/PrimaryInput";
import useToast from "@/hooks/useToast";
const ProjectSetting = () => {
  const { showToast } = useToast();

  const [projectName, setProjectName] = useState("");

  const [projectDetials, setProjectDetails] = useState<any>();
  const [showAddProjectPopup, setShowAddProjectPopup] = useState(false);
  const [projectList, setProjectList] = useState<IProjectList[]>([]);
  const [showDeletePopup, setShowDeletePopup] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const { showLoader } = useGlobalStore();
  const userData: UserData = getDataFromLocalStorage(
    LOCAL_STORAGE_DATA_KEYS.USER_DATA
  ) as UserData;

  const data: ProjectTableData[] = [
    {
      id: 1,
      project_name: "Airia",
    },
    {
      id: 3,
      project_name: "Mindler",
    },
  ];
  const handleDeleteClick = (event: any, row: ProjectTableData) => {
    if (row.project_name == "Default Project") {
      // toast.error("Cannot delete Default project");
      showToast("Cannot delete Default project", "error");
      return;
    }
    event.stopPropagation();
    setProjectDetails(row);
    setShowDeletePopup(true);
  };

  const handleEditCLick = (event: any, row: ProjectTableData) => {
    if (row.project_name == "Default Project") {
      // toast.error("Cannot edit Default project");
      showToast("Cannot edit Default project", "error");
      return;
    }
    event.stopPropagation();
    setIsEdit(true);
    setShowAddProjectPopup(true);
    setProjectName(row.project_name);
    setProjectDetails(row);
  };

  const columns: DrCodeTableColumn<ProjectTableData>[] = [
    { displayName: "Project Name", accessor: "project_name" },
    {
      displayName: "Actions",
      accessor: "actions",
      dataRender: (row: ProjectTableData) => (
        <div className="flex gap-2">
          <Link
            href={`project/${row.id}`}
            className="button--outlined-primary p-2"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            View
          </Link>
          <Link
            className="button--outlined-primary p-2"
            href={`/project-settings/${row.id}`}
          >
            Edit
          </Link>
          <ButtonOutlinedPrimary
            text="Delete"
            size="small"
            onClick={(e) => {
              handleDeleteClick(e, row);
            }}
          />
        </div>
      ),
    },
  ];

  const createProject = async () => {
    const payload = {
      projectName: projectName,
      userId: userData.user_id,
    };
    setShowAddProjectPopup(false);
    try {
      //showLoader(true)
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/projects`,
        payload
      );
      if (response.data.success) {
        setProjectList((prev) => [...prev, response.data.data]);
        // toast.success("Project added");
        showToast("Project added", "success");
        return response.data.data.id;
      }
    } catch (err) {
      console.error("Error:", err);
    } finally {
      //showLoader(false)
    }
  };

  const getProjectsByUser = async () => {
    try {
      //showLoader(true)
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/projects/user/${userData.user_id}`
      );
      if (response.data.success) {
        setProjectList(response.data.data);
      }
    } catch (err) {
      console.error("Error:", err);
    } finally {
      //showLoader(false)
    }
  };

  const deleteProject = async (projectDetials: IProjectList) => {
    setShowDeletePopup(false);
    try {
      //showLoader(true)
      const response = await axios.delete(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/projects/${projectDetials.id}`
      );

      if (response.status == 204) {
        // toast.error("Project Deleted");
        showToast("Project Deleted", "error");
      }
      let filterProjectList = projectList.filter(
        (item, index) => item.id !== projectDetials.id
      );
      setProjectList(filterProjectList);
    } catch (err) {
      console.error("Error:", err);
    } finally {
      //showLoader(false)
    }
  };

  const updateProject = async (projectDetials: IProjectList) => {
    setShowAddProjectPopup(false);
    try {
      //showLoader(true)
      const payload = {
        projectName: projectName,
        userId: userData.user_id,
      };

      const response = await axios.put(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/projects/${projectDetials.id}`,
        payload
      );
      if (response.data.success) {
        setProjectList(
          produce(projectList, (draft) => {
            const index = draft.findIndex(
              (item) => item.id === projectDetials.id
            );
            if (index !== -1) {
              draft[index].project_name = projectName;
            }
          })
        );
        // toast.success("Project Updated");
        showToast("Project Updated", "success");
      }
    } catch (err) {
      console.error("Error:", err);
    } finally {
      //showLoader(false)
    }
  };

  const createEnvironmentVariables = async (data: any) => {
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables`,
        data
      );
      return response;
    } catch (error) {
      console.error("Error in creating environment variables:", error);
    }
  };

  const createGroup = async (projectId: number) => {
    const response = await createEnvironmentVariables({
      name: "Default Environment",
      env: {},
      user_id: userData.user_id,
    });
    let environmentVariable = response.data.id;

    const payload = {
      groupName: "Default Folder",
      projectId: projectId,
      description: "This is a Default Folder",
      env: environmentVariable,
      env_id: environmentVariable,
    };

    try {
      // Create new group
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/groups`,
        payload
      );
    } catch (error) {
      console.error(error);
    }
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    if (!isEdit) {
      const projectId = await createProject();
      await createGroup(projectId);
    } else {
      updateProject(projectDetials);
    }
  };

  useEffect(() => {
    getProjectsByUser();
    const queryParams = new URLSearchParams(window.location.search);
    if (queryParams.get("add") === "true") {
      setShowAddProjectPopup(true);
    }
  }, []);

  return (
    <div className="flex flex-col p-8">
      <div className="flex items-center justify-between w-full">
        <Link href={"/project"} className="flex gap-2 cursor-pointer">
          <Image
            src={"/arrow-left.png"}
            quality={100}
            alt="back"
            width={20}
            height={20}
          />
          <p>Back</p>
        </Link>
        <div className="flex justify-end  w-full">
          <button
            onClick={() => {
              setShowAddProjectPopup(true);
              setIsEdit(false);
              setProjectName("");
            }}
            className="button--outlined-primary button--small"
          >
            + Add Project
          </button>
        </div>
      </div>
      <p className="text-xl my-4">Projects</p>
      <DrCodeTable className="w-full" columns={columns} data={projectList} />
      {projectList.length <= 0 && (
        <div className="flex justify-center items-center flex-col mt-14">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="#875BF8"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            width="180px"
            height="180px"
            // style="width: 80px; height: 80px; margin-bottom: 16px;"
          >
            <rect x="3" y="3" width="18" height="14" rx="2" ry="2" />
            <line x1="3" y1="13" x2="21" y2="13" />
            <line x1="12" y1="17" x2="12" y2="21" />
            <line x1="9" y1="19" x2="15" y2="19" />
          </svg>
          <p className="title--large"> No Projects Found </p>
          <p className="mt-2">
            You haven’t added any projects yet. Start by creating a project and
            assigning it to a group.
          </p>
        </div>
      )}
      {showAddProjectPopup && (
        <Modal
          isOpen={showAddProjectPopup}
          title={
            <>
              <p className="text-background text-2xl font-inter font-semibold">
                {`${!isEdit ? "Add a" : "Update"}`} Project{" "}
              </p>
              <p className="text-[#9494A1] text-sm">
                Projects help you organize your work, enabling better planning,
                tracking, and management of tasks.{" "}
              </p>
            </>
          }
        >
          {" "}
          <p className="my-2">Project Title</p>
          <form onSubmit={handleSubmit}>
            {/* <input
              value={projectName}
              onChange={(e) => setProjectName(e.target.value)}
              className="primary-input w-full"
              required
            /> */}
            <PrimaryInput
              value={projectName}
              onChange={(e) => setProjectName(e.target.value)}
              className="w-full"
              required
            />

            <div className="flex justify-end gap-4 items-center">
              <button
                type={"submit"}
                className="button--primary mt-4 py-2 px-8 text-[14px]"
                style={{ fontSize: "14px" }}
              >
                Continue
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowAddProjectPopup(false);
                }}
                className="button--outlined-primary mt-4 py-2 px-8 text-[14px]"
                style={{ fontSize: "14px" }}
              >
                Close
              </button>
            </div>
          </form>
        </Modal>
      )}
      {showDeletePopup && (
        <div className={`${styles["pop-up"]}`}>
          <div className="p-4 relative">
            {/* <div className='absolute right-0'>
							<Image
								src="/settings.svg"
								alt="Example Image"
								width={110}
								height={110}
							/>
						</div> */}
            <p className="title--large text-center">
              Are you sure you want to delete ?{" "}
            </p>
            <div className="grid grid-cols-2 gap-4 mt-8">
              <button
                onClick={() => deleteProject(projectDetials)}
                className="button--delete button--small mt-4"
              >
                Yes
              </button>
              <button
                onClick={() => setShowDeletePopup(false)}
                className="button--outlined-primary button--small mt-4"
              >
                No
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectSetting;
