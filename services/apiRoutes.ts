export const GLOBAL_API_ROUTES = {
  GENERATE_SINGLE_TESTCASE_FROM_PROMPT:
    "/testCases/generateTestCaseFromPromptStreaming",
  ENHANCED_USER_PROMPT: "/testCases/enhanceUserPrompt",
  GET_E2E_BY_GROUP_ID: "/e2e/group",
  GET_E2E_BY_E2E_ID: "/e2e/e2e_id",
  GET_E2E_BY_PROJECT_ID: "/e2e/project",
  UPDATE_E2E_BY_E2E_STEP_ID: "/e2e/step",
  DELETE_E2E_STEP: "/e2e/step",
  GET_E2E_TEST_RESPONSE: "/run/e2e/response",
  GET_E2E_TEST_ASSERTIONS: "/run/e2e/assertions",
  DELETE_E2E_SCENARIO: "/e2e",
  TOGGLE_CONNECT_FOR_E2E: "/e2e/toggle-connect",
  DOWNLOAD_SAMPLE_CSV: "/test-data-pool/csv-template",
  INSERT_CSV_DATA: "/test-data-pool/bulk",
  READ_DATA_SOURCE: "/test-data-pool/entity",
  MARK_DATA_USED: "/test-data-pool/mark-used",
  MARK_DATA_UNUSED: "/test-data-pool/reset-usage",
  ANALYZE_CSV_DATA: "/testSuites/analyze",
  ANALYZE_CSV_DATA_E2E: "/e2e/analyze",
  ANALYZE_STATUS_CSV_DATA: "/testSuites/analyze/status",
  CONNECT_WITH_DATA_SOURCE: "/testDataInjection",
  CONNECT_WITH_DATA_SOURCE_E2E: "/testDataInjection/e2e",
  RESET_DATA_SOURCE_USAGE: "/test-data-pool/reset-all",
  RESET_ANALYZE_DATA_SOURCE_USAGE: "/testSuites/test-suites",
  DATA_SOURCE_STATUS_E2E: "/e2e/analyze/status",
  DATA_SOURCE_AVAILABILITY: "/test-data-pool/available",
  CREATE_WORKFLOW_WITH_PRD_TEXT: "/workflow/add-prd",
  CREATE_WORKFLOW_WITH_PRD_FILE: "/workflow/upload-prd",
  GET_WORKFLOW: "/workflow/get-workflow",
  GET_WORKFLOWS_IN_PROJECT: "/workflow/get-workflows-in-project",
  AUTH_JIRA: "/integrations/auth/jira",
  GET_JIRA_PROJECTS: "/integrations/jira/projects",
  SET_SELECTED_PROJECT: "/integrations/jira/project/select",
  CREATE_API_AUDIT_GROUP: "/audit/group",
  CREATE_API_AUDIT_REPORT: "/audit",
  GET_API_AUDIT_REPORT: "/audit",
  GET_ALL_FLOWS: "/e2e/flows",
  CREATE_SEQUENCE: "/regressionTest/create",
  GET_SEQUENCE_BY_ID: "/regressionTest/",
  VARIABLE_MAPPING: "/regressionTest/variableMappings",
  GET_ALL_SEQUENCES_BY_PROJECT_ID: "/regressionTest/project",
  SET_SEQUENCE_STATUS: "/regressionTest/update-status",
  RUN_ONE_SEQUENCE: "/regressionTest/run",
  RUN_ALL_SEQUENCES: "/regressionTest/runAll",
  GET_FLOWS_AND_STEPS_BY_PROJECT_ID: "/regressionTest/flowsAndSteps",
  DELETE_SEQUENCE: "/regressionTest/delete",
  TOGGLE_CONNECT_FOR_REGRESSION_TEST: "/regressionTest/toggle-connect",
  GET_SCHEDULER_TYPES: "/scheduler/types",
  CREATE_SCHEDULER: "/scheduler",
  ANALYTICS: "/regressionStats/project",
  GET_STEP_SUMMARIES: "/regressionStats/summary",
  GENERATE_CUSTOM_ASSERTIONS: "/regressionTest/assertions/create",
  DELETE_CUSTOM_ASSERTION: "/regressionTest/assertions/delete",
  UPDATE_CUSTOM_ASSERTION: "/regressionTest/assertions/update"
};
