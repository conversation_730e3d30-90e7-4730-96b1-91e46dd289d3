// import type { NextConfig } from "next";

const nextConfig = {
  env: {
    NEXT_ENV: process.env.NEXT_ENV,
    NEXT_PUBLIC_DR_CODE_AUTH_BASE_API_URL: process.env.NEXT_PUBLIC_DR_CODE_AUTH_BASE_API_URL,
    NEXT_PUBLIC_DR_CODE_BASE_API_URL: process.env.NEXT_PUBLIC_DR_CODE_BASE_API_URL,
  },
  /* config options here */
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://*************:3005/:path*',
      },
    ]
  },
  experimental: {
    missingSuspenseWithCSRBailout: false,
  },
  sassOptions: {
    silenceDeprecations: ['legacy-js-api'],
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "avatars.githubusercontent.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "cdn.drcode.ai",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "lh3.googleusercontent.com",
        port: "",
        pathname: "/**",
      },
    ],
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
};

module.exports =  nextConfig;