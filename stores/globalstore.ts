import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

interface GlobalStates {
    loader: boolean;
    user: any;
    playgroundRequestInput: PlayGroundRequestInput;
    revalidateTestsExplorer: boolean;
    environment: number;
    workflowAlignment: "TB" | "LR";
}

export interface PlayGroundRequestInput {
    url: string;
    method: string;
    body: string;
    headers: {
        headerKey: string;
        headerValue: string;
    }[];
}

interface GlobalActions {
    showLoader: (loaderState: boolean) => void;
    setUserData: (userData: any) => void;
    setPlaygroundRequestInput: (state: PlayGroundRequestInput) => void;
    triggerRevalidateTestsExplorer: () => void;
    setEnvironment: (env: number) => void;
    setWorkFlowAlignment:(alignment :"TB" | "LR") => void;
}


export const useGlobalStore = create<GlobalStates & GlobalActions>()(
    immer<GlobalStates & GlobalActions>(
        (set) => ({
            user: 0,
            loader: false,
            playgroundRequestInput: {
                body: "{}",
                headers: [],
                method: "GET",
                url: ""
            },
            revalidateTestsExplorer: false,
            environment: 0,
            workflowAlignment: "TB",
            showLoader: (loaderState: boolean) => set((state) => {
                state.loader = loaderState;
            }),
            setUserData: (userData: any) => set((state) => {
                state.user = userData;
            }),
            setPlaygroundRequestInput: (input: PlayGroundRequestInput) => set((state) => {
                state.playgroundRequestInput = input;
            }),
            triggerRevalidateTestsExplorer: () => {
                return set((state) => ({ revalidateTestsExplorer: !state.revalidateTestsExplorer }))
            },
            setEnvironment: (env: number) => set((state) => {
                state.environment = env;
            }),
            setWorkFlowAlignment: (alignment: "TB" | "LR") => set((state) => {
                state.workflowAlignment = alignment;
            })
        })
    )
);
