'server only'
import { NextResponse, NextRequest } from 'next/server'

// Limit the middleware to paths starting with `/api/`
export const config = {
    matcher: [
        '/project', '/project/:projectId*',
        '/project-settings'
    ]
}

export async function middleware(request: NextRequest) {
    let cookie = request.cookies.get('auth')
    let authenticated = true;
    if (!authenticated) {
        return NextResponse.redirect('http://localhost:3000');
    }
}