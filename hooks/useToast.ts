import { toast, ToastOptions } from "react-toastify";

const useToast = () => {
  const toastOptions: ToastOptions = {
    autoClose: 2000, // Display time set to 2 seconds
  };

  const showToast = (
    message: string,
    type: "success" | "error" | "info" | "warning" = "info"
  ) => {
    switch (type) {
      case "success":
        toast.success(message, toastOptions);
        break;
      case "error":
        toast.error(message, toastOptions);
        break;
      case "warning":
        toast.warn(message, toastOptions);
        break;
      case "info":
      default:
        toast.info(message, toastOptions);
        break;
    }
  };

  return { showToast };
};

export default useToast;
