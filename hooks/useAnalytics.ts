import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import axios from "axios";
import { useState } from "react";

interface FlakyData {
  name: string;
  flakiness: string;
  id: number;
  status: string[];
  tag: string;
  url: string;
}

interface TimeSeriesFormat {
  datasets: { data: number[]; label: string }[];
}

interface AnalyticsDataFormat {
  flaky_apis: FlakyData[];
  flaky_flows: FlakyData[];
  flow_performance_insights: FlowPerformanceInsightsData[];
  issue_categories: IssueCategoriesData[];
  performance_insights: PerformanceInsightsData[];
  time_series: TimeSeriesData;
}

interface FlowPerformanceInsightsData {
  avg_duration: number;
  cutoff_time: number;
  flow_id: string;
  name: string;
  url: string;
  percentage: number;
  total_runs: number;
  total_steps: number;
}

interface IssueCategoriesData {
  apis: {
    error_count: number;
    errorMessage: string;
    name: string;
    url: string;
  }[];
  category: string;
  count: number;
  description: string;
}

interface PerformanceInsightsData {
  name: string;
  step_id: string;
  total_runs: number;
  avg_response_time: number;
  cutoff_time: number;
  percentage: number;
}

interface TimeSeriesData {
  apis: TimeSeriesFormat;
  flows: TimeSeriesFormat;
  slowest_apis: TimeSeriesFormat;
  slowest_flows: TimeSeriesFormat;
}

interface AnalyticsData {
  "24h": AnalyticsDataFormat;
  "7d": AnalyticsDataFormat;
  "30d": AnalyticsDataFormat;
  "90d": AnalyticsDataFormat;
  "180d": AnalyticsDataFormat;
  "365d": AnalyticsDataFormat;
}

enum TimeSeriesFilter {
  "24h" = "Last 24 hours",
  "7d" = "Last 7 days",
  "30d" = "Last month",
  "90d" = "Last 3 months",
  "180d" = "Last 6 months",
  "365d" = "Last year",
}

export const lineChartColorOptions = [
  {
    borderColor: "#3B82F6", // Blue
    backgroundColor: "rgba(59, 130, 246)",
  },
  {
    borderColor: "#10B981", // Green
    backgroundColor: "rgba(16, 185, 129)",
  },
  {
    borderColor: "#EF4444", // Red
    backgroundColor: "rgba(239, 68, 68)",
  },
  {
    borderColor: "#F59E0B", // Amber
    backgroundColor: "rgba(245, 158, 11)",
  },
  {
    borderColor: "#8B5CF6", // Violet
    backgroundColor: "rgba(139, 92, 246)",
  },
  {
    borderColor: "#EC4899", // Pink
    backgroundColor: "rgba(236, 72, 153)",
  },
  {
    borderColor: "#0EA5E9", // Sky Blue
    backgroundColor: "rgba(14, 165, 233)",
  },
  {
    borderColor: "#22C55E", // Emerald
    backgroundColor: "rgba(34, 197, 94)",
  },
  {
    borderColor: "#D97706", // Orange
    backgroundColor: "rgba(217, 119, 6)",
  },
  {
    borderColor: "#6B7280", // Cool Gray
    backgroundColor: "rgba(107, 114, 128)",
  },
];

export enum TimeRange {
  "Last 24 hours" = "24h",
  "Last 7 days" = "7d",
  "Last month" = "30d",
  "Last 3 months" = "90d",
  "Last 6 months" = "180d",
  "Last year" = "365d",
}

export enum Metric {
  API_PERFORMANCE = "performance_insights",
  TOTAL_FLOW_RUN_VS_PASSED = "time_series_flows",
  TOTAL_STEP_RUN_VS_PASSED = "time_series_apis",
  SLOWEST_FLOW = "time_series_failed_flows",
  SLOWEST_API = "time_series_failed_apis",
  FLAKY_FLOW = "flaky_flows",
  ISSUES_IDENTIFIED = "issue_categories",
}

export const timeRangeOptions = Object.keys(TimeRange).map((key) => ({
  value: TimeRange[key as keyof typeof TimeRange],
  label: key,
}));

const useAnalytics = (projectId: string) => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>();
  const [totalFlowsRunVsPassedData, setTotalFlowsRunVsPassedData] =
    useState(null);
  const [totalStepsRunVsPassedData, setTotalStepsRunVsPassedData] =
    useState(null);
  const [slowestFlowsData, setSlowestFlowsData] = useState(null);
  const [slowestApisData, setSlowestApisData] = useState(null);
  const [flakyFlowsData, setFlakyFlowsData] = useState(null);
  const [issuesIdentifiedData, setIssuesIdentifiedData] = useState(null);
  const [apiPerformanceData, setApiPerformanceData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [metricLoadingObject, setMetricLoadingObject] = useState({
    [Metric.API_PERFORMANCE]: false,
    [Metric.TOTAL_FLOW_RUN_VS_PASSED]: false,
    [Metric.TOTAL_STEP_RUN_VS_PASSED]: false,
    [Metric.SLOWEST_FLOW]: false,
    [Metric.SLOWEST_API]: false,
    [Metric.FLAKY_FLOW]: false,
    [Metric.ISSUES_IDENTIFIED]: false,
  });

  const fetchAnalyticsData = async ({
    timeSeriesFilter = "24h",
  }) => {
    try {
      setLoading(true);
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.ANALYTICS}/${projectId}?timeRange=${timeSeriesFilter}`
      );
      setAnalyticsData(response.data.data);

      let totalFlowsRunVsPassed = {};
      let totalStepsRunVsPassed = {};
      let slowestFlows = {};
      let slowestApis = {};
      let flakyFlows = {};
      let issuesIdentified = {};
      let apiPerformance = {};

      Object.entries(response.data.data as AnalyticsData)?.forEach(
        ([key, value], idx) => {
          totalFlowsRunVsPassed[key] = {
            labels: value?.time_series?.flows?.labels,
            datasets: value?.time_series?.flows?.datasets?.map((val, index) => {
              return {
                ...val,
                backgroundColor:
                  val.label === "Passed Flows"
                    ? "green"
                    : val.label === "Failed Flows"
                      ? "red"
                      : val.label === "Warning Flows"
                        ? "yellow"
                        : "blue",
                borderColor:
                  val.label === "Passed Flows"
                    ? "green"
                    : val.label === "Failed Flows"
                      ? "red"
                      : val.label === "Warning Flows"
                        ? "yellow"
                        : "blue",
              };
            }),
          };
          totalStepsRunVsPassed[key] = {
            labels: value?.time_series?.apis?.labels,
            datasets: value?.time_series?.apis?.datasets?.map((val, index) => {
              return {
                ...val,
                // borderColor: lineChartColorOptions[index]?.borderColor,
                // backgroundColor: lineChartColorOptions[index]?.backgroundColor,
                backgroundColor:
                  val.label === "Passed APIs"
                    ? "green"
                    : val.label === "Failed APIs"
                      ? "red"
                      : val.label === "Warning APIs"
                        ? "yellow"
                        : "blue",
                borderColor:
                  val.label === "Passed APIs"
                    ? "green"
                    : val.label === "Failed APIs"
                      ? "red"
                      : val.label === "Warning APIs"
                        ? "yellow"
                        : "blue",
              };
            }),
          };
          slowestFlows[key] = {
            labels: value?.time_series?.slowest_flows?.labels,
            datasets: value?.time_series?.slowest_flows?.datasets
              ? value?.time_series?.slowest_flows?.datasets
                ?.map((val, index) => {
                  return {
                    ...val,
                    borderColor: lineChartColorOptions[index]?.borderColor,
                    backgroundColor:
                      lineChartColorOptions[index]?.backgroundColor,
                  };
                })
                ?.slice(0, 10)
              : [],
          };
          slowestApis[key] = {
            labels: value?.time_series?.failed_apis?.labels,
            datasets: value?.time_series?.failed_apis?.datasets
              ? value?.time_series?.failed_apis?.datasets
                ?.map((val, index) => {
                  return {
                    ...val,
                    borderColor: lineChartColorOptions[index]?.borderColor,
                    backgroundColor:
                      lineChartColorOptions[index]?.backgroundColor,
                  };
                })
                ?.slice(0, 10)
              : [],
          };
          flakyFlows[key] = value?.flaky_flows;
          issuesIdentified[key] =
            value?.issue_categories;
          apiPerformance[key] =
            value?.performance_insights;
        }
      );

      setTotalFlowsRunVsPassedData(totalFlowsRunVsPassed);
      setTotalStepsRunVsPassedData(totalStepsRunVsPassed);
      setSlowestFlowsData(slowestFlows);
      setSlowestApisData(slowestApis);
      setFlakyFlowsData(flakyFlows);
      setIssuesIdentifiedData(issuesIdentified);
      setApiPerformanceData(apiPerformance);

      setLoading(false);
    } catch (error) {
      console.error("Error in fetching analytics data:", error);
      setLoading(false);
    }
  };


  const addColorsToData = (data: any) => {
    return data?.datasets?.map((item: any, index: number) => {
      const colorIndex = index % lineChartColorOptions.length;
      return {
        ...item,
        backgroundColor: lineChartColorOptions[colorIndex]?.backgroundColor,
        borderColor: lineChartColorOptions[colorIndex]?.borderColor
      };
    });
  };


  const fetchMetricData = async ({
    timeRange = "24h",
    metric = Metric.API_PERFORMANCE,
  }) => {

    try {
      switch (metric) {
        case Metric.API_PERFORMANCE:
          const isDataPresent = apiPerformanceData?.[timeRange];
          if (isDataPresent) {
            return;
          }
          break;
        case Metric.TOTAL_FLOW_RUN_VS_PASSED:
          const isTotalFlowsRunVsPassedDataPresent = totalFlowsRunVsPassedData?.[timeRange];
          // console.log("log:: color data ", addColorsToData(isTotalFlowsRunVsPassedDataPresent));
          if (isTotalFlowsRunVsPassedDataPresent) {
            return;
          }
          break;
        case Metric.ISSUES_IDENTIFIED:
          const isIssuesIdentifiedDataPresent = issuesIdentifiedData?.[timeRange];
          if (isIssuesIdentifiedDataPresent) {
            return;
          }
          break;
        case Metric.FLAKY_FLOW:
          const isFlakyFlowsDataPresent = flakyFlowsData?.[timeRange];
          if (isFlakyFlowsDataPresent) {
            return;
          }
          break;
        case Metric.SLOWEST_API:
          const isSlowestApisDataPresent = slowestApisData?.[timeRange];
          if (isSlowestApisDataPresent) {
            return;
          }
          break;
        case Metric.SLOWEST_FLOW:
          const isSlowestFlowsDataPresent = slowestFlowsData?.[timeRange];
          if (isSlowestFlowsDataPresent) {
            return;
          }
          break;
        case Metric.TOTAL_STEP_RUN_VS_PASSED:
          const isTotalStepsRunVsPassedDataPresent = totalStepsRunVsPassedData?.[timeRange];
          if (isTotalStepsRunVsPassedDataPresent) {
            return;
          }
          break;
        default:
          break;
      }

      setMetricLoadingObject((prevData) => ({ ...prevData, [metric]: true }));

      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.ANALYTICS}/${projectId}?timeRange=${timeRange}&metrics=${metric}`
      );

      const newData = response.data.data?.[timeRange];

      switch (metric) {
        case Metric.API_PERFORMANCE:
          const apiPerformanceData = { [timeRange]: newData?.[Metric.API_PERFORMANCE] };
          setApiPerformanceData((prevData) => ({ ...prevData, ...apiPerformanceData }));
          break;
        case Metric.TOTAL_FLOW_RUN_VS_PASSED:
          let totalFlowsRunVsPassedData = { [timeRange]: newData?.['time_series']?.['flows'] };
          totalFlowsRunVsPassedData[timeRange].datasets = addColorsToData(totalFlowsRunVsPassedData?.[timeRange]);
          setTotalFlowsRunVsPassedData((prevData) => ({ ...prevData, ...totalFlowsRunVsPassedData }));
          break;
        case Metric.ISSUES_IDENTIFIED:
          const issuesIdentifiedData = { [timeRange]: newData?.[Metric.ISSUES_IDENTIFIED] };
          setIssuesIdentifiedData((prevData) => ({ ...prevData, ...issuesIdentifiedData }));
          break;
        case Metric.FLAKY_FLOW:
          const flakyFlowsData = { [timeRange]: newData?.[Metric.FLAKY_FLOW] };
          setFlakyFlowsData((prevData) => ({ ...prevData, ...flakyFlowsData }));
          break;
        case Metric.SLOWEST_API:
          let slowestApisData = { [timeRange]: newData?.['time_series']?.['failed_apis'] };
          slowestApisData[timeRange].datasets = addColorsToData(slowestApisData?.[timeRange]);
          setSlowestApisData((prevData) => ({ ...prevData, ...slowestApisData }));
          break;
        case Metric.SLOWEST_FLOW:
          let slowestFlowsData = { [timeRange]: newData?.['time_series']?.['failed_flows'] };
          slowestFlowsData[timeRange].datasets = addColorsToData(slowestFlowsData?.[timeRange]);
          setSlowestFlowsData((prevData) => ({ ...prevData, ...slowestFlowsData }));
          break;
        case Metric.TOTAL_STEP_RUN_VS_PASSED:
          let totalStepsRunVsPassedData = { [timeRange]: newData?.['time_series']?.['apis'] };
          totalStepsRunVsPassedData[timeRange].datasets = addColorsToData(totalStepsRunVsPassedData?.[timeRange]);
          setTotalStepsRunVsPassedData((prevData) => ({ ...prevData, ...totalStepsRunVsPassedData }));
          break;
        default:
          break;
      }
      setMetricLoadingObject((prevData) => ({ ...prevData, [metric]: false }));
    } catch (error) {
      setMetricLoadingObject((prevData) => ({ ...prevData, [metric]: false }));
      console.error("Error in fetching metric data:", error);
    }
  };

  return {
    fetchAnalyticsData,
    analyticsData,
    totalFlowsRunVsPassedData,
    totalStepsRunVsPassedData,
    slowestFlowsData,
    slowestApisData,
    flakyFlowsData,
    issuesIdentifiedData,
    apiPerformanceData,
    loading,
    fetchMetricData,
    metricLoadingObject,
  };
};

export default useAnalytics;
