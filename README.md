# DrCode Web Application

This is the official frontend web application for **DrCode**, built using **Next.js** with TypeScript and Tailwind CSS.

---

## 📁 Folder Structure
```
- 📁 app → Next.js App Router (pages, layouts, etc.)
- 📁 components → Reusable UI components
- 📁 hooks → Custom React hooks
- 📁 public → Static assets (images, fonts, etc.)
- 📁 services →API routes
- 📁 stores → State management logic (e.g., Zustand/Redux)
- 📁 temp-utils →Utilities and helper files
```



---

## 🚀 Setup Instructions

### 1. Clone the Repository

```bash
git clone https://github.com/airia-in/drcode-frontend.git
cd drcode-frontend
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment setup
- navigate to globalVariables.ts file
- If want to connect local backend server, use the localhost URLs, else use the deployed URLs


### 4. Start the Development Server

```bash
npm run dev
```
- The app will run at http://localhost:3001

---


## 🚀 Deployment Instructions

- Always checkout feature branch from development branch
- Create a pull request from feature branch -> development branch

---