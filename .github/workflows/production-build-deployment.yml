---
name: Build and Deploy drcode-ui-production to ECR
on:
  push:
    branches: [main]
jobs:
  build-and-deploy:
    name: Build and Deploy drcode-ui-production
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          # Change to the 'docker-compose' directory where Docker Compose files are located.
          cd docker-compose/

          # Build Docker containers using 'docker-compose.yml' and 'docker-compose.prod.yml'.
          docker compose -f docker-compose.yml -f docker-compose.prod.yml build

          # Tag the 'drcode-ui' image with the ECR registry URL and 'prod' tag.
          docker tag drcode-ui:prod $ECR_REGISTRY/drcode-ui:prod

          # Push the 'drcode-ui' image with the 'prod' tag to the ECR registry.
          docker push $ECR_REGISTRY/drcode-ui:prod
