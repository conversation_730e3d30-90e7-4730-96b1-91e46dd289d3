---
name: Build and Deploy drcode-ui-development to ECR
on:
  push:
    branches: [development]
jobs:
  build-and-deploy:
    name: Build and Deploy drcode-ui-development
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          # Change to the 'docker-compose' directory where Docker Compose files are located.
          cd docker-compose/

          # Build Docker containers using 'docker-compose.yml' and 'docker-compose.dev.yml'.
          docker compose -f docker-compose.yml -f docker-compose.dev.yml build

          # Tag the 'drcode-ui' image with the ECR registry URL and 'dev' tag.
          docker tag drcode-ui:dev $ECR_REGISTRY/drcode-ui:dev

          # Push the 'drcode-ui' image with the 'dev' tag to the ECR registry.
          docker push $ECR_REGISTRY/drcode-ui:dev
