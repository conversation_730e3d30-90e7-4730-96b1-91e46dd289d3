{"name": "drcode-frontend", "version": "0.1.1", "private": true, "author": "Drcode", "scripts": {"dev": "next dev -p 3001", "build": "rm -rf .next && rm -rf node_modules/.cache && next build --no-lint", "start": "next start", "lint": "echo 'Skipping linting...'", "prod-start": "next start -p 3000", "dev-start": "next start -p 3003"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@monaco-editor/react": "^4.6.0", "@mui/material": "^7.0.2", "@nextui-org/popover": "^2.3.7", "@nextui-org/progress": "^2.2.4", "@nextui-org/react": "^2.6.8", "@nextui-org/spinner": "^2.2.5", "@nextui-org/system": "^2.4.4", "@nextui-org/theme": "^2.4.3", "@tanstack/react-table": "^8.21.3", "@types/prop-types": "^15.7.14", "@types/react-syntax-highlighter": "^15.5.13", "@xyflow/react": "^12.6.1", "axios": "^1.7.9", "chai": "^5.1.2", "chart.js": "^4.5.0", "dagre": "^0.8.5", "draft-js": "^0.11.7", "framer-motion": "^11.15.0", "immer": "^10.1.1", "install": "^0.13.0", "jose": "^5.9.6", "lottie-react": "^2.4.0", "next": "^14.2.22", "nextjs-toploader": "^3.7.15", "npm": "^10.9.2", "papaparse": "^5.5.2", "react": "^18.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.0.0", "react-dropzone-uploader": "^2.11.0", "react-icons": "^5.4.0", "react-markdown": "^10.1.0", "react-player": "^2.16.0", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.3.0", "react-select": "^5.3.0", "react-syntax-highlighter": "^15.6.1", "react-toastify": "^11.0.2", "react-tooltip": "^5.28.1", "socket.io-client": "^4.8.1", "uuid": "^11.0.5", "zustand": "^5.0.2"}, "devDependencies": {"@types/chai": "^5.0.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-select": "^4.0.18", "eslint": "^8", "eslint-config-next": "15.0.4", "postcss": "^8", "sass": "^1.82.0", "tailwindcss": "^3.4.1", "typescript": "4.9.5"}}