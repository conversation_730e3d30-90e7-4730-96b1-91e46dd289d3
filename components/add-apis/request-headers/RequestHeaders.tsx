import React, { useEffect, useState } from 'react'
import Image from 'next/image';
import styles from "./RequestHeader.module.scss"
import { useGlobalStore } from '@/stores/globalstore';

interface HeaderPair {
    headerKey: string;
    headerValue: string;
}

const RequestHeaders = () => {

    const [keyValuePairs, setKeyValuePairs] = useState<HeaderPair[]>([{ headerKey: '', headerValue: '' }]);

    const handleInputChange = (index: number, field: keyof HeaderPair, value: string) => {
        const updatedPairs = structuredClone(keyValuePairs);
        updatedPairs[index][field] = value;
        if (index === keyValuePairs.length - 1 && (updatedPairs[index].headerKey || updatedPairs[index].headerValue)) {
            updatedPairs.push({ headerKey: '', headerValue: '' });
        }
        setKeyValuePairs(updatedPairs);
        setPlaygroundRequestInput({ ...playgroundRequestInput, headers: updatedPairs });

    };

    const deleteKeyValuePair = (index: number) => {
        const filterArray = keyValuePairs.filter((value, keyValueIndex) => index != keyValueIndex);
        setKeyValuePairs(filterArray);
        setPlaygroundRequestInput({ ...playgroundRequestInput, headers: filterArray });
    }

    const handleAddRow = () => {
        setKeyValuePairs([...keyValuePairs, { headerKey: '', headerValue: '' }]);
        setPlaygroundRequestInput({ ...playgroundRequestInput, headers: [...keyValuePairs, { headerKey: '', headerValue: '' }] });
    };

    const { setPlaygroundRequestInput, playgroundRequestInput } = useGlobalStore();

    useEffect(() => {
        setKeyValuePairs(playgroundRequestInput.headers);
    }, []);


    return (
        <div className={`${styles['request-header-container']} `}>
            <div className='grid grid-cols-2 gap-1'>
                <span className='primary--border px-3 py-2 text-[14px]'>Key</span>
                <span className='primary--border px-3 py-2 text-[14px]'>Value</span>
            </div>
            {/* <div className='grid grid-cols-2'>
                <input value={key} onChange={(event) => setKey(event.target.value)} className='primary-input'></input>
                <input value={value} onChange={(event) => setValue(event.target.value)} className='primary-input'></input>
            </div> */}
            {playgroundRequestInput.headers.length > 0 ? playgroundRequestInput.headers?.map((pair, index) => (
                <div key={index} className={`${styles['key-value-container']} grid grid-cols-2 relative mb-1 gap-1 cursor-pointer`}>
                    <input
                        value={pair.headerKey}
                        onChange={(event) => handleInputChange(index, 'headerKey', event.target.value)}
                        className="primary-input !rounded-none text-white text-[13px]"
                        placeholder="Enter key"
                    />
                    <input
                        value={pair.headerValue}
                        onChange={(event) => handleInputChange(index, 'headerValue', event.target.value)}
                        className="primary-input !rounded-none  text-[13px]"
                        placeholder="Enter value"
                    />
                    <div onClick={() => deleteKeyValuePair(index)} className={`${styles['trash-bin-container']} absolute right-2 top-3`} >
                        <Image
                            src="/trashbin.svg"
                            alt="delete row"
                            width={20}
                            height={20}
                        />
                    </div>

                </div>
            )) : <div onClick={handleAddRow} className=' cursor-pointer flex justify-center gap-1 items-center mt-4'>
                <Image
                    src="/add.svg"
                    alt="add row"
                    width={20}
                    height={20}
                />
                <p className="text-[14px]">Add</p>
            </div>}
        </div>
    )
}

export default RequestHeaders