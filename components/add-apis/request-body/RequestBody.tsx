import React from 'react'
import ApiRequestResponseOutputView from '@/components/common/api-request-response-output-view/ApiRequestResponseOutputView';
import { useGlobalStore } from '@/stores/globalstore';

const RequestBody = () => {
    const { setPlaygroundRequestInput, playgroundRequestInput } = useGlobalStore();
    return (
        <div className=''>
            <ApiRequestResponseOutputView onChange={(val) => { setPlaygroundRequestInput({ ...playgroundRequestInput, body: val || "" }) }} readonly={false} responseType='json' value={playgroundRequestInput.body || ""} />
        </div>
    )
}

export default RequestBody