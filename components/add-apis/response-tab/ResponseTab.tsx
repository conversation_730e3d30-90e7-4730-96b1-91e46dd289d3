import ApiRequestResponseOutputView from '@/components/common/api-request-response-output-view/ApiRequestResponseOutputView'
import React from 'react'

export default function ResponseTab(props: { value: string, type: string }) {
    return (
        <div>
            <ApiRequestResponseOutputView onChange={()=>{}} readonly={true} value={props.value} responseType={props.type} />
        </div>
    )
}
