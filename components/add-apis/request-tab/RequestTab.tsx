import React, { useState } from 'react'
import styles from "./RequestTab.module.scss"
import RequestBody from '../request-body/RequestBody';
import RequestHeaders from '../request-headers/RequestHeaders';

// type RequestTabProps = {
 
// };

export default function RequestTab() {

    const [activeTab, setActiveTab] = useState(0);


    return (
        <div className={`${styles['request-tab-container']}`}>
            <div className={`${styles['tabs']}`}>
                <div onClick={() => setActiveTab(0)} className={`${styles['tab']} ${activeTab === 0 && styles['active-tab']}`}>Body</div>
                <div onClick={() => { setActiveTab(1) }} className={`${styles['tab']} ${activeTab === 1 && styles['active-tab']}`}>Headers</div>
                {/* <div onClick={() => { setActiveTab(2) }} className={`${styles['tab']} ${activeTab === 2 && styles['active-tab']}`}>Cookies</div> */}
            </div>
            <div className={`${styles['tabs-value']} rounded-lg  w-full  `}>
                <TabsControl activeTab={activeTab} />
            </div>
        </div>

    )
}

type TabsControlProps = {
    activeTab: number;
};
const TabsControl: React.FC<TabsControlProps> = ({ activeTab }) => {
    if (activeTab == 0) {
        return <RequestBody />
    } else if (activeTab == 1) {
        return <RequestHeaders />
    }
}