"use client";
import { useGlobalStore } from "@/stores/globalstore";
import { Input, Spinner } from "@nextui-org/react";
import React, { useEffect, useState } from "react";
import Select from "react-select";
import RequestTab from "../request-tab/RequestTab";
import ResponseTab from "../response-tab/ResponseTab";

import styles from "./AddApiScreen.module.scss";
import axios from "axios";
import { useParams } from "next/navigation";
import EnvList from "@/components/test-runner/env-list/EnvList";
import { toast } from "react-toastify";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import { useRouter } from "next/navigation";
import PrimaryInput from "@/components/common/primary-input/PrimaryInput";
import useToast from "@/hooks/useToast";
import Modal from "@/components/common/modal/Modal";

const LOCALHOST_PATTERN = /^https?:\/\/(localhost|127\.0\.0\.1|0\.0\.0\.0)(:\d+)?(\/.*)?$/;


const AddApiScreen = () => {
  const { showToast } = useToast();
  const router = useRouter();

  // const [activeTab, setActiveTab] = useState<number>(0);
  const [extensionPopup, setExtensionPopup] = useState(false);

  const [reqTabIndex, setReqTabIndex] = useState<number>(0);

  const [apiResponse, setApiResponse] = useState<string>("");

  const [apiResponseStatus, setApiResponseStatus] = useState<string>("");

  // const [postmanCollection, setPostmanCollection] = useState<any[]>([]);

  const requestNameRef = React.createRef<HTMLInputElement>();
  const [requestName, setRequestName] = useState<string>("");

  const { groupId, projectId } = useParams();

  const [loading, setLoading] = useState(false);

  const options = [
    { value: "GET", label: "GET" },
    { value: "POST", label: "POST" },
    { value: "PUT", label: "PUT" },
    { value: "DELETE", label: "DELETE" },
    { value: "PATCH", label: "PATCH" },
    { value: "OPTIONS", label: "OPTIONS" },
    { value: "HEAD", label: "HEAD" },
    { value: "CONNECT", label: "CONNECT" },
    { value: "TRACE", label: "TRACE" },
  ];

  const {
    playgroundRequestInput,
    setPlaygroundRequestInput,
    showLoader,
    triggerRevalidateTestsExplorer,
  } = useGlobalStore();

  const handleChange = (
    selectedOption: { value: string; label: string } | null
  ) => {
    setPlaygroundRequestInput({
      ...playgroundRequestInput,
      method: selectedOption?.value || "",
    });
  };

  const sendRequest = async () => {
    if (!playgroundRequestInput.url) return;
    if (playgroundRequestInput.url && LOCALHOST_PATTERN.test(playgroundRequestInput.url.trim())) {
      setExtensionPopup(true); // Show popup ONLY for localhost URLs
      return;
    }

    setLoading(true);
    const headersArray = playgroundRequestInput.headers;
    const headersObj: { [key: string]: string } = {};
    for (let i = 0; i < headersArray.length; i++) {
      if (
        headersArray[i].headerKey === "" ||
        headersArray[i].headerValue === ""
      )
        continue;
      headersObj[headersArray[i].headerKey] = headersArray[i].headerValue;
    }
    try {
      const requestData: any = {
        url: playgroundRequestInput.url,
        headers: headersObj,
        method: playgroundRequestInput.method,
        groupId: Number(groupId),
        testSuiteName: requestName || "Untitled Request",
        params: {},
      };

      // Only include `body` if the method supports it
      const methodsWithBody = ["POST", "PUT", "PATCH"];

      if (methodsWithBody.includes(playgroundRequestInput.method)) {
        try {
          requestData.body = playgroundRequestInput.body
            ? JSON.parse(playgroundRequestInput.body)
            : {};
        } catch (e) {
          console.error("Invalid JSON body", e);
        }
      }

      const apiResponse = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/playground/proxy`,
        requestData
      );

      setApiResponseStatus(apiResponse.status.toString());

      if (apiResponse.status < 200 || apiResponse.status >= 300) {
        throw new Error(
          JSON.stringify(apiResponse.data) || "Something went wrong"
        );
      }

      setApiResponse(JSON.stringify(apiResponse.data));
      setLoading(false);
      setReqTabIndex(1);
    } catch (err: any) {
      console.log(err);
      setApiResponseStatus(err.status.toString());
      setApiResponse(JSON.stringify(err.response?.data, null, 10));
      setLoading(false);
      setReqTabIndex(1);
    }
  };

  const getStatusColorClass = (status: string) => {
    if (!status) return "";
    const statusCode = parseInt(status, 10);
    if (statusCode >= 200 && statusCode < 300) return "text-green-500";
    if (statusCode >= 300 && statusCode < 400) return "text-yellow-500";
    if (statusCode >= 400 && statusCode < 600) return "text-red-500";
    return "";
  };

  const onAddApi = async () => {
    if (playgroundRequestInput.url === "") {
      alert("Please enter URL");
      return;
    }

    setApiResponse("");
    setApiResponseStatus("");

    const playgroundPayload: {
      projectId: string | string[];
      groupId: string | string[];
      method: string;
      testSuiteName: string;
      url: string;
      request: {
        headers: any;
        path_params: {};
        query_params: {};
        request_body: any;
      };
    }[] = [];

    playgroundPayload.push({
      projectId: projectId,
      groupId: groupId,
      method: playgroundRequestInput.method,
      testSuiteName: requestName || "Untitled Request",
      url: playgroundRequestInput.url,
      request: {
        headers: playgroundRequestInput.headers.reduce(
          (headers: any, header) => {
            headers[header.headerKey] = header.headerValue;
            return headers;
          },
          {}
        ),
        path_params: {},
        query_params: {},
        request_body: playgroundRequestInput.body || {},
      },
    });

    try {
      //showLoader(true);
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testSuites/bulk`,
        playgroundPayload
      );
      //showLoader(false);
      // toast.success("Added successfully");
      showToast("Added successfully", "success");
      triggerRevalidateTestsExplorer();
      // /project/9/group/5/testSuit/1393
      router.push(
        `/project/${projectId}/group/${groupId}/testSuit/${response.data.data[0].id}?isPlayground=true`
      );
    } catch (err: any) {
      //showLoader(false);
      // toast.error(err.response?.data?.message || "Something went wrong");
      showToast(err.response?.data?.message || "Something went wrong", "error");
      console.log(err);
    }
  };

  useEffect(() => {
    return () => {
      setPlaygroundRequestInput({
        method: "GET",
        url: "",
        body: "",
        headers: [],
      });
    };
  }, []);

  return (
    <div className="mt-0 w-full pr-4">
      <div className="min-h-[40vh]">
        <div className="my-3 w-[50%]">
          {/* <Input ref={requestNameRef} classNames={{ innerWrapper: 'primary-input-next', input: 'text-sm font-bold', inputWrapper: 'p-0' }} placeholder='Untitled Request' /> */}
          <PrimaryInput
            placeholder="Name this API request"
            value={requestName}
            onChange={(e) => {
              setRequestName(e.target.value);
            }}
          />
        </div>
        <div className="secondary-border mt-2"></div>
        <div className="flex gap-2 h-10 mt-3">
          <Select
            className="w-[20%]"
            options={options}
            value={{
              value: playgroundRequestInput.method,
              label: playgroundRequestInput.method,
            }}
            onChange={handleChange}
            classNamePrefix="react-select"
            instanceId="method-select"
            styles={{
              control: (base) => ({
                ...base,
                fontSize: "14px",
                fontWeight: "400",
                color: "#E2E2ED",
              }),
              singleValue: (base) => ({
                ...base,
                color: "#E2E2ED",
              }),
              placeholder: (base) => ({
                ...base,
                color: "#E2E2ED",
              }),
              menu: (base) => ({
                ...base,
                fontSize: "14px",
              }),
              option: (base, { isSelected }) => ({
                ...base,
                fontSize: "14px",
                color: isSelected ? "#FFF" : "#E2E2ED",
              }),
            }}
          />

          <div className="w-[80%]">
            <div>
              {/* <input 
                                value={playgroundRequestInput.url} 
                                onChange={(e) => { 
                                    setPlaygroundRequestInput({ 
                                        ...playgroundRequestInput, 
                                        url: e.target.value 
                                    }) 
                                }} 
                                className="primary-input w-full text-sm text-[#E2E2ED]" 
                                placeholder='Enter or paste URL' 
                            /> */}
              <PrimaryInput
                placeholder="Enter or paste URL"
                value={playgroundRequestInput.url}
                onChange={(e) => {
                  setPlaygroundRequestInput({
                    ...playgroundRequestInput,
                    url: e.target.value,
                  });
                }}
              />
            </div>
          </div>
        </div>

        <div className="mt-3">
          <div className="flex justify-between items-center">
            <div className={`${styles["tabs-container"]}`}>
              <div
                onClick={() => setReqTabIndex(0)}
                className={`${styles["tab"]} ${
                  reqTabIndex === 0 && styles["active-tab"]
                }`}
              >
                Request
              </div>
              <div
                onClick={() => {
                  setReqTabIndex(1);
                }}
                className={`${styles["tab"]} ${
                  reqTabIndex === 1 && styles["active-tab"]
                }`}
              >
                Response
              </div>
            </div>
            {apiResponseStatus && (
              <p
                className={`float-right primary-border rounded-md p-2 ${getStatusColorClass(
                  apiResponseStatus
                )}`}
              >
                {apiResponseStatus}
              </p>
            )}
          </div>
          {reqTabIndex === 0 && <RequestTab />}
          {reqTabIndex === 1 && <ResponseTab value={apiResponse} type="json" />}
        </div>
      </div>

      <div className="flex my-4 justify-end gap-2">
        <button
          type="submit"
          onClick={() => {
            sendRequest();
          }}
          className={`button--primary px-4 py-2 cursor-pointer text-[14px] font-semibold ${
            loading ? "opacity-50 cursor-not-allowed" : ""
          }`}
          disabled={loading}
        >
          <span>Send</span>
        </button>
        <button
          onClick={() => {
            onAddApi();
          }}
          className={`button--primary px-4 py-2 cursor-pointer text-[14px] font-semibold float
                    ${
                      apiResponse === "" ? "opacity-50 cursor-not-allowed" : ""
                    }`}
          disabled={apiResponse === ""}
        >
          Generate Test Cases
        </button>
      </div>

      <Modal isOpen={extensionPopup} onClose={() => setExtensionPopup(false)}>
        <div className="flex flex-col items-center justify-center p-4">
          <h2 className="text-lg font-semibold mb-4">Install Extension</h2>
          <p className="text-center mb-4">
            To use localhost URLs, please install the vscode extension from the
            link below:
          </p>
          <a
            href="https://marketplace.visualstudio.com/items?itemName=DrCodeAI.drcode-vscode-tool"
            target="_blank"
            rel="noopener noreferrer"
            className="button--primary px-4 py-2 text-sm font-semibold"
          >
            Install Extension
          </a>
        </div>
        <button
          onClick={() => setExtensionPopup(false)}
          className="button--outlined-primary py-2 px-8 text-[14px] mt-auto float-right"
        >
          Close
        </button>
      </Modal>
    </div>
  );
};

export default AddApiScreen;
