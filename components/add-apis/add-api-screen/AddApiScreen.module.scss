.add-api-container {

    .progress-container {
        width: 22px;
        border: 2px solid #875BF8;
        border-radius: 6px;
    }

    .active--tab {
        border: 2px solid #2E2E60;
        width: 22px;
        border-radius: 6px;
    }

    .post {
        background-color: rgba(244, 199, 144, 0.1);
        color: rgba(244, 199, 144, 1);
        padding: 5px 10px;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .get {
        background: rgba(11, 123, 105, 0.3);
        color: rgba(11, 123, 105, 1);
        padding: 5px 10px;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .put {
        background: rgba(244, 199, 144, 0.1);
        color: rgba(244, 199, 144, 1);
        padding: 5px 10px;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .patch {
        background: rgba(57, 38, 104, 1);
        color: rgba(175, 145, 250, 1);
        padding: 5px 10px;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .custom-checkbox {
        // appearance: none;
        width: 18px;
        height: 18px;
        border: 1px solid#565F6B;
        border-radius: 4px;
        cursor: pointer;
        position: relative;
        background: #2E2E60;
    }




}

.tabs-container {
    border-bottom: 1px solid #2E2E60;
    display: flex;

    .tab {
        padding: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        font-size: 13px;
        color: #E2E2ED;
    }

    .active-tab {
        padding: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        border-bottom: 2px solid #875BF8;
        font-size: 13px;
        color: #E2E2ED;
    }
}


.thin-view {

    /* Thin scrollbar for WebKit browsers (Chrome, Edge, Safari) */
    // &::-webkit-scrollbar {
    //     height: 3px;
    //     /* Adjust height for horizontal scrollbar */
    // }

    // &::-webkit-scrollbar-track {
    //     background: #080814;
    //     /* Background color of the scrollbar track */
    // }

    // &::-webkit-scrollbar-thumb {
    //     background: #888;
    //     /* Color of the scrollbar thumb */
    //     border-radius: 10px;
    //     /* Rounded scrollbar thumb for better aesthetics */
    // }

    // &::-webkit-scrollbar-thumb:hover {
    //     background: #555;
    //     /* Darker color when hovered */
    // }
}