"use client";
import React, { memo, useEffect, useMemo, useState } from "react";
import { DrCodeTableProps } from "./DrCodeTableTypes";
import styles from "./DrCodeTable.module.scss";

const DrCodeTable = <T,>({
  columns,
  data,
  rowExpandedRender,
  className,
  onRowExpanded,
  activePage = 1,
  setActivePage = () => {},
  pageSize = 10,
  setPageSize = () => {},
}: DrCodeTableProps<T>) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(pageSize);
  const totalPages = Math.ceil(data.length / itemsPerPage);

  const { startIndex, paginatedData } = useMemo(() => {
    const start = (currentPage - 1) * itemsPerPage;
    const pageData = data.slice(start, start + itemsPerPage);
    return { startIndex: start, paginatedData: pageData };
  }, [data, currentPage, itemsPerPage]);

  const onRowClick = (e: any, rowIndex: number, row: any) => {
    if (!rowExpandedRender || !onRowExpanded) return;
    if (e.target.tagName === "Button") return;
    onRowExpanded(startIndex + rowIndex, row); // Adjust index for paginated data
  };

  useEffect(() => {
    console.log("current page is ", currentPage, activePage);

    setCurrentPage(activePage);
  }, [activePage]);

  if (data.length === 0) {
    return (
      <>
        <div className="bg-vscode-editor-background p-0 rounded-md  overflow-hidden">
          <div className="flex justify-center items-center h-[500px] text-white">
            No data available
          </div>
        </div>
      </>
    );
  }

  return (
    <div className="bg-vscode-editor-background p-0 rounded-md  overflow-hidden">
      <table
        className={`${styles["drcode-table"]}  max-h-[500px] overflow-auto overflow-container ${className}`}
        style={{ tableLayout: "fixed", width: "100%" }} // Creates space for scrollbar
      >
        <thead>
          <tr className={`${styles["table-head"]}`}>
            {columns?.map((col, idx) => (
              <th
                className={`${styles["table-head"]} 

                `}
                key={idx}
                // colSpan={col.colSpan || columns.length}
              >
                {col.columnHeadRender
                  ? col.columnHeadRender()
                  : col.displayName}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {paginatedData?.map((row: any, rowIndex) => (
            <React.Fragment key={row.id || rowIndex}>
              <tr
                className={`${styles["table-row"]}  ${
                  row.expanded ? styles["active"] : ""
                }`}
                onClick={(e) => onRowClick(e, rowIndex, row)}
              >
                {columns.map((col, idx) => (
                  <td className={`${styles["table-data"]} `} key={idx}>
                    {idx === 0 ? (
                      <div className=" w-[350px]">
                        {col.dataRender
                          ? col.dataRender(row, rowIndex)
                          : row[col.accessor]}
                      </div>
                    ) : idx === 4 ? (
                      <div className="relative">
                        {col.dataRender
                          ? col.dataRender(row, rowIndex)
                          : row[col.accessor]}
                      </div>
                    ) : (
                      <div className="flex items-center">
                        {col.dataRender
                          ? col.dataRender(row, rowIndex)
                          : row[col.accessor]}
                      </div>
                    )}
                  </td>
                ))}
              </tr>
              {rowExpandedRender && row.expanded && (
                <tr className={`${styles["table-row"]} ${styles["active"]}`}>
                  <td
                    className={`${styles["table-data"]} ${styles["table-data--expanded"]} border-0`}
                    colSpan={columns.length}
                  >
                    {rowExpandedRender(row, rowIndex)}
                  </td>
                </tr>
              )}
            </React.Fragment>
          ))}
        </tbody>
      </table>

      {/* Hide pagination when not enough data is thre */}

      {/* {data.length <= itemsPerPage && (
        <div className="flex justify-center items-center h-[50px] text-white">
          No pagination needed
        </div>
      )} */}
      {data.length > itemsPerPage && (
        <>
          {/* Pagination Controls */}
          <div className="flex justify-between items-center mt-2 px-2">
            {/* Items Per Page */}
            <div>
              <label htmlFor="itemsPerPage" className="mr-2 text-sm text-white">
                Show
              </label>
              <select
                id="itemsPerPage"
                value={itemsPerPage}
                onChange={(e) => {
                  setItemsPerPage(Number(e.target.value));
                  setCurrentPage(1); // Reset to first page when items per page changes
                }}
                className="bg-vscode-editor-background border border-drcodePurple/50 text-white rounded-md text-sm py-1 px-2 focus:outline-none"
              >
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
              </select>
              <span className="ml-2 text-sm text-white">entries</span>
            </div>

            {/* Showing X to Y of Z Entries */}
            <div className="text-sm text-white">
              Showing {data.length === 0 ? 0 : startIndex + 1} to{" "}
              {Math.min(startIndex + itemsPerPage, data.length)} of{" "}
              {data.length} entries
            </div>

            {/* Navigation Buttons */}
            <div className="flex space-x-2">
              <button
                onClick={() => {
                  setCurrentPage((prev) => Math.max(prev - 1, 1));

                  setActivePage((prev) => Math.max(prev - 1, 1)); // Adjust index for paginated data
                }}
                disabled={currentPage === 1}
                className={`px-3 py-1 rounded-md text-sm ${
                  currentPage === 1
                    ? "bg-gray-700 cursor-not-allowed"
                    : "bg-drcodePurple text-white hover:bg-drcodePurple/80"
                }`}
              >
                Previous
              </button>

              <button
                onClick={() => {
                  setCurrentPage((prev) => Math.min(prev + 1, totalPages));
                  setActivePage((prev) => Math.min(prev + 1, totalPages)); // Adjust index for paginated data
                }}
                disabled={currentPage === totalPages}
                className={`px-3 py-1 rounded-md text-sm ${
                  currentPage === totalPages
                    ? "bg-gray-700 cursor-not-allowed"
                    : "bg-drcodePurple text-white hover:bg-drcodePurple/80"
                }`}
              >
                Next
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default memo(DrCodeTable);
