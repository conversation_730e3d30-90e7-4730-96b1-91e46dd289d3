$secondary-color: #1B1B41;

.drcode-table {
    // width: 100%;
    border-collapse: separate;
    border-radius: 12px;
    border-spacing: 0;

    tbody .table-row {
        font-size: 14px;
        background-color: #0D0D22;
        border-left: #1B1B41 solid 1px;
        border-right: #1B1B41 solid 1px;

        &:hover,
        &.active {
            background-color: #1B1B41;
            cursor: pointer;
        }
    }

    .table-data {
        padding: 1rem;
        border-top: 1px solid #1C1C1F;
        width: 40%;
        font-size: 12px;

        &--expanded {
            border: none;
        }
    }

    .table-head {
        padding: 0.25rem;
        text-align: left;
        font-weight: normal;
        font-size: 14px;
        color: #9999A8;
        border-left: #080814 solid 1px;
        border-right: #080814 solid 1px;
    }

    tr:first-child td:first-child {
        border-top-left-radius: 12px;
    }

    tr:first-child td:last-child {
        border-top-right-radius: 12px;
    }

    tr:last-child td:first-child {
        border-bottom-left-radius: 12px;
    }

    tr:last-child td:last-child {
        border-bottom-right-radius: 12px;
    }

    tbody tr:last-child td {
        border-bottom: 1px solid #1B1B41;
    }

    tbody tr:first-child td {
        border-top: 1px solid #1B1B41;
    }
}