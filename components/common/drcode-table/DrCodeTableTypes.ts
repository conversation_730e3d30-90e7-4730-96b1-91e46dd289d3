
export interface DrCodeTableProps<T> {
    columns: DrCodeTableColumn<T>[],
    data: T[],
    rowExpandedRender?: (row: T, rowIndex: number) => React.ReactNode;
    className?: string;
    onRowExpanded?: (idx: number,row?:any) => void;
    activePage?: number;
    setActivePage?: any;
    pageSize?: number;
    setPageSize?: any;
}

export interface DrCodeTableColumn<T> {
    displayName: string,
    accessor: string,
    dataRender?: (row: T, rowIndex: number) => React.ReactNode,
    columnHeadRender?: () => React.ReactNode,
    sortFn?: (a: T, b: T) => number,
    sortable?: boolean,
    colSpan?: number
}
