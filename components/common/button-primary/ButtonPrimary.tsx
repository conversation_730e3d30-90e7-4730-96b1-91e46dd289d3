import React from 'react'
interface ButtonPrimaryProps extends React.ButtonHTMLAttributes<HTMLButtonElement>  {
  text: string;
  onClick: (...args: any[]) => any;
  size: 'small' | 'medium' | 'large';
  className?: string

}
export const ButtonPrimary = ({ text, onClick, size, className,...props }: ButtonPrimaryProps) => {
  const sizeClass = {
    small: 'button--small',
    medium: 'button--medium',
    large: 'button--large',
  }[size];
  return (
    <button onClick={onClick} className={`button--primary ${sizeClass} ${className} `} {...props}>{text}</button>
  )
}
