"use client";
import React, { useEffect } from "react";
import styles from "./SideBar.module.scss";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import { removeDataFromLocalStorage } from "@/temp-utils/globalUtilities";
import { LOCAL_STORAGE_DATA_KEYS } from "@/temp-utils/Constants/localStorageDataModels";

const SideBar = () => {
  const pathname = usePathname();

  const excludedPaths = [
    "/",
    "/login",
    "/pricing",
    "/contact-us",
    "/terms-and-conditions",
    "/privacy-policy",
    "/project",
    "/prd-to-workflow",
    "/api-audit",
    "/api-audit-report",
    "/integrations",
    "/variable-mapping-rules",
  ];

  const shouldRenderGlobalComponents =
    !excludedPaths.includes(pathname) &&
    !pathname.includes("/project/") &&
    !pathname.includes("/api-audit-report/") &&
    !pathname.includes("/integrations/");

  const router = useRouter();

  const handleLogoutClick = () => {
    removeDataFromLocalStorage(LOCAL_STORAGE_DATA_KEYS.ACCESS_TOKEN);
    removeDataFromLocalStorage(LOCAL_STORAGE_DATA_KEYS.SESSION_ID);
    removeDataFromLocalStorage(LOCAL_STORAGE_DATA_KEYS.USER_DATA);
    removeDataFromLocalStorage(LOCAL_STORAGE_DATA_KEYS.SELECTED_ORG);
    router.push("/");
  };

  return (
    <>
      {shouldRenderGlobalComponents && (
        <div className={`${styles["side-bar"]}`}>
          <div className="">
            <Image
              className="cursor-pointer"
              onClick={() => {
                if (pathname.includes("/project")) {
                  //   router.push("/project");
                } else {
                  router.push("/");
                }
              }}
              src={`/logo.png`}
              alt="dr-code-logo"
              width={30}
              height={30}
            />
            <div
              className={`py-2 px-1 cursor-pointer rounded-lg mt-5 ${
                pathname.includes("/project/") ? "bg-[#24243D]" : ""
              }`}
            >
              <Image
                src={
                  pathname.includes("/project")
                    ? "/Home-Active.svg"
                    : `/Home.svg`
                }
                alt="dr-code-logo"
                width={30}
                height={30}
                className=""
                onClick={() => {
                  router.push("/project");
                }}
              />
            </div>
          </div>
          <div className="flex flex-col gap-4 cursor-pointer">
            {/* <Image
              src={`/SettingsIcon.svg`}
              alt="dr-code-logo"
              className={`${
                styles["icon-container"]
              } py-2 px-1 rounded-lg mt-5 ${
                pathname.includes("/project-settings") ? "bg-[#24243D]" : ""
              }`}
              width={35}
              height={30}
              onClick={() => {
                router.push("/project-settings");
              }}
            /> */}
            <div className={`${styles["icon-container"]} ml-[0.2rem]`}>
              <Image
                src={`/Logout.svg`}
                alt="dr-code-logo"
                width={30}
                height={30}
                onClick={handleLogoutClick}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SideBar;
