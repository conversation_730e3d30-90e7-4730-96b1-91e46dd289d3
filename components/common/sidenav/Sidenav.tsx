"use client";
import Link from "next/link";
import { CiFolderOn, CiHome } from "react-icons/ci";
import { IoGitCompare } from "react-icons/io5";
import { Ri<PERSON>low<PERSON>hart } from "react-icons/ri";
import { useParams, usePathname } from "next/navigation";
import Image from "next/image";
import { useState } from "react";
import { BsChevronLeft, BsChevronRight } from "react-icons/bs";
import LogoutButton from "@/app/project/logout-button/LogoutButton";

const navItems = [
  { label: "Home", Icon: CiHome, link: "/", imageIcon: "/Home.svg" },
  {
    label: "Regression test",
    Icon: IoGitCompare,
    link: "/sequences",
    imageIcon: "/regression-icon.svg",
  },
  {
    label: "End to end flow",
    Icon: RiFlowChart,
    link: "/flows",
    imageIcon: "/e2e-icon.svg",
  },
  {
    label: "API audit",
    Icon: CiFolderOn,
    link: "/api-audit",
    imageIcon: "/Scanner.svg",
  },
  {
    label: "UI testing",
    Icon: CiFolderOn,
    link: "/ui-testing",
    imageIcon: "/TestTube.svg",
  },
];

const Sidenav = () => {
  const { projectId } = useParams();
  const pathname = usePathname();

  const expandLink = `/project/${projectId}`;

  const [isCollapsed, setIsCollapsed] = useState(
    pathname === expandLink ? false : true
  );

  return (
    <div
      className={`h-screen bg-[#0D0D22] text-white flex flex-col transition-all duration-300 ease-in-out ${isCollapsed ? "w-[70px]" : "w-[220px]"
        } relative`}
    >
      {/* Collapse Button */}
      <button
        onClick={() => setIsCollapsed(!isCollapsed)}
        className={`absolute top-4 right-[-10px] z-10 bg-[#1B1B41] p-1 rounded-lg hover:bg-[#2A2A50] transition`}
      >
        {isCollapsed ? (
          <BsChevronRight size={16} className="justify-center"/>
        ) : (
          <BsChevronLeft size={16} className="justify-center"/>
        )}
      </button>

      {/* Logo */}
      <div
        className={`flex items-center mt-4 px-4 ${isCollapsed ? "justify-center" : "space-x-2"
          }`}
      >
        <Image src="/logo.png" alt="Logo" width={30} height={30} />
        {!isCollapsed && (
          <h1 className="text-lg font-semibold tracking-wide">DrCode</h1>
        )}
      </div>

      {/* Navigation */}
      <nav className="mt-8 space-y-2 px-2">
        {navItems.map(({ label, Icon, link, imageIcon }, index) => (
          <Link
            href={`/project/${projectId}${link}`}
            key={label}
            className={`flex items-center text-sm px-2 py-2 rounded-md text-gray-300 hover:bg-[#1A1A40] transition ${isCollapsed ? "justify-center" : "gap-3"
              } ${pathname.includes(link) && link !== '/' ? "bg-[#1A1A40]" : ""} ${index === 0 && pathname === `/project/${projectId}` ? "bg-[#1A1A40]" : ""}`}
          >
            <Image src={imageIcon} alt={label} width={20} height={20} />
            {!isCollapsed && <span>{label}</span>}
          </Link>
        ))}
      </nav>

      <div className={`mt-auto flex items-center justify-center mb-6 py-2 rounded-md text-gray-300 hover:bg-[#1A1A40] transition`}>
        <LogoutButton />
      </div>
    </div>
  );
};

export default Sidenav;
