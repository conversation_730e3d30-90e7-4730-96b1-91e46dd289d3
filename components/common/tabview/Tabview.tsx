"use client";

import { useState } from "react";

type Tab = {
  label: string;
  content: React.ReactNode;
  headerClassName?: string;
  contentClassName?: string;
};

type TabViewProps = {
  tabs: Tab[];
  selectedTab?: number;
};

const TabView: React.FC<TabViewProps> = ({ tabs, selectedTab = 0 }) => {
  const [activeTab, setActiveTab] = useState(selectedTab);

  return (
    <div className="w-full   border-b border-gray-700">
      {/* Tab Headers */}
      <div className="flex space-x-8 px-4">
        {tabs.map((tab, index) => (
          <button
            key={index}
            className={`relative py-3 px-3 text-sm font-medium transition-all
              ${
                activeTab === index
                  ? "text-white"
                  : "text-gray-400 hover:text-gray-300"
              } ${tab.headerClassName}`}
            onClick={() => setActiveTab(index)}
          >
            {tab.label}
            {/* Active Tab Indicator */}
            <span
              className={`absolute left-0 bottom-0 h-0.5 bg-[#875bf8] transition-all
                ${activeTab === index ? "w-full" : "w-0"}`}
            />
          </button>
        ))}
      </div>

      {/* Full-width Underline */}
      <div className="h-[1px] bg-gray-700 w-full" />

      {/* Tab Content */}
      <div className="p-4">{tabs[activeTab].content}</div>
    </div>
  );
};

export default TabView;
