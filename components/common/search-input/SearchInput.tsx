"use client";
import Image from "next/image";
import React from "react";
import styles from "./SearchInput.module.scss";
const SearchInput = (props: {
  value: string;
  onChangeHandler: (e: any) => any;
  placeholder?: string;
}) => {
  return (
    <div className={`search-input-container ${styles["search"]}`}>
      {/* Search Icon */}
      <Image
        src="/search-icon.png" // Replace with your actual image path
        alt="Search Icon"
        width={20}
        height={20}
        className="search-icon"
      />
      {/* Input Field */}
      <input
        value={props.value}
        onChange={(e) => {
          props.onChangeHandler(e);
        }}
        type="text"
        className="primary-input primary-search-input  rounded-lg"
        placeholder={
          props.placeholder ? props.placeholder : "Search Test Cases"
        }
        name=""
        id=""
      />
    </div>
  );
};

export default SearchInput;
