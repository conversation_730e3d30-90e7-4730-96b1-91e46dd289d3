import Image from "next/image";
import React from "react";

interface ModalProps {
  isOpen: boolean;
  onClose?: () => void;
  title?: React.ReactNode;
  children: React.ReactNode;
  footer?: React.ReactNode;
  className?: string;
  size?: "small" | "medium" | "large";
  showFooterButtons?: boolean;
  showBackButton?: boolean;
  onSave?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  onCancel?: () => void;
  onBack?: () => void;
  saveLabel?: string;
  cancelLabel?: string;
  backLabel?: string;
  isLoading?: boolean;
  isDisabled?: boolean;
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title = null,
  children,
  footer,
  className,
  size = "medium",
  showFooterButtons = false,
  showBackButton = false,
  onSave,
  onCancel,
  onBack,
  saveLabel = "Save",
  cancelLabel = "Cancel",
  backLabel = "Back",
  isLoading = false,
  isDisabled = false,
}) => {
  if (!isOpen) return null;

  // Define modal width based on size prop
  const sizeClasses = {
    small: "max-w-[400px]",
    medium: "max-w-[600px]",
    large: "max-w-[800px]",
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-drcodeBlue/20 bg-opacity-60 backdrop-blur-lg z-50">
      <div
        className={`bg-drcodeBlue text-white p-4 rounded-lg shadow-sm shadow-drcodePurple w-full ${sizeClasses[size]} ${className}`}
      >
        {/* Header */}
        <div className="relative flex justify-between items-center">
          {title && <h2 className="text-lg font-semibold">{title}</h2>}
          <Image
            src="/settings.svg"
            alt="Example Image"
            width={110}
            height={110}
            className="absolute top-[-10px] right-0 z-0 opacity-40"
          />
        </div>

        {/* Body */}
        <div className="relative mt-2 max-h-[350px] overflow-auto z-10">
          {children}
        </div>

        {/* Footer (Optional) */}
        {(footer || showFooterButtons || showBackButton) && (
          <div className="relative z-10 mt-4 flex justify-between items-center">
            {/* Left Footer Button */}
            {showBackButton && (
              <button
                onClick={onBack}
                className="button--outlined-primary py-2 px-8 text-[14px]"
                style={{
                  fontSize: "14px",
                }}
              >
                {backLabel}
              </button>
            )}
            {/* Right Footer Buttons */}
            <div className="flex gap-2 ml-auto">
              {showFooterButtons && (
                <>
                  <button
                    onClick={onCancel}
                    className="button--outlined-primary py-2 px-8 text-[14px]"
                    style={{
                      fontSize: "14px",
                    }}
                  >
                    {cancelLabel}
                  </button>
                  <button
                    onClick={onSave}
                    className="button--primary py-2 px-8 text-[14px]"
                    style={{
                      fontSize: "14px",
                    }}
                    disabled={isLoading || isDisabled}
                  >
                    {saveLabel}
                  </button>
                </>
              )}
            </div>
            {/* Custom Footer (if provided) */}
            {footer && <div className="ml-4">{footer}</div>}
          </div>
        )}
      </div>
    </div>
  );
};

export default Modal;
