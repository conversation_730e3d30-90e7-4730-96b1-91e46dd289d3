import React, { useRef, useState } from "react";
import { FiUpload } from "react-icons/fi";
import <PERSON> from "papaparse";
import { FaRegFileAlt } from "react-icons/fa";

interface FileUploaderProps {
  handleData: (data: any) => void;
  label?: string;
  acceptedFileTypes?: string[];
}

const FileUploader = ({
  handleData,
  label = "Select a CSV file to upload",
  acceptedFileTypes = ["text/csv"],
}: FileUploaderProps) => {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [fileData, setFileData] = useState<any>(null);
  const [fileName, setFileName] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  const handleClick = () => {
    inputRef.current?.click();
  };

  const handleFile = (file: File) => {
    console.log("log:: file is ", file);
    setFileName(file.name);
    //   parse using papaparse and then send it to handleData only if file is csv
    if (file.type !== "text/csv") {
      setFileData(file.name);
      handleData(file);
      return;
    }
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        console.log("log:: results are ",results.data)
        handleData(results.data);
        setFileData(results.data);
      },
      error: (error) => {
        console.error("Error parsing CSV:", error);
        setFileData(null);
      },
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      handleFile(file);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFile(file);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  return (
    <div
      className={`w-full mx-auto p-6 border-2 border-dashed rounded-lg cursor-pointer transition-all ${
        isDragging
          ? "border-blue-400 bg-blue-950/20"
          : "border-blue-900 bg-[#151437]"
      }`}
      onClick={handleClick}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
    >
      <div className="flex flex-col items-center justify-center space-y-2">
        {fileData ? (
          <>
            <FaRegFileAlt size={30} color="#D1D1E3" />

            <span className="text-[#B2B2C1] text-[14px] font-bold">
              {fileName}
            </span>
            <button
              className="text-[#9F7CF9] font-normal text-[13px]"
              onClick={() => {
                setFileData(null);
                handleData(null);
                setFileName("");
              }}
            >
              Remove
            </button>
          </>
        ) : (
          <>
            <FiUpload size={30} color="#D1D1E3" />
            <div className="text-[#9F7CF9] font-normal text-[13px]">
              {label}
            </div>
            <div className="text-[11px] text-[#9494A1]">
              or drag and drop it here
            </div>
            <input
              ref={inputRef}
              type="file"
              accept={acceptedFileTypes.join(",")}
              className="hidden"
              onChange={handleFileChange}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default FileUploader;
