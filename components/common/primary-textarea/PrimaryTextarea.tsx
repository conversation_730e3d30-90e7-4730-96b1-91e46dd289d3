import React, { forwardRef } from "react";

interface PrimaryTextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  border?: "primary--border" | "secondary--border" | "";
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  disabled?: boolean;
  className?: string;
  readOnly?: boolean;
}

const PrimaryTextarea = forwardRef<HTMLTextAreaElement, PrimaryTextareaProps>(
  (
    {
      border = "primary--border",
      placeholder = "Enter text...",
      value,
      onChange,
      disabled = false,
      readOnly = false,
      className = "",
      ...props
    },
    ref
  ) => {
    return (
      <textarea
        ref={ref}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        disabled={disabled}
        readOnly={readOnly}
        className={`primary-input w-full px-4 py-2 rounded-md outline-none transition-all text-white text-sm resize-none
        ${
          border === "primary--border" && !readOnly
            ? "border border-[#11112C] focus:border-drcodePurple"
            : ""
        }
        ${
          border === "secondary--border" && !readOnly
            ? "border border-gray-400 focus:border-gray-600"
            : ""
        }
        ${
          disabled ? "bg-gray-200 cursor-not-allowed" : "bg-white text-gray-900"
        }
        ${className}
      `}
        {...props}
      />
    );
  }
);

export default PrimaryTextarea;
