.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    width: 100%;
  }
  
  .animation {
    width: 13vw;
    height: 18vh;
  }
  
  .text-container {
    position: relative;
    height: 1.5rem;
    width: 100%;
    text-align: center;
    margin-top: 1rem;
    overflow: hidden;
  }
  
  .step-text {
    font-size: 16px;
    font-weight: 600;
    color: #fafafc;
    opacity: 0;
    animation: fadeIn 1s ease-in-out forwards;
  }
  
  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  