import { useEffect, useState } from "react";
import <PERSON><PERSON> from "lottie-react";
// import redirectAnimation from "/redirectLoadingAnimation.json";
import "./TestCaseLoader.scss"; // Import SCSS styles
import Loader from "../loader/Loader";
import LoaderGif from "../loader-gif/LoaderGif";
import QueueLoader from "./queueLoading.json";

const STEPS = [
  { id: 1, title: "Analyzing your API request…" },
  { id: 2, title: "Generating test cases…" },
  { id: 3, title: "Validating across scenarios…" },
  { id: 4, title: "Finalizing detailed test cases…" },
];

interface TestCaseLoaderProps {
  isQueue?: boolean;
  steps?: { id: number; title: string }[];
}

const TestCaseLoader = ({
  isQueue = false,
  steps = STEPS,
}: TestCaseLoaderProps) => {
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStep((prevStep) =>
        prevStep === STEPS.length - 1 ? 0 : prevStep + 1
      );
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="loading-container w-full">
      {isQueue ? (
        <Lottie className="h-[250px]" animationData={QueueLoader} loop={true} />
      ) : (
        <LoaderGif />
      )}

      <div className={`text-container ${isQueue ? "bottom-[75px]" : ""}`}>
        <div key={currentStep} className="step-text animate-slide">
          {isQueue
            ? "Test case generation is in Queue"
            : steps[currentStep]?.title}{" "}
          {}
        </div>
      </div>
    </div>
  );
};

export default TestCaseLoader;
