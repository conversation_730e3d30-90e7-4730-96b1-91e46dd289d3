import React from 'react'
interface ButtonSoftPrimaryProps {
    text: string;
    onClick: () => void;
    size: 'small' | 'medium' | 'large';

}
export const ButtonSoftPrimary = ({ text, onClick, size }: ButtonSoftPrimaryProps) => {
    const sizeClass = {
        small: 'button--small',
        medium: 'button--medium',
        large: 'button--large',
    }[size];
    return (
        <button onClick={onClick} className={`button--soft-primary ${sizeClass}`}>{text}</button>
    )
}
