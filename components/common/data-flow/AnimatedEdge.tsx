import React from "react";
import { BaseEdge, getSmoothStepPath, type EdgeProps } from "@xyflow/react";

export function AnimatedSVGEdge({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
}: EdgeProps) {
  const [edgePath] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  return (
    <>
      <BaseEdge id={id} path={edgePath} />
      {/* <circle r="10" fill="#ff0073">
        <animateMotion dur="2s" repeatCount="indefinite" path={edgePath} />
      </circle> */}
      {/* <path d="M0,-5 L10,0 L0,5 Z" fill="#ff0073">
        <animateMotion dur="2s" repeatCount="indefinite" path={edgePath} />
      </path> */}
      <circle r="6" fill="#ff0073">
        <animateMotion dur="2s" repeatCount="indefinite" path={edgePath} />
        <animate
          attributeName="r"
          values="4;6;4"
          dur="1s"
          repeatCount="indefinite"
        />
      </circle>
    </>
  );
}
