import React from "react";
import { Handle, Position } from "@xyflow/react";
import { LuBadgeInfo } from "react-icons/lu";
import { useGlobalStore } from "@/stores/globalstore";
import { Tooltip } from "react-tooltip";

const drCodePurple = "#7C3AED"; // Tailwind's purple-600

const CustomNode = ({
  data,
}: {
  data: { label: string; description: string };
}) => {
  const { workflowAlignment } = useGlobalStore();

  return (
    <>
      {/* Tooltip element with a fixed ID */}
      <Tooltip
        id="custom-node-tooltip"
        place="top"
        style={{
          backgroundColor: drCodePurple,
          fontSize: "26px", // or "18px", "1.125rem", etc.
        }}
      />

      <div className="bg-[#1E293B] text-white p-2 py-4 rounded-lg shadow-md border border-gray-600 relative max-w-[300px] w-[300px] text-center z-10 overflow-visible">
        <Handle
          type="target"
          position={workflowAlignment === "TB" ? Position.Top : Position.Left}
          className="w-3 h-3 bg-blue-500"
        />

        <div className="flex items-center justify-center gap-3 relative">
          <h4 className="font-semibold text-lg">{data.label}</h4>

          {/* Attach tooltip via data-tooltip-id */}
          <LuBadgeInfo
            size={20}
            data-tooltip-id="custom-node-tooltip"
            data-tooltip-content={data.description}
            className="cursor-pointer text-base text-blue-400"
          />
        </div>

        <Handle
          type="source"
          position={
            workflowAlignment === "TB" ? Position.Bottom : Position.Right
          }
          className="w-3 h-3 bg-blue-500"
        />
      </div>
    </>
  );
};

export default CustomNode;
