import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import axios from "axios";
import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";
import React, { useEffect } from "react";
import { RxCross2 } from "react-icons/rx";
import Select from "react-select";
import SearchInput from "../search-input/SearchInput";

const WorkflowExplorer = ({ handleCollapse = () => {} }) => {
  const { projectId, groupId, testSuitId } = useParams();
  const [search, setSearch] = React.useState("");
  const [workflows, setWorkflows] = React.useState([]);

  const onInputChange = (e) => {
    setSearch(e.target.value);
  };

  const fetchWorkProjectWorkFlows = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GET_WORKFLOWS_IN_PROJECT}/${projectId}`
      );
      console.log("response is ", response.data);

      const workflowData = response.data.map((workflow) => ({
        label: workflow.workflow_name,
        value: workflow.id,
      }));
      setWorkflows(workflowData);
    } catch (e) {}
  };

  useEffect(() => {
    if (!projectId) return;
    fetchWorkProjectWorkFlows();
  }, [projectId]);

  return (
    <div className={`overflow-y-auto h-[100vh] bg-[#0D0D22]`}>
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-[#0D0D22] border-drcodeBlue">
        <div className="flex items-center px-3 py-2">
          <Link
            href={`/project/${projectId}`}
            className="flex items-center gap-2 cursor-pointer max-w-[fit-content]"
          >
            <Image
              src={"/arrow-left.png"}
              quality={100}
              alt="back"
              width={20}
              height={20}
            />
            <p className="text-[14px] font-normal text-[#B6B6BF]">
              Back to Dashboard
            </p>
          </Link>
          <button className="ml-auto" onClick={handleCollapse}>
            <RxCross2 size={20} />
          </button>
        </div>
        {/* 
        <div className="px-4">
          <Select
            className="my-4"
            onChange={() => {}}
            options={workflows}
            value={""}
            placeholder="Default Folder"
            classNamePrefix="react-select"
            instanceId="method-select"
            styles={{
              control: (provided) => ({
                ...provided,
                fontSize: "14px",
                color: "#777781",
              }),
              singleValue: (provided) => ({
                ...provided,
                fontSize: "14px",
                color: "#777781",
              }),
              option: (provided, state) => ({
                ...provided,
                fontSize: "14px",
                color: state.isSelected ? "white" : "#777781",
                backgroundColor: state.isSelected ? "#777781" : "white",
              }),
              placeholder: (provided) => ({
                ...provided,
                fontSize: "14px",
                color: "#777781",
              }),
            }}
          />
        </div> */}

        <div className="min-w-[90%]">
          <SearchInput value={search} onChangeHandler={onInputChange} placeholder="Search Workflows"  />
        </div>
      </div>
      <div className="px-4 mt-2 flex flex-col divide-y divide-[#3A3A4A]">
        {workflows
          .filter((workflow) => {
            if (search === "") return true;
            return workflow.label.toLowerCase().includes(search.toLowerCase());
          })
          .map((workflow) => (
            <Link
              href={`/project/${projectId}/prd-to-workflow/${workflow.value}`}
              key={workflow.value}
              className="text-[#B6B6BF] py-2 text-[14px] font-normal"
            >
              {workflow.label}
            </Link>
          ))}
      </div>
    </div>
  );
};

export default WorkflowExplorer;
