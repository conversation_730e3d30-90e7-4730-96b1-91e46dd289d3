"use client";
import React, { useState } from "react";
import {
  ReactFlow,
  ReactFlowProvider,
  NodeTypes,
  addEdge,
  Connection,
  Edge,
  Background,
  Controls,
  useNodesState,
  useEdgesState,
} from "@xyflow/react";
import "@xyflow/react/dist/style.css";
import { JourneyData } from "@/app/project/[projectId]/prd-to-workflow/types";
import { convertJourneyToReactFlow } from "./utils";
import CustomNode from "./CustomNode";
import { AnimatedSVGEdge } from "./AnimatedEdge";
import PrimaryButton from "../buttons/PrimaryButton";
import { useGlobalStore } from "@/stores/globalstore";

const nodeTypes: NodeTypes = {
  custom: CustomNode,
};

const edgeTypes = {
  animatedSvg: AnimatedSVGEdge,
};

const DataFlow = ({
  data,
  onUpdate,
}: {
  data: JourneyData;
  onUpdate: (updatedData: JourneyData) => void;
}) => {
  const { setWorkFlowAlignment } = useGlobalStore();

  const { workflowAlignment } = useGlobalStore();

  const initialLayout = convertJourneyToReactFlow(data, workflowAlignment);
  const [nodes, setNodes, onNodesChange] = useNodesState(initialLayout.nodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialLayout.edges);
  const [selected, setSelected] = useState("TB");

  React.useEffect(() => {
    const { nodes: newNodes, edges: newEdges } = convertJourneyToReactFlow(
      data,
      workflowAlignment
    );
    setNodes(newNodes);
    setEdges(newEdges);
  }, [workflowAlignment, data]);

  const handleConnect = (connection: Connection) => {
    const newEdge: Edge = {
      ...connection,
      id: `${connection.source}-${connection.target}`,
      type: "straight",
      animated: true,
    };
    setEdges((prevEdges) => [...prevEdges, newEdge]);

    // Update the data object
    const updatedData = { ...data };
    const sourceNode = updatedData.journey.find(
      (node) => node.id === connection.source
    );
    if (sourceNode && !sourceNode.targets.includes(connection.target)) {
      sourceNode.targets.push(connection.target);
    }
    onUpdate(updatedData);
  };

  const handleWorkflowAlignment = (direction: "TB" | "LR") => {
    setWorkFlowAlignment(direction);
  };

  const tabs: { label: string; value: "TB" | "LR" }[] = [
    { label: "Top to bottom", value: "TB" },
    { label: "Left to right", value: "LR" },
  ];

  const handleClick = (value: "TB" | "LR") => {
    setSelected(value);
    handleWorkflowAlignment(value);
  };

  return (
    <div
      className=""
      style={{
        width: "100vw",
        height: "100vh",
      }}
    >
      <div className="inline-flex rounded-md shadow-sm border border-gray-300 overflow-hidden">
        {tabs.map((tab) => (
          <button
            key={tab.value}
            onClick={() => handleClick(tab.value)}
            className={`px-2 py-1 text-sm font-medium focus:outline-none transition-colors ${
              selected === tab.value
                ? "bg-[#875bf8] text-white"
                : "bg-white text-gray-700 hover:bg-gray-100"
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>
      <ReactFlowProvider>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          fitView
          onConnect={handleConnect} // Handle new connections
        />
        <Background />
        <Controls />
      </ReactFlowProvider>
    </div>
  );
};

export default DataFlow;
