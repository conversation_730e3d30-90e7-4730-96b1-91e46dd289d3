// import { JourneyData } from "@/app/project/[projectId]/prd-to-workflow/types";
// import { Node, Edge } from "@xyflow/react";

// // export const convertJourneyToReactFlow = (
// //   data: JourneyData
// // ): { nodes: Node[]; edges: Edge[] } => {
// //   const nodeSpacingX = 300; // Horizontal spacing between nodes
// //   const nodeSpacingY = 200; // Vertical spacing between nodes
// //   const levelMap: Record<number, string[]> = {}; // Map to track nodes at each level
// //   const nodePositions: Record<string, { x: number; y: number }> = {};

// //   // Helper function to calculate positions
// //   const calculatePosition = (stepId: string, level: number): { x: number; y: number } => {
// //     if (!levelMap[level]) {
// //       levelMap[level] = [];
// //     }
// //     const indexAtLevel = levelMap[level].length;
// //     levelMap[level].push(stepId);

// //     // Center nodes horizontally based on their index at the level
// //     const x = indexAtLevel * nodeSpacingX - ((levelMap[level].length - 1) * nodeSpacingX) / 2;
// //     const y = level * nodeSpacingY; // Space nodes vertically
// //     nodePositions[stepId] = { x, y };
// //     return { x, y };
// //   };

// //   // Calculate positions for nodes
// //   const nodes: Node[] = data.journey.map((step) => {
// //     const sources = step.sources;
// //     let level = 0;

// //     if (sources.length > 0) {
// //       // Determine the level based on the maximum level of sources
// //       level = Math.max(...sources.map((sourceId) => nodePositions[sourceId]?.y || 0)) / nodeSpacingY + 1;
// //     }

// //     const position = calculatePosition(step.id, level);
// //     return {
// //       id: step.id,
// //       data: { label: step.label },
// //       position,
// //       type: "custom", // Assign custom node type
// //     };
// //   });

// //   // Create edges
// //   const edges: Edge[] = [];
// //   data.journey.forEach((step) => {
// //     step.targets.forEach((target) => {
// //       edges.push({
// //         id: `${step.id}-${target}`,
// //         source: step.id,
// //         target: target,
// //         type: "smoothstep",
// //         animated: true,
// //       });
// //     });
// //   });

// //   return { nodes, edges };
// // };

// export const convertJourneyToReactFlow = (
//   data: JourneyData
// ): { nodes: Node[]; edges: Edge[] } => {
//   const nodeSpacingX = 300;
//   const nodeSpacingY = 200;
//   const nodeLevels: Record<string, number> = {};
//   const levelNodes: Record<number, string[]> = {};

//   // First pass: determine levels
//   data.journey.forEach((step) => {
//     if (step.sources.length === 0) {
//       nodeLevels[step.id] = 0;
//     } else {
//       const maxSourceLevel = Math.max(
//         ...step.sources.map((src) => nodeLevels[src] ?? 0)
//       );
//       nodeLevels[step.id] = maxSourceLevel + 1;
//     }

//     const level = nodeLevels[step.id];
//     if (!levelNodes[level]) levelNodes[level] = [];
//     levelNodes[level].push(step.id);
//   });

//   // Second pass: assign positions
//   const nodes: Node[] = data.journey.map((step) => {
//     const level = nodeLevels[step.id];
//     const indexAtLevel = levelNodes[level].indexOf(step.id);
//     const totalAtLevel = levelNodes[level].length;

//     const x =
//       indexAtLevel * nodeSpacingX - ((totalAtLevel - 1) * nodeSpacingX) / 2;
//     const y = level * nodeSpacingY;

//     return {
//       id: step.id,
//       data: { label: step.label },
//       position: { x, y },
//       type: "custom",
//       dragHandle: ".drag-handle",
//     };
//   });

//   // Create edges
//   const edges: Edge[] = data.journey.flatMap((step) =>
//     step.targets.map((target) => ({
//       id: `${step.id}-${target}`,
//       source: step.id,
//       target: target,
//       type: "step",
//       animated: true,
//       interactionWidth: 175,
//     }))
//   );

//   return { nodes, edges };
// };

import dagre from "dagre";
import { JourneyData } from "@/app/project/[projectId]/prd-to-workflow/types";
import { Node, Edge } from "@xyflow/react";
import { useGlobalStore } from "@/stores/globalstore";

const nodeWidth = 300; // Width of the node with padding and text
const nodeHeight = 100; // Height with top/bottom handles + spacing

export const convertJourneyToReactFlow = (
  data: JourneyData,
  workflowAlignment = "TB"
): { nodes: Node[]; edges: Edge[] } => {
  console.log("Workflow Alignment:", workflowAlignment);

  const dagreGraph = new dagre.graphlib.Graph();
  dagreGraph.setDefaultEdgeLabel(() => ({}));

  // Set layout direction (TB = top to bottom, LR = left to right)
  dagreGraph.setGraph({ rankdir: workflowAlignment });

  // Step 1: Add nodes to the graph
  data.journey.forEach((step) => {
    dagreGraph.setNode(step.id, { width: nodeWidth, height: nodeHeight });
  });

  // Step 2: Add edges to the graph
  const edges: Edge[] = data.journey.flatMap((step) =>
    step.targets.map((target) => {
      dagreGraph.setEdge(step.id, target);
      return {
        id: `${step.id}-${target}`,
        source: step.id,
        target,
        type: "animatedSvg",
        animated: true,
        interactionWidth: 175,
      };
    })
  );

  // Step 3: Compute layout
  dagre.layout(dagreGraph);

  // Step 4: Generate nodes with computed positions
  const nodes: Node[] = data.journey.map((step) => {
    const { x, y } = dagreGraph.node(step.id);
    return {
      id: step.id,
      data: { label: step.label, description: step.description },
      position: { x, y },
      type: "custom",
    };
  });

  return { nodes, edges };
};
