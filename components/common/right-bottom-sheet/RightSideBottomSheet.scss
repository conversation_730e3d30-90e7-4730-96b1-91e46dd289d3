@import "../../../app/globals.scss";

.bottom-sheet-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
  z-index: 1000;

  &.right {
    justify-content: flex-end;
  }

  &.left {
    justify-content: flex-start;
  }
}

.bottom-sheet-overlay.open {
  opacity: 1;
  visibility: visible;
}

.bottom-sheet {
  background-color: $drcode-blue;
  width: 460px;
  height: 100vh;
  transition: transform 0.3s ease;
  padding: 16px;
}

.bottom-sheet.right {
  transform: translateX(100%);
  animation: slideOutRight 0.3s forwards;
}

.bottom-sheet.right.open {
  transform: translateX(0);
  animation: slideInRight 0.3s forwards;
}

.bottom-sheet.left {
  transform: translateX(-100%);
  animation: slideOutLeft 0.3s forwards;
}

.bottom-sheet.left.open {
  transform: translateX(0);
  animation: slideInLeft 0.3s forwards;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}
