import React, { useState } from "react";
import "./RightSideBottomSheet.scss";

interface BottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  side?: "right" | "left"; // default to "right"
  widthInPercentage?: string; // optional width prop
}

const BottomSheet: React.FC<BottomSheetProps> = ({
  isOpen,
  onClose,
  children,
  side = "right",
  widthInPercentage = "40%",
}) => {
  return (
    <div
      className={`bottom-sheet-overlay ${side} ${isOpen ? "open" : ""}`}
      onClick={onClose}
    >
      <div
        className={`bottom-sheet ${side} ${isOpen ? "open" : ""}`}
        onClick={(e) => e.stopPropagation()}
        style={{
          width: widthInPercentage,
        }}
      >
        {children}
      </div>
    </div>
  );
};

export default BottomSheet;
