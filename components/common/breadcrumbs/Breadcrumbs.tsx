'use client';

import Link from 'next/link';
import { FaChevronRight } from 'react-icons/fa';

type BreadcrumbItem = {
  label: string;
  href: string;
};

type BreadcrumbsProps = {
  items: BreadcrumbItem[];
};

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ items }) => {
  return (
    <nav aria-label="breadcrumb" className="text-sm ">
      <ol className="flex items-center space-x-2">
        {items.map((item, index) => {
          const isLast = index === items.length - 1;

          return (
            <li key={item.href} className="flex items-center space-x-2">
              {index > 0 && <FaChevronRight className="w-4 h-4 text-gray-400" />}
              {isLast ? (
                <span className="text-drcodeGrayLight font-medium capitalize">{item.label}</span>
              ) : (
                <Link href={item.href} className="text-drcodeGray hover:underline capitalize">
                  {item.label}
                </Link>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

export default Breadcrumbs;
