'use client';
import { useGlobalStore } from '@/stores/globalstore';
import React from 'react';

const Loader = () => {
    const { loader } = useGlobalStore();

    if (!loader) return null;

    return (
        <div className='fixed inset-0 flex justify-center items-center'>
            <img
                loading='lazy'
                src='https://cdn.drcode.ai/assets/loader.gif'
                alt='Loading'
                className='object-contain self-center aspect-square w-[51px]'
            />
        </div>
    );
};

export default Loader;
