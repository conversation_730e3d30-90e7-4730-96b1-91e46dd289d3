"use client";
import React from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "react-chartjs-2";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const baseOptions = {
  responsive: true,
  plugins: {
    legend: { position: "bottom" as const },
    title: {
      display: true,
      text: "", // will be overridden
    },
  },
  scales: {
    x: {
      grid: { display: true, color: "#ebe5e812" },
    },
    y: {
      grid: { display: true, color: "#ebe5e812" },
    },
  },
};

interface LineChartDataSet {
  label: string;
  data: number[];
  borderColor?: string;
  backgroundColor?: string;
}

interface LineChartData {
  labels: string[];
  datasets: LineChartDataSet[];
}

interface BarChartProps {
  title?: string;
  dataMap: LineChartData;
}

const CustomBarChart: React.FC<BarChartProps> = ({
  title = "",
  dataMap
}) => {
  const chartData = dataMap;

  const options = {
    ...baseOptions,
    plugins: {
      ...baseOptions.plugins,
      title: { ...baseOptions.plugins.title, text: title },
      legend: {
        labels: {
          maxWidth: 150,
          boxWidth: 12,
          font: {
            size: 12,
          },
        },
      },
    },
  };

  if (!dataMap) {
    return <div>No Data</div>;
  }

  return (
    <div style={{ position: "relative" , minHeight: "100%"}}>
      <Bar
        options={options}
        data={chartData}
        height={250} // or 500, adjust as needed
      />
    </div>
  );
};

export default CustomBarChart;
