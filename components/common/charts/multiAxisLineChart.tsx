"use client";
// import React, { useState } from "react";
// import {
//   Chart as ChartJS,
//   CategoryScale,
//   LinearScale,
//   PointElement,
//   LineElement,
//   Title,
//   Tooltip,
//   Legend,
// } from "chart.js";
// import { Line } from "react-chartjs-2";

// ChartJS.register(
//   CategoryScale,
//   LinearScale,
//   PointElement,
//   LineElement,
//   Title,
//   Tooltip,
//   Legend
// );

// export const options = {
//   responsive: true,
//   plugins: {
//     legend: {
//       position: "top" as const,
//     },
//     title: {
//       display: true,
//       text: "Total Flows run vs Total Flows passed",
//     },
//   },
//   scales: {
//     x: {
//       grid: {
//         display: true,
//         color: "#ebe5e812",
//       },
//     },
//     y: {
//       grid: {
//         display: true,
//         color: "#ebe5e812",
//       },
//     },
//   },
// };

// // Mock datasets for different timeframes
// const mockData = {
//   "last-week": {
//     labels: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "<PERSON>"],
//     runs: [10, 15, 9, 12, 14, 8, 11],
//     passed: [8, 14, 7, 11, 12, 6, 10],
//   },
//   "last-month": {
//     labels: Array.from({ length: 30 }, (_, i) => `Day ${i + 1}`),
//     runs: Array.from({ length: 30 }, () => Math.floor(Math.random() * 20 + 10)),
//     passed: Array.from({ length: 30 }, () =>
//       Math.floor(Math.random() * 20 + 5)
//     ),
//   },
//   "last-year": {
//     labels: [
//       "Jan",
//       "Feb",
//       "Mar",
//       "Apr",
//       "May",
//       "Jun",
//       "Jul",
//       "Aug",
//       "Sep",
//       "Oct",
//       "Nov",
//       "Dec",
//     ],
//     runs: [100, 120, 130, 90, 110, 95, 125, 140, 105, 115, 135, 150],
//     passed: [80, 100, 110, 70, 90, 85, 100, 120, 95, 100, 120, 130],
//   },
// };

// const MultiAxisLineChart = () => {
//   const [range, setRange] = useState<"last-week" | "last-month" | "last-year">(
//     "last-week"
//   );

//   const { labels, runs, passed } = mockData[range];

//   const data = {
//     labels,
//     datasets: [
//       {
//         label: "Total Flows run",
//         data: runs,
//         borderColor: "rgb(53, 162, 235)",
//         backgroundColor: "rgba(53, 162, 235, 0.5)",
//       },
//       {
//         label: "Total Flows passed",
//         data: passed,
//         borderColor: "rgb(255, 99, 132)",
//         backgroundColor: "rgba(255, 99, 132, 0.5)",
//       },
//     ],
//   };

//   return (
//     <div className="w-full max-w-4xl mx-auto">
//       <div className="flex justify-end mb-4">
//         <select
//           value={range}
//           onChange={(e) => setRange(e.target.value as typeof range)}
//           className="p-2 border rounded-md bg-white text-sm"
//         >
//           <option value="last-week">Last Week</option>
//           <option value="last-month">Last Month</option>
//           <option value="last-year">Last 1 Year</option>
//         </select>
//       </div>

//       <Line options={options} data={data} />
//     </div>
//   );
// };

// export default MultiAxisLineChart;

"use client";
import React from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Line } from "react-chartjs-2";
import CustomSelect from "../custom-select/CustomSelect";
import { data } from "react-router-dom";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const baseOptions = {
  responsive: true,
  plugins: {
    legend: { position: "top" as const },
    title: {
      display: true,
      text: "", // set dynamically
    },
  },
  scales: {
    x: {
      grid: { display: true, color: "#ebe5e812" },
    },
    y: {
      grid: { display: true, color: "#ebe5e812" },
    },
  },
};

interface LineChartDataSet {
  label: string;
  data: number[];
  borderColor?: string;
  backgroundColor?: string;
}

interface LineChartData {
  labels: string[];
  datasets: LineChartDataSet[];
}

interface LineChartProps {
  title?: string;
  dataMap: Record<string, LineChartData>;
  selectedFilter: string;
}

const MultiAxisLineChart: React.FC<LineChartProps> = ({
  title = "",
  dataMap,
  selectedFilter,
}) => {
  const chartData = dataMap?.[selectedFilter];

  const options = {
    ...baseOptions,
    plugins: {
      ...baseOptions.plugins,
      title: { ...baseOptions.plugins.title, text: title },
    },
  };

  if (!dataMap) {
    return <div>No Data</div>;
  }

  console.log("log:: data map is ", dataMap);

  return (
    <Line
      style={{
        height: "100%",
      }}
      options={options}
      data={chartData}
    />
  );
};

export default MultiAxisLineChart;
