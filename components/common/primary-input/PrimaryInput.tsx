import React from "react";

interface PrimaryInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  border?: "primary--border" | "secondary--border" | "";
  placeholder?: string;
  type?: string;
  value?: string | number;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  className?: string;
}

const PrimaryInput: React.FC<PrimaryInputProps> = ({
  border = "primary--border",
  placeholder = "Enter text...",
  type = "text",
  value,
  onChange,
  disabled = false,
  className = "",
  ...props
}) => {
  return (
    <input
      type={type}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      disabled={disabled}
      className={`primary-input w-full px-4 py-2 rounded-md outline-none transition-all  text-white text-sm
        ${
          border === "primary--border"
            ? "border border-[#11112C] focus:border-drcodePurple"
            : ""
        }
        ${
          border === "secondary--border"
            ? "border border-gray-400 focus:border-gray-600"
            : ""
        }
        ${
          disabled ? "bg-gray-200 cursor-not-allowed" : "bg-white text-gray-900"
        }
        ${className}
      `}
      {...props}
    />
  );
};

export default PrimaryInput;
