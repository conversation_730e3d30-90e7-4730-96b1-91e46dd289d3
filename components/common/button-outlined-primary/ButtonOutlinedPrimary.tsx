import React from "react";
interface SecondaryButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  text: string;
  onClick: (...args: any[]) => any;
  size: "small" | "medium" | "large";
}
export const ButtonOutlinedPrimary = ({
  text,
  onClick,
  size,
  ...props
}: SecondaryButtonProps) => {
  const sizeClass = {
    small: "button--small",
    medium: "button--medium",
    large: "button--large",
  }[size];
  return (
    <button
      onClick={onClick}
      className={`button--outlined-primary ${sizeClass}`}
      {...props}
    >
      {text}
    </button>
  );
};
