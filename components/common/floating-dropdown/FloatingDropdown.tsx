"use client";
import { useState, useEffect, useRef, ReactNode } from "react";
import { FaChevronDown } from "react-icons/fa";

interface DropdownProps {
  buttonContent: ReactNode;
  children: ReactNode;
}

export default function FloatingDropdown({ buttonContent, children }: DropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [positionClass, setPositionClass] = useState("left-[-40px]");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Adjust position based on screen width
  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;

      if (buttonRect.right + 200 > viewportWidth) {
        setPositionClass("right-0");
      } else {
        setPositionClass("left-[-40px]");
      }
    }
  }, [isOpen]);

  return (
    <div className="relative inline-block" ref={dropdownRef}>
      {/* Toggle Button */}
      <button
        ref={buttonRef}
        onClick={() => setIsOpen((prev) => !prev)}
        className="flex items-center justify-between focus:outline-none"
      >
        {buttonContent} 
      </button>

      {/* Dropdown Content */}
      {isOpen && (
        <div
          className={`absolute mt-2 min-w-[200px] bg-drcodeBlue border-drcodePurple border-1 rounded-md shadow-lg p-2 z-50 ${positionClass}`}
        >
          {children}
        </div>
      )}
    </div>
  );
}
