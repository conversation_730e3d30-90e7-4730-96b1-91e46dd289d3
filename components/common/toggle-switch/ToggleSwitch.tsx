"use client";

import { useState } from "react";

interface ToggleSwitchProps {
  value: boolean;
  onToggle: (isConnected: boolean) => void;
  label: string;
  disabled?: boolean; // Add optional disabled prop
}

export default function ToggleSwitch({
  value,
  onToggle,
  label,
  disabled = false, // Default to false
}: ToggleSwitchProps) {
  const [isConnected, setIsConnected] = useState(value);

  const toggleConnection = () => {
    if (disabled) return; // Prevent toggling if disabled
    setIsConnected(!isConnected);
    onToggle(!isConnected);
  };


  return (
    <div className="flex items-center justify-between gap-2">
      <span
        className={`text-[12px] font-medium ${
          disabled ? "text-gray-500" : "text-gray-300"
        }`}
      >
        {label}
      </span>
      <button
        type="button"
        role="switch"
        aria-checked={isConnected}
        aria-disabled={disabled}
        onClick={toggleConnection}
        disabled={disabled}
        className={`relative inline-flex h-6 w-12 items-center rounded-full transition-colors focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-500 ${
          isConnected ? "bg-violet-500" : "bg-gray-700"
        } ${disabled ? "opacity-50 cursor-not-allowed" : ""}`}
      >
        <span
          className={`inline-block h-5 w-5 transform rounded-full bg-white transition-transform ${
            isConnected ? "translate-x-7" : "translate-x-1"
          }`}
        />
      </button>
    </div>
  );
}
