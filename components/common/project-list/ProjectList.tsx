"use client";
import { useGlobalStore } from "@/stores/globalstore";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import {
  LOCAL_STORAGE_DATA_KEYS,
  UserData,
} from "@/temp-utils/Constants/localStorageDataModels";
import { getDataFromLocalStorage } from "@/temp-utils/globalUtilities";
import axios from "axios";
import { useParams, useRouter } from "next/navigation";
import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import Select from "react-select";
import Modal from "../modal/Modal";
import PrimaryInput from "../primary-input/PrimaryInput";
import { toast } from "react-toastify";
import useToast from "@/hooks/useToast";
interface OptionType {
  value: any;
  label: string;
}
const ProjectList = (props: {
  className?: string;
  setSelectedProject?: Dispatch<
    SetStateAction<{ value: string; label: string }[]>
  >;
}) => {
  const router = useRouter();
  const { showLoader } = useGlobalStore();
  const {showToast} = useToast()
  const { projectId } = useParams();
  const [selectedOption, setSelectedOption] = useState<OptionType | undefined>(
    undefined
  );
  const [showCreateProjectModal, setShowCreateProjectModal] = useState(false);
  const [projectName, setProjectName] = useState<string>("");
  const userData: UserData = getDataFromLocalStorage(
    LOCAL_STORAGE_DATA_KEYS.USER_DATA
  ) as UserData;

  const [projectList, setProjectList] = useState<
    { value: string; label: string }[]
  >([]);

  const [projectResponse, setProjectResponse] = useState<any>();

  const handleChange = (selectedOption: any) => {
    setSelectedOption(selectedOption);
    router.push(`/project/${selectedOption.value}`);

    if (props.setSelectedProject) {
      const selectedEnv = projectResponse?.find(
        (item: any) => item.id === selectedOption.value
      );
      const envs: { value: string; label: string }[] = [];
      for (const key in selectedEnv?.env) {
        envs.push({ value: selectedEnv.env[key], label: key });
      }
      envs.unshift({ value: "", label: "None" });
      props.setSelectedProject(envs);
    }
  };
  const getProjectList = async () => {
    try {
      //showLoader(true)
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/projects/user/${userData.user_id}`
      );
      let responseData = response.data.data;

      if (projectId) {
        let findProject = responseData.find(
          (item: any) => item.id == projectId
        );

        console.log("log:: find project is ", findProject,responseData,projectId);
        let projectOption: any = {};
        projectOption["value"] = findProject?.id;
        projectOption["label"] = findProject?.project_name;
        setSelectedOption(projectOption);
      }
      let filterList = responseData?.map((item: any) => {
        return { value: item.id, label: item.project_name };
      });
      console.log("log:: filter list is ", filterList);
      setProjectList(filterList);
      setProjectResponse(responseData);
    } catch (err) {
      //showLoader(false);
      console.error("Error:", err);
    } finally {
      //showLoader(false);
    }
  };

  const createProject = async () => {
    const payload = {
      projectName: projectName,
      userId: userData.user_id,
    };
    try {
      //showLoader(true)
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/projects`,
        payload
      );
      if (response.data.success) {
        setProjectList((prev) => [...prev, response.data.data]);
        // toast.success("Project added");
        showToast("Project added", "success")
        setShowCreateProjectModal(false);

        return response.data.data.id;
      }
    } catch (err) {
      console.error("Error:", err);
    } finally {
      //showLoader(false)
    }
  };

  const createEnvironmentVariables = async (data: any) => {
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables`,
        data
      );
      return response;
    } catch (error) {
      console.error("Error in creating environment variables:", error);
    }
  };

  const createGroup = async (projectId: number) => {
    const response = await createEnvironmentVariables({
      name: "Default Environment",
      env: {},
      user_id: userData.user_id,
    });
    let environmentVariable = response.data.id;

    const payload = {
      groupName: "Default Folder",
      projectId: projectId,
      description: "This is a Default Folder",
      env: environmentVariable,
      env_id: environmentVariable,
    };

    try {
      // Create new group
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/groups`,
        payload
      );
    } catch (error) {
      console.error(error);
    }
  };

  const handleCreateProject = async (e: any) => {
    e.preventDefault();
    const projectId = await createProject();
    await createGroup(projectId);

    router.push(`/project/${projectId}`);
  };

  useEffect(() => {
    getProjectList();
  }, []);

  return (
    <>
      <Select
        className={props.className ?? "w-[20%]"}
        options={[
          ...projectList,
          { label: "➕ Add Project", value: "create_new" },
        ]}
        placeholder="Project"
        value={selectedOption}
        onChange={(selected) => {
          if (selected.value === "create_new") {
            // router.push(`/project-settings?add=true`);
            setShowCreateProjectModal(true);
          } else {
            handleChange(selected);
          }
        }}
        classNamePrefix="react-select"
        instanceId="method-select"
        styles={{
          control: (provided) => ({ ...provided, fontSize: "14px" }),
          input: (provided) => ({ ...provided, fontSize: "14px" }),
          option: (provided) => ({ ...provided, fontSize: "14px" }),
          singleValue: (provided) => ({ ...provided, fontSize: "14px" }),
        }}
      />

      {showCreateProjectModal && (
        <Modal isOpen={showCreateProjectModal} title="Create Project">
          <label htmlFor="name">Name</label>
          <PrimaryInput
            className="my-2"
            placeholder="eg. Project Invisible"
            value={projectName}
            onChange={(e) => setProjectName(e.target.value)}
          />
          <div className="flex justify-end gap-4 items-center">
            <button
              type="button"
              onClick={() => setShowCreateProjectModal(false)}
              className="button--outlined-primary mt-4 py-2 px-8 text-[14px]"
              style={{ fontSize: "14px" }}
            >
              Close
            </button>
            <button
              onClick={handleCreateProject}
              className="button--primary mt-4 py-2 px-8 text-[14px]"
              style={{ fontSize: "14px" }}
            >
              Create
            </button>
          </div>
        </Modal>
      )}
    </>
  );
};

export default ProjectList;
