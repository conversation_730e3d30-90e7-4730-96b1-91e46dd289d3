import React from "react";

type LoaderProgressProps = {
  progress: number;
  size?: number;
  strokeWidth?: number;
};

const LoaderProgress: React.FC<LoaderProgressProps> = ({
  progress,
  size = 80,
  strokeWidth = 8,
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const offset = circumference - (progress / 100) * circumference;

  return (
    <div className="relative flex items-center justify-center">
      <svg width={size} height={size} className="transform -rotate-90">
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="#e5e7eb"
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="#875bf8"
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={circumference}
          strokeDashoffset={offset}
          strokeLinecap="round"
          style={{
            transition: "stroke-dashoffset 0.5s ease-out", // 👈 Smooth transition
          }}
        />
      </svg>
      <div className="absolute text-drcodePurple font-semibold text-sm">
        {Math.round(progress)}%
      </div>
    </div>
  );
};

export default LoaderProgress;
