import React from 'react'
interface ButtonDarkSecondaryProps {
    text: string;
    onClick: (...args: any[]) => any;
    size: 'small' | 'medium' | 'large';

}

export const ButtonDarkSecondary = ({ text, onClick, size }: ButtonDarkSecondaryProps) => {
    const sizeClass = {
        small: 'button--small',
        medium: 'button--medium',
        large: 'button--large',
    }[size];
    return (
        <button onClick={onClick} className={`button--dark-secondary ${sizeClass}`}>{text}</button>
    )
}