"use client";
import { TestCase } from "@/components/test-runner/tests-table/TestsTable";
import Editor, { MonacoDiffEditor, loader } from "@monaco-editor/react";
import React, { useEffect } from "react";

const ApiRequestResponseOutputView = ({
  value,
  responseType,
  readonly,
  onChange,
  height = "35vh",
  background = "#0D0D22",
}: {
  value: string;
  responseType: string;
  readonly: boolean;
  onChange: (value: string | undefined) => void;
  height?: string;
  background?: string;
}) => {
  const fileTypes: any = {
    css: "css",
    js: "javascript",
    json: "json",
    md: "markdown",
    mjs: "javascript",
    ts: "typescript",
  };

  const handleEditorInit = (editor: any) => {
    if (!editor) return;
    setTimeout(() => {
      editor?.getAction("editor.action.formatDocument")?.run();
    });
    if (readonly) {
      setTimeout(() => {
        editor.updateOptions({ readOnly: true });
      }, 550);
    }
  };

  // useEffect(() => {
  //   loader &&
  //     loader.init().then((monaco) => {
  //       monaco.editor.defineTheme("myCustomTheme", {
  //         base: "vs-dark",
  //         inherit: true,
  //         rules: [],
  //         colors: {
  //           "editor.background": "#0D0D22",
  //         },
  //       });
  //     });
  // }, []);
  useEffect(() => {
    loader &&
      loader.init().then((monaco) => {
        monaco.editor.defineTheme("myCustomTheme", {
          base: "vs-dark",
          inherit: true,
          rules: [],
          colors: {
            "editor.background": background, // Editor background
            "editor.lineHighlightBackground": "#3A3A55", // Change highlighted line background
            "editor.lineHighlightBorder": "#0D0D22", // Optional border for the highlighted line
          },
        });
  
        monaco.editor.setTheme("myCustomTheme");
      });
  }, []);
  
  if(typeof value !== "string") return <></>
  return (
    <div className="api-request-response-viewer">
      <Editor
        width="100%"
        onChange={(e) => {
          onChange(e);
        }}
        options={{ minimap: { enabled: false } }}
        onMount={(editor) => {
          handleEditorInit(editor);
        }}
        value={value}
        height={height}
        defaultLanguage={fileTypes[responseType] || "json"}
        theme="myCustomTheme"
      />
    </div>
  );
};

export default ApiRequestResponseOutputView;
