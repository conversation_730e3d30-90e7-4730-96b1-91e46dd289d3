import React from "react";
import Select, {
  components,
  OptionProps,
  SingleValueProps,
} from "react-select";

interface OptionType {
  value: string;
  label: string;
  [key: string]: any; // allow custom fields
}

interface CustomSelectProps {
  options: OptionType[];
  value: string;
  onChange: (value: string) => void;
  renderOption?: (option: OptionType) => React.ReactNode;
  renderSingleValue?: (option: OptionType) => React.ReactNode;
}

const CustomOption = (renderFn?: (option: OptionType) => React.ReactNode) => {
  return (props: OptionProps<OptionType, false>) => {
    const { data, innerRef, innerProps, isFocused } = props;
    return (
      <div
        ref={innerRef}
        {...innerProps}
        className={`px-3 py-2 cursor-pointer ${isFocused ? "bg-gray-700" : ""}`}
      >
        {renderFn ? renderFn(data) : data.label}
      </div>
    );
  };
};

const CustomSingleValue = (
  renderFn?: (option: OptionType) => React.ReactNode
) => {
  return (props: SingleValueProps<OptionType, false>) => {
    const { data } = props;
    return (
      <components.SingleValue {...props}>
        {renderFn ? renderFn(data) : data.label}
      </components.SingleValue>
    );
  };
};

const CustomSelect = ({
  options,
  value,
  onChange,
  renderOption,
  renderSingleValue,
}: CustomSelectProps) => {
  const selected = options.find((opt) => opt.value === value) || null;

  return (
    <Select
      className="w-full"
      options={options}
      value={selected}
      onChange={(selectedOption) => {
        if (selectedOption) onChange(selectedOption.value);
      }}
      instanceId="custom-select"
      classNamePrefix="react-select"
      components={{
        Option: CustomOption(renderOption),
        SingleValue: CustomSingleValue(renderSingleValue),
      }}
      styles={{
        control: (base) => ({
          ...base,
          fontSize: "14px",
          fontWeight: "400",
          backgroundColor: "#1a1a1f",
          color: "#E2E2ED",
        }),
        singleValue: (base) => ({
          ...base,
          color: "#E2E2ED",
        }),
        placeholder: (base) => ({
          ...base,
          color: "#E2E2ED",
        }),
        menu: (base) => ({
          ...base,
          backgroundColor: "#1a1a1f",
          fontSize: "14px",
          // Removed maxHeight and overflowY from here
        }),
        menuList: (base) => ({
          ...base,
          maxHeight: "200px",
          overflowY: "auto",
        }),
        option: (base, { isSelected }) => ({
          ...base,
          backgroundColor: isSelected ? "#2a2a38" : "transparent",
          color: "#E2E2ED",
        }),
      }}
    />
  );
};

export default CustomSelect;
