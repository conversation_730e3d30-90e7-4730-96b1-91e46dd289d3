import React from "react";

interface PrimaryButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void;
  label?: string;
  disabled?: boolean;
}

const PrimaryButton = ({
  onClick,
  label,
  disabled = false,
  children,
  ...rest
}: PrimaryButtonProps) => {
  return (
    <button
      onClick={onClick}
      className="button--primary py-2 px-8 text-[14px]"
      style={{
        fontSize: "14px",
      }}
      disabled={disabled}
      {...rest}
    >
      {label ?? children}
    </button>
  );
};

export default PrimaryButton;
