import React from "react";

interface PrimaryButtonOutlinedProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void;
  label?: string;
  disabled?: boolean;
}

const PrimaryButtonOutlined = ({
  onClick,
  label,
  disabled = false,
  children,
  ...rest
}: PrimaryButtonOutlinedProps) => {
  return (
    <button
      onClick={onClick}
      className="button--outlined-primary py-2 px-8 text-[14px]"
      style={{
        fontSize: "14px",
      }}
      disabled={disabled}
      {...rest}
    >
      {label ?? children}
    </button>
  );
};

export default PrimaryButtonOutlined;
