import React from "react";
import { FaChevronDown, FaChevronUp } from "react-icons/fa";

interface CollapsibleViewProps {
  children: React.ReactNode;
  header: React.ReactNode;
  collapsed?: boolean;
  onToggle?: (collapsed: string | number) => void;
  itemKey?: string | number;
}

const CollapsibleView = ({
  header,
  children,
  collapsed = false,
  onToggle,
  itemKey,
}: CollapsibleViewProps) => {
  return (
    <div>
      <div
        onClick={() => {
          if (onToggle) {
            console.log("log:: onToggle ", itemKey);
            onToggle(itemKey);
          }
        }}
        className="px-2 py-1 bg-drcodePurple/10 rounded-md  w-full cursor-pointer flex justify-between items-center"
      >
        <span>{header}</span>
        {collapsed ? <FaChevronDown /> : <FaChevronUp />}
      </div>

      {!collapsed && (
        <div className="p-2">
          {children}
        </div>
      )}
    </div>
  );
};

export default CollapsibleView;
