import React, { useEffect, useRef, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import axios from "axios";
import {
  getDataFromLocalStorage,
  removeDataFromLocalStorage,
  setDataOnLocalStorage,
} from "@/temp-utils/globalUtilities";
import {
  LOCAL_STORAGE_DATA_KEYS,
  UserData,
} from "@/temp-utils/Constants/localStorageDataModels";
import { useGlobalStore } from "@/stores/globalstore";
import {
  NEXT_PUBLIC_DR_CODE_AUTH_BASE_API_URL,
  NEXT_PUBLIC_DR_CODE_BASE_API_URL,
} from "@/temp-utils/Constants/globalVariables";

const RedirectPage: React.FC = () => {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const { loader, showLoader, setUserData } = useGlobalStore();
  const [loadingStep, setLoadingStep] = useState("");
  const searchParams = useSearchParams();
  const isApiCalled = useRef(false);

  useEffect(() => {
    if (isApiCalled.current) return; // Prevent duplicate calls
    isApiCalled.current = true;

    const fetchSessionId = async () => {
      try {
        const code = searchParams.get("code");
        const page = searchParams.get("page")
        const carrier = getDataFromLocalStorage(
          LOCAL_STORAGE_DATA_KEYS.CARRIER
        );
        const isAudit = getDataFromLocalStorage(
          LOCAL_STORAGE_DATA_KEYS.IS_AUDIT_LOGIN
        );

        if (!code || !carrier) {
          throw new Error(`Missing code or carrier`);
        }

        const apiUrl =
          isAudit == 1
            ? `${NEXT_PUBLIC_DR_CODE_AUTH_BASE_API_URL}bucket=auth&operationName=gitCallback&carrier=${carrier}&code=${code}&is_audit=1`
            : `${NEXT_PUBLIC_DR_CODE_AUTH_BASE_API_URL}bucket=auth&operationName=gitCallback&carrier=${carrier}&code=${code}`;
        console.log("logg:: is audit", apiUrl);
        const response = await axios.get(apiUrl);
        const sessionId = response.data.data.sessionId;

        if (!sessionId) {
          throw new Error("Session ID not found in response");
        }

        axios.defaults.headers.common["Authorization"] = `Bearer ${sessionId}`;
        setDataOnLocalStorage(LOCAL_STORAGE_DATA_KEYS.SESSION_ID, sessionId);

        const profileResponse = await fetch(
          `${NEXT_PUBLIC_DR_CODE_AUTH_BASE_API_URL}bucket=account&operationName=profile`,
          {
            headers: {
              Authorization: `${sessionId}`,
            },
          }
        );

        if (!profileResponse.ok) {
          throw new Error("Failed to fetch user profile");
        }

        const result: { message: string; data: UserData } =
          await profileResponse.json();

        if (result?.message !== "ok") {
          throw new Error("Invalid profile response");
        }

        // Save user data to local storage
        setDataOnLocalStorage(
          LOCAL_STORAGE_DATA_KEYS.SELECTED_ORG,
          result.data.orgs[0].org_name
        );
        setDataOnLocalStorage(LOCAL_STORAGE_DATA_KEYS.USER_DATA, result.data);
        setUserData(result.data);
        removeDataFromLocalStorage(LOCAL_STORAGE_DATA_KEYS.CARRIER);
        removeDataFromLocalStorage(LOCAL_STORAGE_DATA_KEYS.IS_AUDIT_LOGIN);

        if (getDataFromLocalStorage(LOCAL_STORAGE_DATA_KEYS.VSCODE)) {
          const vscodeUrl = `${LOCAL_STORAGE_DATA_KEYS.VSCODE_URL}${sessionId}`;
          window.location.href = vscodeUrl;
          removeDataFromLocalStorage(LOCAL_STORAGE_DATA_KEYS.VSCODE);
        } else {
          removeDataFromLocalStorage(LOCAL_STORAGE_DATA_KEYS.VSCODE);
          let existingUser = await getUserById(result.data.user_id);

          if (!existingUser) {
            existingUser = await createUser(result.data.user_id);
            if (!existingUser) {
              setError("Failed to create user, please try again.");
              return;
            }
          }
          if(page === "audit"){
            router.push(`/api-audit-report`);
          }else{
            router.push(`/project`);
          }
        }
      } catch (error) {
        console.error("Error during authentication:", error);
        setError(
          error instanceof Error ? error.message : "An unknown error occurred"
        );
      } finally {
        // Stop loading if there's an error or navigation is done
        //showLoader(false);
      }
    };

    fetchSessionId();
  }, [router, searchParams]);

  const getUserById = async (userId: string): Promise<UserData | null> => {
    try {
      const response = await fetch(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/users/${userId}`
      );
      if (response.ok) {
        const user: UserData = await response.json();
        return user;
      } else if (response.status === 404) {
        return null;
      } else {
        console.error(`Failed to fetch user: ${response.statusText}`);
        return null;
      }
    } catch (error) {
      console.error("Error fetching user:", error);
      return null;
    }
  };

  const createUser = async (userId: string): Promise<UserData | null> => {
    try {
      const response = await fetch(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/users`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ userId: userId }),
        }
      );

      if (response.ok || response.status === 201) {
        const user: UserData = await response.json();
        return user;
      } else {
        console.error(`Failed to create user: ${response.statusText}`);
        return null;
      }
    } catch (error) {
      console.error("Error creating user:", error);
      return null;
    }
  };

  const createEnvironmentVariables = async (data: any) => {
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables`,
        data
      );
      return response;
    } catch (error) {
      console.error("Error in creating environment variables:", error);
    }
  };

  const createGroup = async (projectId: number, user_id) => {
    const response = await createEnvironmentVariables({
      name: "Default Environment",
      env: {},
      user_id: user_id,
    });
    let environmentVariable = response?.data.id;

    const payload = {
      groupName: "Default Folder",
      projectId: projectId,
      description: "This is a Default Folder",
      env: environmentVariable,
      env_id: environmentVariable,
    };

    try {
      await axios.post(`${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/groups`, payload);
    } catch (error) {
      console.error(error);
    }
  };

  const createProject = async (user_id) => {
    const payload = {
      projectName: "Default Project",
      userId: user_id,
    };
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/projects`,
        payload
      );
      return response.data.data.id;
    } catch (err) {
      console.error("Error:", err);
    }
  };

  const fetchUserProjects = async (user_id) => {
    setLoadingStep("FETCHING PROJECT");
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/projects/user/${user_id}`
      );
      if (response.data.success) {
        let responseData = response.data.data;
        if (responseData.length === 0) {
          const projectId = await createProject(user_id);
          await createGroup(projectId, user_id);
          router.push(`/project/${projectId}/api-audit`);
        } else {
          router.push(`/project/${responseData[0].id}/api-audit`);
        }
      }
    } catch (err) {
      console.error("Error:", err);
    }
  };

  if (loader) {
    //showLoader(true)
  }

  if (error) {
    //showLoader(false)
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-[#080814] text-white">
        <h1 className="text-2xl font-bold mb-4">Authentication Error</h1>
        <p>{error}</p>
        <button
          className="mt-4 px-4 py-2 bg-blue-500 rounded hover:bg-blue-600"
          onClick={() => router.push("/")}
        >
          Return to Home
        </button>
      </div>
    );
  }

  return null; // Prevent rendering anything else during navigation
};

export default RedirectPage;
