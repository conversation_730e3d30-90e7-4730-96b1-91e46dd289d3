"use client";
import React, { useState } from "react";
import { FaChevronRight } from "react-icons/fa";

interface AccordionItem {
  id: string;
  title: string;
  statusColor?: string; // for the dot
  content: React.ReactNode;
  deleteAssertion?: React.ReactNode;
}

interface StyledAccordionProps {
  items: AccordionItem[];
}

const Accordion: React.FC<StyledAccordionProps> = ({ items = [] }) => {
  const [openId, setOpenId] = useState<string | null>(null);

  const toggle = (id: string) => {
    setOpenId((prev) => (prev === id ? null : id));
  };

  return (
    <div className="space-y-2 w-full">
      {items?.map((item) => {
        const isOpen = openId === item.id;
        return (
          <div
            key={item.id}
            className="bg-[#0D0C24] text-white rounded-md transition-colors hover:bg-[#14132f] my-2"
          >
            <button
              className="w-full flex justify-between items-center px-4 py-3"
              onClick={() => toggle(item.id)}
            >
              <div>
                <div className="flex items-center gap-2">
                  <p className="font-semibold text-sm">{item.title}</p>
                  {
                    item.statusColor && (
                      <span
                      className={`h-2 w-2 rounded-full ${
                        item.statusColor || "bg-red-400"
                      }`}
                    />
                    )
                  }
                 
                </div>
              </div>
              <div className="flex items-center gap-2">
                {item.deleteAssertion}
                <FaChevronRight
                className={`h-4 w-4 text-gray-400 transition-transform ${
                  isOpen ? "rotate-90" : ""
                }`}
              />
              </div>
              
            </button>
            {isOpen && (
              <div className="px-4 pb-4 text-sm text-gray-300">
                {item.content}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default Accordion;
