"use client";
import React, { Dispatch, SetStateAction, useMemo, useState } from "react";
import styles from "./AddApiPostman.module.scss";
import Image from "next/image";
import ApiRequestResponseOutputView from "../common/api-request-response-output-view/ApiRequestResponseOutputView";
import "react-dropzone-uploader/dist/styles.css";
import Dropzone, { IFileWithMeta, StatusValue } from "react-dropzone-uploader";
import { useParams } from "next/navigation";
import { FaRegPaste } from "react-icons/fa6";
import { loadPostmanCollection } from "@/temp-utils/Services/parsingPostmanCollection";
import CollapsibleView from "../common/collapsible-view/CollapsibleView";
import { MdOutlineUploadFile } from "react-icons/md";

interface FileMeta {
  name: string;
  size: number;
  type: string;
  lastModified: number;
}

const AddApiPostman = (props: {
  setPostmanCollection: Dispatch<SetStateAction<any[]>>;
  groupId: number;
  setShowAddImportPopup?: (show: boolean) => void;
  setSampleCollection: Dispatch<string>;
  setShowSelectApiModal?: (show: boolean) => void;
  setErrorMessage?: (message: string) => void;
  setSelectedApis?: (apis: any[]) => void;
}) => {
  const [activeTab, setActiveTab] = useState(1);
  const [files, setFiles] = useState<any[]>([]);
  const [manualInput, setManualInput] = useState("");

  async function transformPostmanCollections(postmanCollections: any) {
    const result = await loadPostmanCollection(postmanCollections);
    console.log("selected apis ", result);
    // if (props.setSelectedApis) {
    //   const selectedApisIds = result.apis.map((item) => item.id);
    //   props.setSelectedApis(selectedApisIds);
    // }
    return result.apis;
  }

  const handleChangeStatus = (
    fileWithMeta: IFileWithMeta,
    status: StatusValue,
    allFiles: IFileWithMeta[]
  ) => {
    const { file } = fileWithMeta;

    if (status === "done") {
      const reader = new FileReader();

      reader.onload = (event: ProgressEvent<FileReader>) => {
        try {
          const json = JSON.parse(event.target?.result as string);
          props.setSampleCollection(event.target?.result as string);
          loadPostmanCollection(json)
            .then((formattedCollectionsData) => {
              setFiles(formattedCollectionsData.apis);
              props.setPostmanCollection(formattedCollectionsData.apis);
              // if(props.setSelectedApis){
              //   const selectedApisIds = formattedCollectionsData.apis.map((item) => item.id);
              //   props.setSelectedApis(selectedApisIds);
              // }
            })
            .catch((error) => {
              console.error("Error processing Postman collection:", error);
              props.setErrorMessage?.(
                error.message || "Error processing Postman collection"
              );
            });
        } catch (error) {
          console.error("Error parsing JSON:", error);
        }
      };

      reader.readAsText(file);
    } else if (status === "removed") {
      setActiveTab(0);
      setActiveTab(1);
      setFiles([]);
      props.setPostmanCollection([]);
      props.setSampleCollection("");
    }
  };

  const onManualCollectionInput = async (value: string) => {
    setManualInput(value);
    try {
      props.setSampleCollection(value);
      const parsedJSON = JSON.parse(value);
      const transformedData = await transformPostmanCollections(parsedJSON);
      props.setPostmanCollection(transformedData);
    } catch (e) {
      console.error("Invalid JSON input", e);
    }
  };

  const onRedirect = () => props.setShowAddImportPopup?.(false);

  return (
    <div className={`${styles["postman-sub-tabs"]} m-0`}>
      {/* <nav className={`${styles["tab-list"]}`}>
        {tabs.map((tab, index) => (
          <button
            className={`${styles["tab"]} ${
              index === activeTab ? styles["active"] : ""
            }`}
            onClick={() => onTabClick(tab, index)}
            key={tab.id}
          >
            <Image src={tab.icon} height={20} width={20} alt="Icon" />
            <p>{tab.name}</p>
          </button>
        ))}
      </nav>

      <p className="my-4">
        <span className="gap-2 inline mr-2">
          <Image
            className="inline"
            src={`/info.svg`}
            height={14}
            width={14}
            alt="Icon"
          />
        </span>
        v2.1 version of Postman Collections is supported
      </p>
      {activeTab === 0 && (
        <div>
          <p className="title--small">Postman collection</p>
          <ApiRequestResponseOutputView
            onChange={(e) => onManualCollectionInput(e || "")}
            readonly={false}
            responseType="json"
            value={manualInput}
          />
        </div>
      )}
      {activeTab === 1 && (
        <div className="w-full">
          <Dropzone
            onChangeStatus={handleChangeStatus}
            accept="application/json"
            maxFiles={1}
            canRemove={true}
            inputContent="Drop a JSON file or click to browse"
            styles={{
              dropzone: {
                width: "100%",
                height: 200,
                overflow: "hidden",
                border: "2px dashed #875bf8",
                borderRadius: "12px",
                backgroundColor: "transparent",
                color: "#FFF",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "16px",
                fontWeight: "bold",
                transition: "all 0.3s ease-in-out",
                boxShadow: "0 4px 10px rgba(0, 255, 0, 0.2)",
              },
              dropzoneActive: {
                borderColor: "#00FF00",
                transform: "scale(1.02)",
              },
              inputLabel: {
                color: "#FFF",
                fontSize: "16px",
                fontWeight: "500",
              },
              preview: {
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                borderRadius: "8px",
                border: "1px solid rgba(255, 255, 255, 0.2)",
                backgroundColor: "rgba(255, 255, 255, 0.1)",
                padding: "8px",
                marginTop: "10px",
                width: "100%",
                color: "#FFF",
                fontSize: "14px",
                fontWeight: "500",
                textAlign: "center",
                transition: "all 0.3s ease-in-out",
              },
              previewImage: {
                maxWidth: "50px",
                maxHeight: "50px",
                borderRadius: "4px",
                marginBottom: "4px",
              },
              input:{color:"white"}
            }}
            
          />
        </div>
      )} */}

      {/* <CollapsibleView
        itemKey={1}
        collapsed={activeTab !== 1}
        onToggle={(key) => setActiveTab(key as number)}
        header={
          <div className="flex items-center gap-2">
            <MdOutlineUploadFile />
            Upload
          </div>
        }
      > */}
      <div className="w-full">
        <Dropzone
          onChangeStatus={handleChangeStatus}
          accept="application/json"
          maxFiles={1}
          canRemove={true}
          inputContent="Drop a JSON file or click to browse"
          styles={{
            dropzone: {
              width: "100%",
              height: 120,
              overflow: "hidden",
              border: "2px dashed #875bf8",
              borderRadius: "12px",
              backgroundColor: "transparent",
              color: "#FFF",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              fontSize: "16px",
              fontWeight: "bold",
              transition: "all 0.3s ease-in-out",
            },
            dropzoneActive: {
              borderColor: "#00FF00",
              transform: "scale(1.02)",
            },
            inputLabel: {
              color: "#FFF",
              fontSize: "16px",
              fontWeight: "500",
            },
            preview: {
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              borderRadius: "8px",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              backgroundColor: "rgba(255, 255, 255, 0.1)",
              padding: "8px",
              marginTop: "10px",
              width: "100%",
              color: "#FFF",
              fontSize: "14px",
              fontWeight: "500",
              textAlign: "center",
              transition: "all 0.3s ease-in-out",
            },
            previewImage: {
              maxWidth: "50px",
              maxHeight: "50px",
              borderRadius: "4px",
              marginBottom: "4px",
            },
            input: { color: "white" },
          }}
        />
      </div>
      {/* </CollapsibleView> */}

      <p className="my-1 text-center">OR</p>

      {/* <CollapsibleView
        itemKey={0}
        collapsed={false}
        onToggle={(key) => setActiveTab(key as number)}
        header={
          <div className="flex items-center gap-2">
            <FaRegPaste />
            Paste
          </div>
        }
      > */}
      <div className="px-4">
        <p className="text-[#E2E2ED] font-sans text-sm flex justify-between items-center pr-4">
          <p> Paste your postman collection and watch the tests take off 🚀</p>
        </p>

        <div className="mt-4 border-[#1B1B41] border-1 rounded-lg overflow-hidden">
          <ApiRequestResponseOutputView
            height="120px"
            onChange={(e) => onManualCollectionInput(e || "")}
            readonly={false}
            responseType="json"
            value={manualInput}
          />
        </div>
      </div>
      {/* </CollapsibleView> */}
    </div>
  );
};

export default AddApiPostman;
