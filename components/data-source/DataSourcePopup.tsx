import Image from "next/image";
import React from "react";

interface DataSourcePopupProps {
  onClose?: () => void;
  onCsvClick?: () => void;
  onAiClick?: () => void;
}

const DataSourcePopup = ({
  onAiClick,
  onClose,
  onCsvClick,
}: DataSourcePopupProps) => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-drcodeBlue/20 bg-opacity-60 backdrop-blur-lg z-50">
      <div
        className={`bg-drcodeBlue text-white p-4 rounded-lg  border-1 border-[#2E2E60] w-full max-w-[600px]`}
      >
        <div className="text-[16px] font-bold text-[#D1D1E3]">
          How do you want to run this test?
        </div>
        <div className="text-[13px] font-normal text-[#9494A1]">
          Choose the type of data you’d like to use for testing.
        </div>

        <div className="flex gap-4 my-4">
          <div
            onClick={onCsvClick}
            className="h-[210px] cursor-pointer border-1 border-[#2E2E60] rounded-lg flex-1 flex space-y-2 flex-col items-center justify-center hover:bg-[#2E2E60]"
          >
            <Image
              src={"/csv_upload.svg"}
              alt="Use CSV"
              width={50}
              height={50}
            />
            <div className="text-[14px] text-[#B2B2C1] font-semibold">
              {" "}
              Use CSV Data
            </div>
            <div className="text-[11px] text-[#9494A1]">
              Test with your own data
            </div>
          </div>

          <div
            onClick={onAiClick}
            className="h-[210px] cursor-pointer border-1 border-[#2E2E60]  rounded-lg flex-1 flex space-y-2 flex-col items-center justify-center hover:bg-[#2E2E60]"
          >
            <Image src={"/sparkler.svg"} alt="Use CSV" width={50} height={50} />
            <div className="text-[14px] text-[#B2B2C1] font-semibold">
              {" "}
              Use AI Data
            </div>
            <div className="text-[11px] text-[#9494A1]">
              Auto-generate test data
            </div>
          </div>
        </div>

        <div className="flex w-full justify-end">
          <button
            onClick={onClose}
            className="button--outlined-primary py-2 px-8 text-[14px]"
            style={{
              fontSize: "14px",
            }}
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default DataSourcePopup;
