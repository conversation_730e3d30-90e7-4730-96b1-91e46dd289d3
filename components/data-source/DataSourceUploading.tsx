import React, { useEffect, useState } from "react";
import LoaderGif from "../common/loader-gif/LoaderGif";

const STEPS = [
  { id: 1, title: "Reading uploaded CSV data…" },
  { id: 2, title: "Processing and validating input fields…" },
  { id: 3, title: "Mapping data to test cases…" },
  { id: 4, title: "Finalizing configuration for execution…" },
];

const DataSourceUploading = () => {
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStep((prevStep) =>
        prevStep === STEPS.length - 1 ? 0 : prevStep + 1
      );
    }, 5000);

    return () => clearInterval(interval);
  }, []);
  return (
    <div className="flex h-[80%] flex-col justify-center items-center">
      <LoaderGif />

      <div className={`text-container `}>
        <div key={currentStep} className="step-text animate-slide">
          {STEPS[currentStep].title}{" "}
        </div>
      </div>
    </div>
  );
};

export default DataSourceUploading;
