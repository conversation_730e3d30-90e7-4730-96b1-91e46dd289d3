"use client";

import useToast from "@/hooks/useToast";
import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import axios from "axios";
import { DATASOURCE_TYPE } from "@/app/project/[projectId]/group/[groupId]/testSuit/[testSuitId]/file-mapping-model/useFileMapping";

const useDataSource = () => {
  const [fileData, setFileData] = useState<any[]>([]);
  const [uniqueData, setUniqueData] = useState<any[]>([]);
  const [keyTypes, setKeyTypes] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [isReupload, setIsReupload] = useState(false);
  const [reuploadModal, setReuploadModal] = useState(false);

  const intervalIdRef = useRef<NodeJS.Timeout | null>(null);

  const router = useRouter();
  const params = useParams();
  const searchParms = useSearchParams();
  const { showToast } = useToast();

  const id = params.testSuitId ?? params.e2eId ?? "";
  const entity_type: DATASOURCE_TYPE = params.testSuitId
    ? DATASOURCE_TYPE.TEST_SUITE
    : DATASOURCE_TYPE.E2E;

  const fetchDataSource = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.READ_DATA_SOURCE}/${id}?type=${entity_type}`
      );

      if (response.data?.success) {
        const fileValues = response.data?.data?.map((item) => {
          return {
            ...item.data_values,
            is_used: item.is_used,
            dataIdDrCode: item.id,
          };
        });
        setFileData(fileValues);
      }
    } catch (error) {}
  };

  const handleChange = (rowIndex: number, key: string, value: string) => {
    setFileData((prevData) =>
      prevData.map((row, idx) =>
        idx === rowIndex ? { ...row, [key]: value, is_used: false } : row
      )
    );

    if (fileData[rowIndex]?.is_used) {
      const rowId = fileData[rowIndex]?.dataIdDrCode;
      console.log("log:: row index", rowId);
      setDataUnused(rowId);
    }
  };

  function updateMainObjectWithFlatTypes(mainObject, flatData) {
    Object.entries(flatData).forEach(([key, newType]) => {
      // Match format: stepN_section_propertyName
      const match = key.match(/^step\d+_(\w+)_(.+)$/);
      if (!match) return;

      const [, section, propName] = match;

      if (
        mainObject.types &&
        mainObject.types[section] &&
        mainObject.types[section].properties &&
        mainObject.types[section].properties[propName]
      ) {
        // Convert string "null" to actual null type
        mainObject.types[section].properties[propName].type =
          newType === "null" ? "null" : newType;
      }
    });

    return mainObject;
  }

  const handleDataSource = async () => {
    console.log(fileData, "fileData");
    try {
      setUploading(true);
      let response = null;

      // Utility function to replace "" with null
      const replaceEmptyWithNull = (obj) => {
        const newObj = {};
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            newObj[key] = obj[key] === "" ? null : obj[key];
          }
        }
        return newObj;
      };

      if (searchParms.get("isEdit") === "true") {
        const editData = fileData?.map((item) => {
          const { dataIdDrCode, ...rest } = item;

          return {
            id: dataIdDrCode,
            data_values: replaceEmptyWithNull(rest),
          };
        });

        response = await axios.put(
          `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.INSERT_CSV_DATA}`,
          {
            dataEntries: editData,
            entity_id: id,
            entity_type: entity_type,
          }
        );
      } else {
        const API_URL = isReupload
          ? `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.INSERT_CSV_DATA}?reupload=true`
          : `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.INSERT_CSV_DATA}?reupload=false`;

        const updatedFileData = fileData.map((item) =>
          replaceEmptyWithNull(item)
        );

        const api_steps = JSON.parse(
          localStorage.getItem(`${id}_stepsArray`) ?? "[]"
        );

        const updatedStep = api_steps.map((step: any) =>
          updateMainObjectWithFlatTypes(step, keyTypes)
        );

        const updatedKeyTypes = updatedStep.map((step) => step.types);

        response = await axios.post(API_URL, {
          entity_id: id,
          entity_type: entity_type,
          dataEntries: updatedFileData,
          isUnique: uniqueData,
          typeInfo: updatedKeyTypes,
        });
      }

      if (response.data.success) {
        localStorage.removeItem("csvData");
        localStorage.removeItem("csvUniqueHeaders");
        localStorage.removeItem("csvKeyTypes");
        localStorage.removeItem(`${id}_stepsArray`);
        localStorage.removeItem(`${id}_typesArray`);
        localStorage.removeItem(`${id}_csvKeyTypes`);

        if (searchParms.get("isEdit") === "true") {
          setUploading(false);
          router.back();
          showToast("Data uploaded successfully", "success");
          return;
        }

        showToast("Data uploaded successfully", "success");

        const analyzeUrl = params.testSuitId
          ? GLOBAL_API_ROUTES.ANALYZE_CSV_DATA
          : GLOBAL_API_ROUTES.ANALYZE_CSV_DATA_E2E;

        console.log("log:: analyzeUrl", analyzeUrl);

        const analyseData = await axios.post(
          `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${analyzeUrl}/${id}`
        );

        if (analyseData.data.success) {
          router.back();
        }
      }
    } catch (error) {
      console.error("Error uploading data:", error);
      if (error?.response?.data?.currentStatus === "READY") {
        router.back();
        return;
      }
      showToast("Error uploading data", "error");
      setUploading(false);
    }
  };

  const handleConnectWithDataSource = async (toast = true) => {
    const connectUrl = params.testSuitId
      ? GLOBAL_API_ROUTES.CONNECT_WITH_DATA_SOURCE
      : GLOBAL_API_ROUTES.CONNECT_WITH_DATA_SOURCE_E2E;
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${connectUrl}/${id}/apply`
      );

      return response.data;
    } catch (error) {
      console.error(
        "Error connecting with data source:",
        error?.response?.data?.message
      );
      // toast &&
      //   showToast(
      //     error?.response?.data?.message ||
      //       "Failed to connect with data source.",
      //     "error"
      //   );
    }
  };

  const checkAnalyseStatus = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.ANALYZE_STATUS_CSV_DATA}/${id}`
      );
      // console.log("log:: response", response.data);

      // if (response.data.data?.status === "READY") {
      //   router.back();
      // }
      return response.data;
    } catch (error) {}
  };

  const checkAnalyseStatusE2E = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.DATA_SOURCE_STATUS_E2E}/${id}`
      );
      // console.log("log:: response", response.data);

      // if (response.data.data?.status === "READY") {
      //   router.back();
      // }
      return response.data;
    } catch (error) {}
  };

  const checkDataSourceAvailability = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.DATA_SOURCE_AVAILABILITY}/${id}?type=${entity_type}`
      );
      return response.data;
    } catch (error) {
      console.error("Error checking data source availability:", error);
    }
  };

  const handleDataUsage = (isUsed: boolean, id: number) => {
    if (isUsed) {
      setDataUsed(id);
      setFileData((prevData) =>
        prevData.map((row) =>
          row.dataIdDrCode === id ? { ...row, is_used: true } : row
        )
      );
    } else {
      setDataUnused(id);
      setFileData((prevData) =>
        prevData.map((row) =>
          row.dataIdDrCode === id ? { ...row, is_used: false } : row
        )
      );
    }
  };

  const setDataUsed = async (id: number) => {
    try {
      const response = await axios.put(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.MARK_DATA_USED}/${id}`
      );

      if (response.data.success) {
        showToast("Data marked as used", "success");
      }
    } catch (error) {}
  };

  const setDataUnused = async (id: number) => {
    try {
      const response = await axios.put(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.MARK_DATA_UNUSED}/${id}`
      );

      if (response.data.success) {
        showToast("Data marked as unused", "success");
      }
    } catch (error) {}
  };

  const resetDataUsage = async () => {
    try {
      const response = await axios.put(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.RESET_DATA_SOURCE_USAGE}/${id}?type=${entity_type}`
      );

      if (response.data.success) {
        fetchDataSource();
        showToast("Data marked as unused", "success");
      }
    } catch (error) {}
  };

  useEffect(() => {
    setLoading(true);

    if (searchParms.get("isEdit") === "true") {
      fetchDataSource();
    } else {
      const data = JSON.parse(localStorage.getItem("csvData") ?? "[]");
      const unique_data = JSON.parse(
        localStorage.getItem("csvUniqueHeaders") ?? "[]"
      );
      // const keyTypes_data = JSON.parse(localStorage.getItem("csvKeyTypes") ?? "{}");
      const keyTypes_data = JSON.parse(
        localStorage.getItem(`${id}_csvKeyTypes`) ?? "{}"
      );

      setKeyTypes(keyTypes_data);
      setUniqueData(unique_data);
      setFileData(data);
    }

    setLoading(false);

    return () => {
      if (intervalIdRef.current) {
        clearInterval(intervalIdRef.current);
      }
    };
  }, [searchParms]);

  const headers =
    fileData?.length > 0 &&
    Object.keys(fileData?.[0])
      ?.filter((item) => item !== "dataIdDrCode")
      ?.sort((a, b) => {
        if (a === "is_used") return -1;
        if (b === "is_used") return 1;
        return 0;
      });

  return {
    fileData,
    headers,
    setFileData,
    uniqueData,
    setUniqueData,
    loading,
    uploading,
    setUploading,
    handleChange,
    handleDataSource,
    handleDataUsage,
    setReuploadModal,
    reuploadModal,
    setDataUsed,
    setDataUnused,
    fetchDataSource,
    resetDataUsage,
    setIsReupload,
    handleConnectWithDataSource,
    checkAnalyseStatus,
    checkAnalyseStatusE2E,
    checkDataSourceAvailability,
  };
};

export default useDataSource;
