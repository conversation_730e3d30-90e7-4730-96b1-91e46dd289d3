"use client";
import { Checkbox } from "@mui/material";

import React from "react";
import PrimaryInput from "../common/primary-input/PrimaryInput";
import FileMappingModal from "@/app/project/[projectId]/group/[groupId]/testSuit/[testSuitId]/file-mapping-model/FileMappingModal";
import useDataSource from "./useDataSource";
import DataSourceUploading from "./DataSourceUploading";
import BottomSheet from "../common/right-bottom-sheet/RightBottomSheet";
import { CustomCheckedIcon, CustomIcon, CustomIndeterminateIcon } from "@/temp-utils/stylesIcons";

const DataSourceComponent = () => {
  const {
    headers,
    fileData,
    loading,
    reuploadModal,
    uploading,
    handleChange,
    handleDataSource,
    handleDataUsage,
    setReuploadModal,
    resetDataUsage,
    setIsReupload,
  } = useDataSource();

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <svg
          className="animate-spin h-5 w-5 text-white"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            fill="none"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 1 1 16 0A8 8 0 0 1 4 12z"
          />
        </svg>
      </div>
    );
  }

  if (!fileData || fileData.length === 0) {
    return <div className="p-10 text-white">No data found</div>;
  }

  if (uploading) {
    return <DataSourceUploading />;
  }

  return (
    <div>
      <div className="h-[calc(100vh-200px)] overflow-y-auto">
        <table className="min-w-full  text-white   rounded-lg overflow-auto">
          <thead>
            <tr className="border-b-1 border-gray-600">
              {headers.map((header) => (
                <th
                  key={header}
                  className="p-1 text-left text-sm text-[#B2B2C1]"
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {fileData.map((row, rowIndex) => (
              <tr key={rowIndex} className="">
                {headers.map((key) => (
                  <td key={key} className="p-1 py-2 border-b border-gray-700">
                    {key === "is_used" ? (
                      <>
                        <Checkbox
                          checked={row[key]}
                          onChange={(e) =>
                            handleDataUsage(e.target.checked, row.dataIdDrCode)
                          }
                          icon={<CustomIcon />}
                          checkedIcon={
                            <CustomCheckedIcon>
                              <svg className="font-bold" viewBox="0 0 24 24">
                                <path
                                  d="M9 16.2l-4.2-4.2L4 13l5 5 12-12-1.4-1.4z"
                                  fill="#FFFFFF"
                                />
                              </svg>
                            </CustomCheckedIcon>
                          }
                          indeterminateIcon={<CustomIndeterminateIcon />}
                        />
                      </>
                    ) : (
                      <PrimaryInput
                        type="text"
                        value={row[key]}
                        onChange={(e) =>
                          handleChange(rowIndex, key, e.target.value)
                        }
                        className="bg-transparent border border-gray-500 text-white px-2 py-1 rounded w-full text-sm focus:outline-none focus:ring-1 focus:ring-blue-400 min-w-[170px]"
                        placeholder={key}
                      />
                    )}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="flex justify-end items-center gap-4 mt-auto border-t-2 border-drcodeBlue pt-4">
        {/* <button
          className="button--outlined-primary py-2 px-8 text-[14px]"
          style={{
            fontSize: "14px",
          }}
          onClick={resetDataUsage}
        >
          Reset data usage{" "}
        </button> */}
        <button
          className="button--outlined-primary py-2 px-8 text-[14px]"
          style={{
            fontSize: "14px",
          }}
          onClick={() => {
            setIsReupload(true);
            setReuploadModal(true);
          }}
        >
          Re upload
        </button>
        <button
          onClick={handleDataSource}
          className="button--primary py-2 px-8 text-[14px]"
          style={{
            fontSize: "14px",
          }}
        >
          Save and continue
        </button>
      </div>

      {/* {reuploadModal && (
        <FileMappingModal
          isOpen={reuploadModal}
          handleClose={() => {
            setIsReupload(false);
            setReuploadModal(false);
          }}
          setModalOpen={setReuploadModal}
          isEdit={true}
        />
      )} */}
            {reuploadModal && (
        <BottomSheet
          isOpen={reuploadModal}
          onClose={() => {
            setReuploadModal(false);
          }}
        >
          <FileMappingModal
            isOpen={reuploadModal}
            handleClose={() => {
              setIsReupload(false);
              setReuploadModal(false);
            }}
            setModalOpen={setReuploadModal}
            isEdit={true}
          />
        </BottomSheet>
      )}
    </div>
  );
};

export default DataSourceComponent;

// "use client";

// import React, { useMemo, useState } from "react";
// import {
//   useReactTable,
//   getCoreRowModel,
//   flexRender,
//   ColumnDef,
// } from "@tanstack/react-table";
// import { Checkbox } from "@mui/material";
// import {
//   CustomCheckedIcon,
//   CustomIcon,
//   CustomIndeterminateIcon,
// } from "@/app/project/[projectId]/page";
// import PrimaryInput from "../common/primary-input/PrimaryInput";
// import FileMappingModal from "@/app/project/[projectId]/group/[groupId]/testSuit/[testSuitId]/file-mapping-model/FileMappingModal";
// import useDataSource from "./useDataSource";
// import DataSourceUploading from "./DataSourceUploading";
// import BottomSheet from "../common/right-bottom-sheet/RightBottomSheet";

// const DataSourceComponent = () => {
//   const {
//     headers,
//     fileData,
//     loading,
//     reuploadModal,
//     uploading,
//     handleChange,
//     handleDataSource,
//     handleDataUsage,
//     setReuploadModal,
//     resetDataUsage,
//     setIsReupload,
//     setFileData,
//   } = useDataSource();

//   const [columnWidths, setColumnWidths] = useState({});

//   const columns = useMemo<ColumnDef<any>[]>(
//     () =>
//       headers
//         ? headers?.map((key) => ({
//             accessorKey: key,
//             header: () => key,
//             size: 150,
//             cell: ({ row, getValue }) => {
//               const value = getValue();
//               const rowIndex = row.index;

//               if (key === "is_used") {
//                 return (
//                   <Checkbox
//                     checked={value as boolean}
//                     onChange={(e) =>
//                       handleDataUsage(
//                         e.target.checked,
//                         row.original.dataIdDrCode
//                       )
//                     }
//                     icon={<CustomIcon />}
//                     checkedIcon={
//                       <CustomCheckedIcon>
//                         <svg className="font-bold" viewBox="0 0 24 24">
//                           <path
//                             d="M9 16.2l-4.2-4.2L4 13l5 5 12-12-1.4-1.4z"
//                             fill="#FFFFFF"
//                           />
//                         </svg>
//                       </CustomCheckedIcon>
//                     }
//                     indeterminateIcon={<CustomIndeterminateIcon />}
//                   />
//                 );
//               }

//               return (
//                 <PrimaryInput
//                   type="text"
//                   value={value as string | number}
//                   onChange={(e) => handleChange(rowIndex, key, e.target.value)}
//                   className="bg-transparent border border-gray-500 text-white px-2 py-1 rounded w-full text-sm focus:outline-none focus:ring-1 focus:ring-blue-400"
//                   placeholder={key}
//                 />
//               );
//             },
//           }))
//         : [],
//     [headers, fileData]
//   );

//   console.log("log:: file data is ",fileData,columns)

//   const table = useReactTable({
//     data: fileData,
//     columns,
//     getCoreRowModel: getCoreRowModel(),
//     columnResizeMode: "onChange",
//     enableColumnResizing: true,
//     state: {
//       columnSizing: columnWidths,
//     },
//     onColumnSizingChange: setColumnWidths,
//   });

//   if (loading) {
//     return (
//       <div className="flex justify-center items-center h-screen">
//         <svg
//           className="animate-spin h-5 w-5 text-white"
//           xmlns="http://www.w3.org/2000/svg"
//           viewBox="0 0 24 24"
//         >
//           <circle
//             className="opacity-25"
//             cx="12"
//             cy="12"
//             r="10"
//             fill="none"
//             stroke="currentColor"
//             strokeWidth="4"
//           />
//           <path
//             className="opacity-75"
//             fill="currentColor"
//             d="M4 12a8 8 0 1 1 16 0A8 8 0 0 1 4 12z"
//           />
//         </svg>
//       </div>
//     );
//   }

//   if (!fileData || fileData.length === 0) {
//     return <div className="p-10 text-white">No data found</div>;
//   }

//   if (uploading) {
//     return <DataSourceUploading />;
//   }

//   return (
//     <div>
//       <div className="h-[calc(100vh-200px)] overflow-y-auto">
//         <table className="min-w-full text-white rounded-lg">
//           <thead>
//             {table.getHeaderGroups().map((headerGroup) => (
//               <tr key={headerGroup.id} className="border-b border-gray-600">
//                 {headerGroup.headers.map((header) => (
//                   <th
//                     key={header.id}
//                     style={{ width: header.getSize(), position: "relative" }}
//                     className="text-left text-sm text-[#B2B2C1] p-1"
//                   >
//                     {flexRender(
//                       header.column.columnDef.header,
//                       header.getContext()
//                     )}
//                     {header.column.getCanResize() && (
//                       <div
//                         onMouseDown={header.getResizeHandler()}
//                         onTouchStart={header.getResizeHandler()}
//                         className="absolute right-0 top-0 h-full w-1 cursor-col-resize bg-gray-700 opacity-40"
//                       />
//                     )}
//                   </th>
//                 ))}
//               </tr>
//             ))}
//           </thead>
//           <tbody>
//             {table.getRowModel().rows.map((row) => (
//               <tr key={row.id}>
//                 {row.getVisibleCells().map((cell) => (
//                   <td
//                     key={cell.id}
//                     className="p-1 py-2 border-b border-gray-700"
//                     style={{ width: cell.column.getSize() }}
//                   >
//                     {flexRender(cell.column.columnDef.cell, cell.getContext())}
//                   </td>
//                 ))}
//               </tr>
//             ))}
//           </tbody>
//         </table>
//       </div>

//       <div className="flex justify-end items-center gap-4 mt-auto border-t-2 border-drcodeBlue pt-4">
//         <button
//           className="button--outlined-primary py-2 px-8 text-[14px]"
//           onClick={() => {
//             setIsReupload(true);
//             setReuploadModal(true);
//           }}
//         >
//           Re upload
//         </button>
//         <button
//           onClick={handleDataSource}
//           className="button--primary py-2 px-8 text-[14px]"
//         >
//           Save and continue
//         </button>
//       </div>

      // {reuploadModal && (
      //   <BottomSheet
      //     isOpen={reuploadModal}
      //     onClose={() => {
      //       setReuploadModal(false);
      //     }}
      //   >
      //     <FileMappingModal
      //       isOpen={reuploadModal}
      //       handleClose={() => {
      //         setIsReupload(false);
      //         setReuploadModal(false);
      //       }}
      //       setModalOpen={setReuploadModal}
      //       isEdit={true}
      //     />
      //   </BottomSheet>
      // )}
//     </div>
//   );
// };

// export default DataSourceComponent;
