import CustomSelect from "@/components/common/custom-select/CustomSelect";
import { Metric, TimeRange, timeRangeOptions } from "@/hooks/useAnalytics";
import { useEffect, useRef, useState } from "react";
import { FaCheckCircle } from "react-icons/fa";
import { FiXCircle } from "react-icons/fi";
import { RiLoader2Fill } from "react-icons/ri";

export interface FlowEntry {
  name: string;
  flakiness: string; // percentage as string e.g. "60%"
  status: Array<"success" | "fail">;
  tag: string;
}

export interface FlowsData {
  [key: string]: FlowEntry[];
}

const StatusIcon = ({ type }: { type: "success" | "fail" }) => {
  return type === "success" ? (
    <FaCheckCircle className="text-emerald-400 h-4 w-4" />
  ) : (
    <FiXCircle className="text-rose-400 h-4 w-4" />
  );
};

const FlakyFlows = ({ data, fetchMetricData, loading }: { data: FlowsData, fetchMetricData: (data: { timeRange: TimeRange; metric: Metric }) => void , loading: boolean}) => {
  const filters = timeRangeOptions
  const [selected, setSelected] = useState(filters?.[0]?.value);
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    fetchMetricData({
      timeRange: selected,
      metric: Metric.FLAKY_FLOW,
    });
  }, [selected]);

  if (!data) {
    return <div>No Data</div>;
  }

  return (
    <div className="relative h-full">
      {loading && (
        <div className="absolute top-0 left-0 right-0 bottom-0 bg-black/50 flex items-center justify-center z-10">
          <RiLoader2Fill className="animate-spin text-white text-xl" />
        </div>
      )}
      <div className="flex justify-between items-center my-2">
        <div className="">Top 5 Flaky flows</div>
        <div className="">
          <CustomSelect
            options={filters}
            value={selected}
            onChange={(value) => setSelected(value as TimeRange)}
          />
        </div>
      </div>

      {data?.[selected]?.length ? (
        <div className="overflow-x-auto max-h-[300px] overflow-y-auto rounded-md">
          <table className="min-w-full text-sm text-white">
            <thead className="text-left border-b border-[#1e1e35] text-gray-300">
              <tr>
                <th className="px-4 py-3">Flow</th>
                <th className="px-4 py-3">Flakiness %</th>
                <th className="px-4 py-3">Last Run Status</th>
                <th className="px-4 py-3">Stability Tag</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-[#1e1e35]">
              {data[selected]?.map((flow, index) => (
                <tr
                  key={index}
                  className={`${index === 0 ? "bg-[#16153b]" : ""
                    } hover:bg-[#14132f] transition`}
                >
                  <td className="px-4 py-3">{flow.name}</td>
                  <td className="px-4 py-3 text-gray-300">{flow.flakiness}</td>
                  <td className="px-4 py-3">
                    <div className="flex gap-1">
                      {flow.status.map((s, i) => (
                        <StatusIcon key={i} type={s as "success" | "fail"} />
                      ))}
                    </div>
                  </td>
                  <td className="px-4 py-3 text-gray-300">{flow.tag}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="h-full flex items-center justify-center">
          Not enough data to show
        </div>
      )}
    </div>
  );
};

export default FlakyFlows;
