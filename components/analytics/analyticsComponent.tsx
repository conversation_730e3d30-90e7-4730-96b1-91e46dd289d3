"use client";
import useAnalytics, { Metric } from "@/hooks/useAnalytics";
import { useParams } from "next/navigation";
import React, { useEffect } from "react";
import ApiPerformance from "./apiPerformance";
import TotalFlowVsPassed from "./totalFlowVsPassed";
import IssuesIdentified from "./issuesIdentified";
import FlakyFlows from "./flakyFlows";
import TotalStepsVsPassed from "./totalStepsVsPassed";
import BottomApis from "./bottomApis";
import BottomFlows from "./bottomFlows";
import LoaderGif from "../common/loader-gif/LoaderGif";


export default function AnalyticsComponent() {
  const { projectId } = useParams();

  const {
    fetchAnalyticsData,
    totalFlowsRunVsPassedData,
    slowestFlowsData,
    slowestApisData,
    totalStepsRunVsPassedData,
    flakyFlowsData,
    issuesIdentifiedData,
    apiPerformanceData,
    loading,
    fetchMetricData,
    metricLoadingObject,
  } = useAnalytics(projectId as string);

  useEffect(() => {
    if (!projectId) return;
    fetchAnalyticsData({ timeSeriesFilter: '24h' });
  }, [projectId]);

  return (
    <div className="">
      {loading ? (
        <div className="flex justify-center items-center h-[70vh]"><LoaderGif /></div>
      ) : (
        <>
          <div className="flex gap-3 p-3 justify-between   my-1.5">
            <div className="w-[100%] bg-[#11112C] rounded-md p-4 max-h-[700px]">
              {apiPerformanceData ? (
                <ApiPerformance data={apiPerformanceData} fetchMetricData={fetchMetricData} loading={metricLoadingObject[Metric.API_PERFORMANCE]} />
              ) : (
                <div>No Data</div>
              )}
            </div>
          </div>
          <div className="flex gap-3 p-3 justify-between   my-1.5">
            <div className="w-[50%] bg-[#11112C] rounded-md p-4 max-h-[700px]">
              {totalFlowsRunVsPassedData ? (
                <TotalFlowVsPassed data={totalFlowsRunVsPassedData} fetchMetricData={fetchMetricData} loading={metricLoadingObject[Metric.TOTAL_FLOW_RUN_VS_PASSED]} />
              ) : (
                <div>No Data</div>
              )}
            </div>
            <div className="w-[50%] bg-[#11112C] rounded-md p-4 max-h-[700px]">
              {issuesIdentifiedData ? (
                <IssuesIdentified data={issuesIdentifiedData} fetchMetricData={fetchMetricData} loading={metricLoadingObject[Metric.ISSUES_IDENTIFIED]} />
              ) : (
                <div>No Data</div>
              )}
            </div>
          </div>
          <div className="flex gap-3 p-3 justify-between   my-1.5">
            <div className="w-[50%] bg-[#11112C] rounded-md p-4 max-h-[700px]">
              {flakyFlowsData ? (
                <FlakyFlows data={flakyFlowsData} fetchMetricData={fetchMetricData} loading={metricLoadingObject[Metric.FLAKY_FLOW]} />
              ) : (
                <div>No Data</div>
              )}
            </div>
            <div className="w-[50%] bg-[#11112C] rounded-md p-4 max-h-[700px]">
              {totalStepsRunVsPassedData ? (
                <TotalStepsVsPassed data={totalStepsRunVsPassedData} fetchMetricData={fetchMetricData} loading={metricLoadingObject[Metric.TOTAL_STEP_RUN_VS_PASSED]} />
              ) : (
                <div>No Data</div>
              )}
            </div>
          </div>
          <div className="flex gap-3 p-3 justify-between   my-1.5">
            <div className="w-[50%] bg-[#11112C] rounded-md p-4 max-h-[700px]">
              {slowestApisData ? (
                <BottomApis data={slowestApisData} fetchMetricData={fetchMetricData} loading={metricLoadingObject[Metric.SLOWEST_API]} />
              ) : (
                <div>No Data</div>
              )}
            </div>
            <div className="w-[50%] bg-[#11112C] rounded-md p-4 max-h-[700px]">
              {slowestFlowsData ? (
                <BottomFlows data={slowestFlowsData} fetchMetricData={fetchMetricData} loading={metricLoadingObject[Metric.SLOWEST_FLOW]} />
              ) : (
                <div>No Data</div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
}