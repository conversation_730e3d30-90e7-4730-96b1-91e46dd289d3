import CustomBarChart from "@/components/common/charts/customBarChart";
import CustomSelect from "@/components/common/custom-select/CustomSelect";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { ChartDataMap } from "./totalFlowVsPassed";
import { Metric, TimeRange, timeRangeOptions } from "@/hooks/useAnalytics";
import { RiLoader2Fill } from "react-icons/ri";

const BottomFlows = ({ data, fetchMetricData, loading }: { data: ChartDataMap, fetchMetricData: (data: { timeRange: TimeRange; metric: Metric }) => void, loading: boolean }) => {
  const filters = timeRangeOptions
  const [selected, setSelected] = useState(filters?.[0]?.value);
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    fetchMetricData({
      timeRange: selected,
      metric: Metric.SLOWEST_FLOW,
    });
  }, [selected]);

  if (!data) {
    return <div>No Data</div>;
  }


  const sortedChartData = useMemo(() => {
    if (!data) return {};

    return Object.fromEntries(
      Object.entries(data).map(([key, value]) => {
        const sortedDatasets = [...(value.datasets || [])].sort((a, b) => {
          const sumA = a.data.reduce((acc, val) => acc + val, 0);
          const sumB = b.data.reduce((acc, val) => acc + val, 0);
          return sumB - sumA;
        }).slice(0, 5);

        return [key, { ...value, datasets: sortedDatasets }];
      })
    );
  }, [data]);

  return (
    <div className="relative h-full">
      {loading && (
        <div className="absolute top-0 left-0 right-0 bottom-0 bg-black/50 flex items-center justify-center z-10">
          <RiLoader2Fill className="animate-spin text-white text-xl" />
        </div>
      )}
      <div className="flex justify-between items-center my-2">
        <div className="">Least performant Flows</div>
        <div className="">
          <CustomSelect
            options={filters}
            value={selected}
            onChange={(value) => setSelected(value as TimeRange)}
          />
        </div>
      </div>

      {/* <MultiAxisLineChart dataMap={data} selectedFilter={selected} /> */}
      <CustomBarChart dataMap={sortedChartData?.[selected]} />
    </div>
  );
};

export default BottomFlows;
