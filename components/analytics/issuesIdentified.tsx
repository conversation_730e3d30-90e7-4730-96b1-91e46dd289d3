import Accordion from "@/components/common/accordion/Accordion";
import CustomSelect from "@/components/common/custom-select/CustomSelect";
import { Metric, TimeRange, timeRangeOptions } from "@/hooks/useAnalytics";
import Link from "next/link";
import React, { useEffect, useRef, useState } from "react";
import { RiLoader2Fill } from "react-icons/ri";

interface IssuesIdentifiedData {
  id: number;
  category: string;
  description: string;
  count: number;
  apis: {
    id: number;
    name: string;
    url: string;
  }[];
}

interface IssuesIdentifiedProps {
  data: { [key: string]: IssuesIdentifiedData[] };
  fetchMetricData: (data: { timeRange: TimeRange; metric: Metric }) => void;
  loading: boolean
}

const IssuesIdentified = ({ data, fetchMetricData, loading }: IssuesIdentifiedProps) => {
  const filters = timeRangeOptions
  const [selected, setSelected] = useState(filters?.[0]?.value);
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    fetchMetricData({
      timeRange: selected,
      metric: Metric.ISSUES_IDENTIFIED,
    });
  }, [selected]);

  return (
    <div className="relative h-full">
      {loading && (
        <div className="absolute top-0 left-0 right-0 bottom-0 bg-black/50 flex items-center justify-center z-10">
          <RiLoader2Fill className="animate-spin text-white text-xl" />
        </div>
      )}
      <div className="flex justify-between items-center my-2">
        <div className="">Issues Identified</div>
        <div className="">
          <CustomSelect
            options={filters}
            value={selected}
            onChange={(value) => setSelected(value as TimeRange)}
          />
        </div>
      </div>

      {data?.[selected]?.length ? (
        <Accordion
          items={data?.[selected]?.map((item, idx) => {
            return {
              id: idx?.toString(),
              title: item?.category,
              content: (
                <ul className="max-h-[300px] overflow-y-auto">
                  {item?.apis?.map((api) => {
                    return (
                      <li className="list-disc">
                        <Link
                          href={`/${api?.url}`}
                          target="_blank"
                          key={api?.id}
                          className="text-xs my-2 block"
                        >
                          {api?.name}
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              ),
              statusColor: "",
            };
          })}
        />
      ) : (
        <div className="h-full flex items-center justify-center">
          Not enough data to show
        </div>
      )}
    </div>
  );
};

export default IssuesIdentified;
