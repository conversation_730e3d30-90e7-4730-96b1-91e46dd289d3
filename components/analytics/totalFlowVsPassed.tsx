import CustomBarChart from "@/components/common/charts/customBarChart";
import MultiAxisLineChart from "@/components/common/charts/multiAxisLineChart";
import CustomSelect from "@/components/common/custom-select/CustomSelect";
import { Metric, TimeRange, timeRangeOptions } from "@/hooks/useAnalytics";
import React, { useEffect, useRef, useState } from "react";
import { RiLoader2Fill } from "react-icons/ri";

export interface ChartDataset {
  label: string;
  data: number[];
  borderColor: string;
  backgroundColor: string;
}

export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface ChartDataMap {
  [key: string]: ChartData;
}

const TotalFlowVsPassed = ({ data, fetchMetricData, loading }: { data: ChartDataMap, fetchMetricData: (data: { timeRange: TimeRange; metric: Metric }) => void, loading: boolean }) => {
  const filters = timeRangeOptions
  const [selected, setSelected] = useState(filters?.[0]?.value);
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    fetchMetricData({
      timeRange: selected,
      metric: Metric.TOTAL_FLOW_RUN_VS_PASSED,
    });
  }, [selected]);

  if (!data) {
    return <div>No Data</div>;
  }

  return (
    <div className="relative h-full">
      {loading && (
        <div className="absolute top-0 left-0 right-0 bottom-0 bg-black/50 flex items-center justify-center z-10">
          <RiLoader2Fill className="animate-spin text-white text-xl" />
        </div>
      )}
      <div className="flex justify-between gap-2 items-center my-2">
        <div className="">Total Flows run vs Total Flows passed</div>
        <div className="">
          <CustomSelect
            options={filters}
            value={selected}
            onChange={(value) => setSelected(value as TimeRange)}
          />
        </div>
      </div>

      <CustomBarChart dataMap={data?.[selected]} />
    </div>
  );
};

export default TotalFlowVsPassed;
