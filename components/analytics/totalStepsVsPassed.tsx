import MultiAxisLineChart from "@/components/common/charts/multiAxisLineChart";
import CustomSelect from "@/components/common/custom-select/CustomSelect";
import React, { useEffect, useRef, useState } from "react";
import { ChartDataMap } from "./totalFlowVsPassed";
import CustomBarChart from "@/components/common/charts/customBarChart";
import { Metric, TimeRange, timeRangeOptions } from "@/hooks/useAnalytics";
import { RiLoader2Fill } from "react-icons/ri";

const TotalStepsVsPassed = ({ data, fetchMetricData, loading }: { data: ChartDataMap, fetchMetricData: (data: { timeRange: TimeRange; metric: Metric }) => void, loading: boolean }) => {
  const filters = timeRangeOptions
  const [selected, setSelected] = useState(filters?.[0]?.value);
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    fetchMetricData({
      timeRange: selected,
      metric: Metric.TOTAL_STEP_RUN_VS_PASSED,
    });
  }, [selected]);

  if (!data) {
    return <div>No Data</div>;
  }

  return (
    <div className="relative h-full">
    {loading && (
      <div className="absolute top-0 left-0 right-0 bottom-0 bg-black/50 flex items-center justify-center z-10">
        <RiLoader2Fill className="animate-spin text-white text-xl" />
      </div>
    )}
      <div className="flex justify-between items-center my-2">
        <div className="">Total APIs run vs Total APIs passed</div>
        <div className="">
          <CustomSelect
            options={filters}
            value={selected}
            onChange={(value) => setSelected(value as TimeRange)}
          />
        </div>
      </div>

      {/* <MultiAxisLineChart dataMap={data} selectedFilter={selected} /> */}
      <CustomBarChart dataMap={data?.[selected]} />
    </div>
  );
};

export default TotalStepsVsPassed;
