"use client";
import CustomSelect from "@/components/common/custom-select/CustomSelect";
import { Metric, TimeRange, timeRangeOptions } from "@/hooks/useAnalytics";
import React, { useEffect, useRef, useState } from "react";
import { RiLoader2Fill } from "react-icons/ri";

interface ApiData {
  name: string;
  avg_response_time: number;
  cutoff_time: number;
  percentage: number;
}

interface Props {
  data: {
    [key: string]: ApiData[];
  };
  fetchMetricData: (data: { timeRange: TimeRange; metric: Metric }) => void;
  loading: boolean;
}

const getBarColor = (percentage: number): string => {
  if (percentage < 50) return "bg-green-500";
  if (percentage === 50) return "bg-yellow-500";
  return "bg-red-500";
};

const ApiPerformance: React.FC<Props> = ({ data, fetchMetricData, loading }) => {
  const filters = timeRangeOptions
  const [selected, setSelected] = useState(filters?.[0]?.value);
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    fetchMetricData({
      timeRange: selected,
      metric: Metric.API_PERFORMANCE,
    });
  }, [selected]);


  return (
    <div className="relative h-full">
      {loading && (
        <div className="absolute top-0 left-0 right-0 bottom-0 bg-black/50 flex items-center justify-center z-10">
          <RiLoader2Fill className="animate-spin text-white text-xl" />
        </div>
      )}
      <div className="flex justify-between items-center my-2">
        <div className="">API Performance</div>
        <div className="">
          <CustomSelect
            options={filters}
            value={selected}
            onChange={(value) => setSelected(value as TimeRange)}
          />
        </div>
      </div>
      <div className="overflow-x-auto max-h-[300px] overflow-y-auto rounded-md">
        {data?.[selected]?.length ? (
          <table className="w-full text-sm text-white table-fixed">
            <thead className="text-left border-b border-[#1e1e35] text-gray-300 bg-[#0D0C24]">
              <tr>
                <th className="px-3 py-2 w-[40%] truncate">API Name</th>
                <th className="px-3 py-2 w-[20%]">Avg Response Time</th>
                <th className="px-3 py-2 w-[20%]">Cutoff Time</th>
                <th className="px-3 py-2 w-[20%]">Slowness</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-[#1e1e35] bg-[#0D0C24]">
              {data?.[selected]?.map((api, idx) => (
                <tr
                  key={idx}
                  className={`${idx === 0 ? "bg-[#16153b]" : ""
                    } hover:bg-[#14132f] transition`}
                >
                  <td className="px-3 py-2 w-[40%] truncate max-w-[300px]">
                    {api.name}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap">
                    {api.avg_response_time.toFixed(2)} ms
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap">
                    {api.cutoff_time} ms
                  </td>
                  <td className="px-3 py-2">
                    <div className="flex items-center gap-2">
                      <div className="w-full h-2 bg-gray-800 rounded">
                        <div
                          className={`h-2 rounded ${getBarColor(
                            api.percentage
                          )}`}
                          style={{ width: `${api.percentage}%` }}
                        />
                      </div>
                      <span className="text-xs whitespace-nowrap">
                        {api.percentage}%
                      </span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div className="p-4 h-full flex items-center justify-center">
            Not enough data to show
          </div>
        )}
      </div>
    </div>
  );
};

export default ApiPerformance;
