"use client";

import React, { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
const drcode_flow_message_box_right = "../../drcode_flow_message_box_right.svg";
const drcode_flow_message_box_left = "../../drcode_flow_message_box_left.svg";
import "./FlowStep.css";
import { motion } from "framer-motion"
import LogoWhite from "../../public/LogoWhite.svg";
import Image from "next/image";
interface FlowStepProps {
    heading: string;
    personHeading: string;
    personContent: string;
    personTime: string;
    drcodeMessage: string;
    drcodeTime: string;
    employee_img: string;
    index: number;
}

const FlowStep: React.FC<FlowStepProps> = ({
    heading, personHeading, personContent, personTime,
    drcodeMessage, drcodeTime, employee_img, index
}) => {
    const router = useRouter();

    const convertToPixels = (value:number, unit:string) => {
        if (unit === "vw") {
            return (window.innerWidth * value) / 100;
        } else if (unit === "rem") {
            const fontSize = parseFloat(getComputedStyle(document.documentElement).fontSize);
            return fontSize * value;
        }
        return value;
    };

    const fadeIn = ( direction:string, type: any, delay: any, duration: any) => {
        return {
            hidden: {
                x: direction==="right"? convertToPixels(10,"vw"):convertToPixels(-10,"vw"),
                opacity: 0,
            },
            show: {
                x: direction==="right"?convertToPixels(5,"vw"):convertToPixels(-2,"vw"),
                opacity: 1,
                transition: {
                    type: type,
                    delay: delay,
                    duration: duration,
                    ease: "easeOut",
                },
            },
        };
    };

    const fadeUp = ( type: any, delay: any, duration: any) => {
        return {
            hidden: {
                y:convertToPixels(10,"vw"),
                opacity: 0,
            },
            show: {
                y:0,
                opacity: 1,
                transition: {
                    type: type,
                    delay: delay,
                    duration: duration,
                    ease: "easeOut",
                },
            },
        };
    };


    return (
        <div className="relative flex flex-col items-center justify-center w-full">
            <p className="absolute text-[120px] font-semibold bg-clip-text text-transparent"
                style={{
                    backgroundImage: 'linear-gradient(360deg, rgba(255, 255, 255, 0.01) 19.79%, rgba(255, 255, 255, 0.05) 73.7%)',
                    WebkitBackgroundClip: 'text',
                    backgroundClip: 'text',
                }}
            >
                {heading}
            </p>
            <div className="absolute top-4">
                {index % 2 !== 0 ? (
                    <div className="flex flex-row items-center gap-20 ">
                        <motion.div initial='hidden'whileInView='show'viewport={{ once: true, amount: 0.25 }} variants={fadeUp("spring", 0.5, 1.5)} className="flex items-center relative">
                            <img
                                src={employee_img}
                                alt="flow_step"
                                className="h-[30rem] w-[26rem]"
                            />
                            <div
                                className="absolute top-[-44px] right-[-28px] mt-4 p-8 border-2 border-[#2E2E60] rounded-[32px] w-[18rem]"
                                style={{
                                    backgroundColor: '#16161CBF',
                                    backdropFilter: 'blur(8px)',
                                }}
                            >
                                <div className="relative flex flex-col justify-center z-10">
                                    <p className="text-[22px] font-[600] text-[#D1D1E3]">{personHeading}</p>
                                    <p className="text-[18px] font-[400] text-[#E2E2ED]">{personContent}</p>
                                    <p className="mt-2 text-[16px] font-400 text-[#E2E2ED]">
                                        Average Time: <span className="text-[#9F7CF9]">{personTime}</span>
                                    </p>
                                </div>
                            </div>
                        </motion.div>
                        <motion.div initial='hidden'whileInView='show'viewport={{ once: true, amount: 0.25 }} variants={fadeIn("right","spring", 1.5, 1.5)} className={`flex items-center relative top-[2.5rem]`}>
                                <img
                                    src={drcode_flow_message_box_right}
                                    alt="DrCode Message on development lifecycle"
                                    className="w-[27rem]"
                                />
                                <div className="absolute inset-0 flex flex-col justify-center p-4 z-10 w-[80%] ml-16">
                                    <div className='flex space-x-2'>
                                        <Image src={LogoWhite} alt="Logo" width={30} height={30} className="" />
                                        <p className="text-[22px] font-semibold text-[#E2E2ED]">DrCode</p>
                                    </div>
                                    <p className="mt-4 text-[18px] font-normal text-[#E2E2ED]">I've got this!</p>
                                    <p className="text-[18px] font-normal text-[#E2E2ED]">{drcodeMessage}</p>
                                    <p className="mt-2 text-[14px] font-400 text-[#E2E2ED]">
                                        Average Time: <span className="text-[#9F7CF9]">{drcodeTime}</span>
                                    </p>
                                </div>
                        </motion.div>
                    </div>

                ) : (
                    <div className="flex flex-row items-center gap-20">
                        <motion.div initial='hidden'whileInView='show'viewport={{ once: true, amount: 0.25 }} variants={fadeIn("left","spring", 1.5, 1.5)} className={`flex items-center relative top-[2.5rem]`}>
                            <img
                                src={drcode_flow_message_box_left}
                                alt="DrCode Message on development lifecycle"
                                className="w-[27rem]"
                            />
                            <div className="absolute inset-0 flex flex-col justify-center p-4 z-10 w-[80%] ml-10">
                                <div className='flex space-x-2'>
                                        <Image src={LogoWhite} alt="Logo" width={30} height={30} className="" />
                                        <p className="text-[22px] font-semibold text-[#E2E2ED]">DrCode</p>
                                </div>
                                <p className="mt-4 text-[18px] font-normal text-[#E2E2ED]">I've got this!</p>
                                <p className="text-[18px] font-normal text-[#E2E2ED]">{drcodeMessage}</p>
                                <p className="mt-2 text-[14px] font-400 text-[#E2E2ED]">
                                    Average Time: <span className="text-[#9F7CF9]">{drcodeTime}</span>
                                </p>
                            </div>
                        </motion.div>
                        <motion.div initial='hidden'whileInView='show'viewport={{ once: true, amount: 0.25 }} variants={fadeUp("spring", 0.5, 1.5)} className="flex items-center relative right-[-8vw]">
                            <img
                                src={employee_img}
                                alt="flow_step"
                                className="h-[30rem] w-[26rem]"
                            />
                            <div className="absolute top-[-44px] right-[-28px] mt-4 p-8 border-2 border-[#2E2E60] rounded-[32px] w-[18rem]"
                                style={{
                                    backgroundColor: '#16161CBF',
                                    backdropFilter: 'blur(8px)',
                                }}
                            >
                                <div className="relative flex flex-col justify-center z-10">
                                    <p className="text-[22px] font-semibold text-[#E2E2ED]">{personHeading}</p>
                                    <p className="text-[18px] font-normal text-[#E2E2ED]">{personContent}</p>
                                    <p className="mt-2 text-[16px] font-400 text-[#E2E2ED]">
                                        Average Time: <span className="text-[#9F7CF9]">{personTime}</span>
                                    </p>
                                </div>
                            </div>
                        </motion.div>
                    </div>
                )}
            </div>
        </div >
    );
};

export default FlowStep;
