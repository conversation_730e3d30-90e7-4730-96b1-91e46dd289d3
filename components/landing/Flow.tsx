"use client"
import React from "react";
import { useRouter } from "next/navigation";
import FlowStep from "./FlowStep";
import FlowStepMobile from "./FlowStepMobile";
const developer_img = "../../developer.png";
const reviewer_img = "../../reviewer.png";
const tester_img = "../../tester.png";
const production_img = "../../production.png";
const FlowStepImg = "../../flowstep.svg";
const FlowStepMb = "../../flowstepmb.svg";
import { motion } from "framer-motion"

const Flow = () => {
    const router = useRouter();
    const convertToPixels = (value: number, unit: string) => {
        if (unit === "vw") {
            return (window.innerWidth * value) / 100;
        } else if (unit === "rem") {
            const fontSize = parseFloat(getComputedStyle(document.documentElement).fontSize);
            return fontSize * value;
        }
        return value;
    };

    const fadeUp = (type: any, delay: any, duration: any) => {
        return {
            hidden: {
                y: convertToPixels(3, "vw"),
                opacity: 0,
            },
            show: {
                y: 0,
                opacity: 1,
                transition: {
                    type: type,
                    delay: delay,
                    duration: duration,
                    ease: "easeOut",
                },
            },
        };
    };

    return (
        <main className="flex flex-col justify-center items-center">
            <motion.div initial='hidden' whileInView='show' viewport={{ once: true, amount: 0.25 }} variants={fadeUp("spring", 0.5, 1.5)} className="text-center pt-12 relative z-10 ">
                <h1 className="text-[35px] md:text-[48px] font-bold text-[#D1D1E3] leading-tight tracking-wide">
                    Tired of doing the manual work?
                </h1>
                <p className="text-[18px] md:text-[24px] mt-1 text-[#B2B2C1] font-400">
                    Let the AI solve manual tasks for you
                    <br />while you work on the deep tech problems

                </p>
                <p className="mt-16 text-[#9F7CF9] text-[16px] font-semibold">Here's how</p>
                <p className="text-[#B2B2C1] text-[16px] font-400">we get there</p>
            </motion.div>

            <div className="hidden md:flex relative flex-col space-y-0 items-center justify-center w-full">
                {/* flow_start_arrow */}
                {/* <svg className="hidden md:block top-0 ml-[2.5rem] w-[20vw] h-[400vh]" viewBox="0 0 323 4371" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M136.5 56.5V300.5C136.5 303.053 136.5 304.33 136.539 305.409C137.671 336.851 162.899 362.079 194.341 363.211C195.42 363.25 196.697 363.25 199.25 363.25V363.25C201.803 363.25 203.08 363.25 204.159 363.289C235.601 364.421 260.829 389.649 261.961 421.091C262 422.17 262 423.447 262 426V1144C262 1172.43 262 1186.64 257.281 1197.82C251.201 1212.23 239.734 1223.7 225.323 1229.78C214.139 1234.5 199.926 1234.5 171.5 1234.5V1234.5C143.074 1234.5 128.861 1234.5 117.677 1239.22C103.266 1245.3 91.7987 1256.77 85.7187 1271.18C81 1282.36 81 1296.57 81 1325V2159C81 2179.93 81 2190.4 83.5836 2198.92C89.4006 2218.09 104.407 2233.1 123.583 2238.92C132.1 2241.5 142.567 2241.5 163.5 2241.5V2241.5C184.433 2241.5 194.9 2241.5 203.417 2244.08C222.593 2249.9 237.599 2264.91 243.416 2284.08C246 2292.6 246 2303.07 246 2324V3073.5C246 3102.87 246 3117.55 240.971 3129.04C234.921 3142.88 223.878 3153.92 210.045 3159.97C198.548 3165 183.866 3165 154.5 3165V3165C125.134 3165 110.452 3165 98.9551 3170.03C85.1224 3176.08 74.0793 3187.12 68.0287 3200.96C63 3212.45 63 3227.13 63 3256.5V3909C63 3941.19 63 3957.28 69.0151 3969.68C74.8966 3981.81 84.6894 3991.6 96.8154 3997.48C109.217 4003.5 125.311 4003.5 157.5 4003.5V4003.5C189.689 4003.5 205.783 4003.5 218.185 4009.52C230.311 4015.4 240.103 4025.19 245.985 4037.32C252 4049.72 252 4065.81 252 4098V4371" stroke="url(#paint0_linear_79_105)" stroke-width="3" />
                    <g filter="url(#filter0_d_79_105)">
                        <path d="M112 40.3038C112 26.8812 122.881 16 136.304 16C149.726 16 160.608 26.8812 160.608 40.3038V71.6962C160.608 85.1188 149.726 96 136.304 96C122.881 96 112 85.1188 112 71.6962V40.3038Z" fill="url(#paint1_linear_79_105)" shape-rendering="crispEdges" />
                        <path d="M112 40.3038C112 26.8812 122.881 16 136.304 16C149.726 16 160.608 26.8812 160.608 40.3038V71.6962C160.608 85.1188 149.726 96 136.304 96C122.881 96 112 85.1188 112 71.6962V40.3038Z" stroke="#6041B0" shape-rendering="crispEdges" />
                        <path d="M137.25 40.5C137.25 39.8096 136.69 39.25 136 39.25C135.31 39.25 134.75 39.8096 134.75 40.5H137.25ZM135.116 72.3839C135.604 72.872 136.396 72.872 136.884 72.3839L144.839 64.4289C145.327 63.9408 145.327 63.1493 144.839 62.6612C144.351 62.173 143.559 62.173 143.071 62.6612L136 69.7322L128.929 62.6612C128.441 62.173 127.649 62.173 127.161 62.6612C126.673 63.1493 126.673 63.9408 127.161 64.4289L135.116 72.3839ZM134.75 40.5V71.5H137.25V40.5H134.75Z" fill="#EBEBF3" />
                    </g>
                    <g filter="url(#filter1_d_79_105)">
                        <circle cx="261" cy="706" r="21" fill="url(#paint2_linear_79_105)" />
                        <circle cx="261" cy="706" r="24" stroke="#1B1B41" stroke-width="6" />
                    </g>
                    <path d="M263.027 699.909V713H260.656V702.217H260.579L257.517 704.173V701.999L260.771 699.909H263.027Z" fill="white" />
                    <g filter="url(#filter2_d_79_105)">
                        <circle cx="81" cy="1641" r="21" fill="url(#paint3_linear_79_105)" />
                        <circle cx="81" cy="1641" r="24" stroke="#1B1B41" stroke-width="6" />
                    </g>
                    <path d="M75.1712 1648V1646.29L79.7159 1641.83C80.1506 1641.39 80.5128 1641 80.8026 1640.66C81.0923 1640.32 81.3097 1639.99 81.4545 1639.67C81.5994 1639.35 81.6719 1639.01 81.6719 1638.65C81.6719 1638.24 81.5781 1637.88 81.3906 1637.59C81.2031 1637.29 80.9453 1637.06 80.6172 1636.9C80.2891 1636.74 79.9162 1636.65 79.4986 1636.65C79.0682 1636.65 78.6911 1636.74 78.3672 1636.92C78.0433 1637.1 77.7919 1637.35 77.6129 1637.67C77.4382 1637.99 77.3509 1638.38 77.3509 1638.83H75.0945C75.0945 1638 75.2841 1637.27 75.6634 1636.66C76.0426 1636.05 76.5646 1635.57 77.2294 1635.24C77.8984 1634.9 78.6655 1634.73 79.5305 1634.73C80.4084 1634.73 81.1797 1634.89 81.8445 1635.22C82.5092 1635.55 83.0249 1636 83.3913 1636.57C83.7621 1637.14 83.9474 1637.79 83.9474 1638.53C83.9474 1639.02 83.8537 1639.5 83.6662 1639.97C83.4787 1640.44 83.1484 1640.97 82.6754 1641.54C82.2067 1642.12 81.5483 1642.82 80.7003 1643.63L78.4439 1645.93V1646.02H84.1456V1648H75.1712Z" fill="white" />
                    <g filter="url(#filter3_d_79_105)">
                        <circle cx="245" cy="2551" r="21" fill="url(#paint4_linear_79_105)" />
                        <circle cx="245" cy="2551" r="24" stroke="#1B1B41" stroke-width="6" />
                    </g>
                    <path d="M243.324 2558.18C242.404 2558.18 241.586 2558.02 240.87 2557.71C240.158 2557.39 239.596 2556.95 239.182 2556.39C238.769 2555.83 238.55 2555.18 238.524 2554.44H240.927C240.949 2554.79 241.066 2555.1 241.279 2555.37C241.492 2555.63 241.775 2555.83 242.129 2555.97C242.483 2556.12 242.879 2556.19 243.318 2556.19C243.787 2556.19 244.202 2556.11 244.564 2555.95C244.927 2555.78 245.21 2555.55 245.415 2555.26C245.619 2554.96 245.719 2554.62 245.715 2554.24C245.719 2553.85 245.617 2553.5 245.408 2553.19C245.199 2552.89 244.897 2552.65 244.501 2552.48C244.108 2552.31 243.635 2552.23 243.081 2552.23H241.925V2550.4H243.081C243.537 2550.4 243.936 2550.32 244.277 2550.16C244.622 2550.01 244.893 2549.78 245.089 2549.5C245.285 2549.21 245.381 2548.87 245.376 2548.5C245.381 2548.12 245.297 2547.8 245.127 2547.53C244.961 2547.25 244.724 2547.04 244.417 2546.88C244.115 2546.73 243.759 2546.65 243.35 2546.65C242.949 2546.65 242.579 2546.73 242.238 2546.87C241.897 2547.02 241.622 2547.22 241.413 2547.49C241.204 2547.76 241.094 2548.07 241.081 2548.44H238.799C238.816 2547.7 239.027 2547.06 239.432 2546.51C239.841 2545.95 240.386 2545.51 241.068 2545.2C241.75 2544.89 242.515 2544.73 243.363 2544.73C244.236 2544.73 244.995 2544.89 245.638 2545.22C246.286 2545.55 246.787 2545.98 247.14 2546.53C247.494 2547.08 247.671 2547.69 247.671 2548.35C247.675 2549.09 247.458 2549.71 247.019 2550.21C246.584 2550.71 246.013 2551.03 245.306 2551.19V2551.29C246.226 2551.42 246.932 2551.76 247.422 2552.31C247.916 2552.86 248.161 2553.54 248.157 2554.36C248.157 2555.1 247.948 2555.75 247.53 2556.33C247.117 2556.91 246.546 2557.36 245.817 2557.69C245.093 2558.01 244.262 2558.18 243.324 2558.18Z" fill="white" />
                    <g filter="url(#filter4_d_79_105)">
                        <circle cx="62" cy="3356" r="21" fill="url(#paint5_linear_79_105)" />
                        <circle cx="62" cy="3356" r="24" stroke="#1B1B41" stroke-width="6" />
                    </g>
                    <path d="M56.1261 3360.57V3358.69L61.6808 3349.91H63.2532V3352.59H62.2944L58.555 3358.52V3358.62H66.3086V3360.57H56.1261ZM62.3711 3363V3360L62.3967 3359.15V3349.91H64.6339V3363H62.3711Z" fill="white" />
                    <defs>
                        <filter id="filter0_d_79_105" x="86.5" y="0.5" width="99.6074" height="131" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix" />
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                            <feOffset dy="10" />
                            <feGaussianBlur stdDeviation="12.5" />
                            <feComposite in2="hardAlpha" operator="out" />
                            <feColorMatrix type="matrix" values="0 0 0 0 0.529412 0 0 0 0 0.356863 0 0 0 0 0.972549 0 0 0 0.3 0" />
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_79_105" />
                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_79_105" result="shape" />
                        </filter>
                        <filter id="filter1_d_79_105" x="199" y="645" width="124" height="124" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix" />
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                            <feOffset dy="1" />
                            <feGaussianBlur stdDeviation="17.5" />
                            <feComposite in2="hardAlpha" operator="out" />
                            <feColorMatrix type="matrix" values="0 0 0 0 0.529412 0 0 0 0 0.356863 0 0 0 0 0.972549 0 0 0 0.8 0" />
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_79_105" />
                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_79_105" result="shape" />
                        </filter>
                        <filter id="filter2_d_79_105" x="19" y="1580" width="124" height="124" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix" />
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                            <feOffset dy="1" />
                            <feGaussianBlur stdDeviation="17.5" />
                            <feComposite in2="hardAlpha" operator="out" />
                            <feColorMatrix type="matrix" values="0 0 0 0 0.529412 0 0 0 0 0.356863 0 0 0 0 0.972549 0 0 0 0.8 0" />
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_79_105" />
                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_79_105" result="shape" />
                        </filter>
                        <filter id="filter3_d_79_105" x="183" y="2490" width="124" height="124" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix" />
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                            <feOffset dy="1" />
                            <feGaussianBlur stdDeviation="17.5" />
                            <feComposite in2="hardAlpha" operator="out" />
                            <feColorMatrix type="matrix" values="0 0 0 0 0.529412 0 0 0 0 0.356863 0 0 0 0 0.972549 0 0 0 0.8 0" />
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_79_105" />
                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_79_105" result="shape" />
                        </filter>
                        <filter id="filter4_d_79_105" x="0" y="3295" width="124" height="124" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix" />
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                            <feOffset dy="1" />
                            <feGaussianBlur stdDeviation="17.5" />
                            <feComposite in2="hardAlpha" operator="out" />
                            <feColorMatrix type="matrix" values="0 0 0 0 0.529412 0 0 0 0 0.356863 0 0 0 0 0.972549 0 0 0 0.8 0" />
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_79_105" />
                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_79_105" result="shape" />
                        </filter>
                        <linearGradient id="paint0_linear_79_105" x1="162.5" y1="56.5" x2="162.5" y2="4157" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#2E2E60" />
                            <stop offset="0.80218" stop-color="#5E5EC3" />
                            <stop offset="1" stop-color="#5F5FC6" stop-opacity="0" />
                        </linearGradient>
                        <linearGradient id="paint1_linear_79_105" x1="118" y1="16" x2="155" y2="108" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#A380FF" />
                            <stop offset="0.285" stop-color="#9975F4" />
                            <stop offset="1" stop-color="#290781" />
                        </linearGradient>
                        <linearGradient id="paint2_linear_79_105" x1="244.594" y1="669.25" x2="276.094" y2="738.156" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#A380FF" />
                            <stop offset="0.285" stop-color="#9975F4" />
                            <stop offset="1" stop-color="#290781" />
                        </linearGradient>
                        <linearGradient id="paint3_linear_79_105" x1="64.5937" y1="1604.25" x2="96.0937" y2="1673.16" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#A380FF" />
                            <stop offset="0.285" stop-color="#9975F4" />
                            <stop offset="1" stop-color="#290781" />
                        </linearGradient>
                        <linearGradient id="paint4_linear_79_105" x1="228.594" y1="2514.25" x2="260.094" y2="2583.16" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#A380FF" />
                            <stop offset="0.285" stop-color="#9975F4" />
                            <stop offset="1" stop-color="#290781" />
                        </linearGradient>
                        <linearGradient id="paint5_linear_79_105" x1="45.5937" y1="3319.25" x2="77.0937" y2="3388.16" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#A380FF" />
                            <stop offset="0.285" stop-color="#9975F4" />
                            <stop offset="1" stop-color="#290781" />
                        </linearGradient>
                    </defs>
                </svg> */}
                <img
                    src={FlowStepImg}
                    alt="Flow step"
                    className="hidden md:block top-0 ml-[2.5rem] w-[20vw] h-[420vh]"
                />

                <div className="md:absolute top-[40vh] w-full flex justify-center">
                    <FlowStep
                        heading="MANUAL WORK"
                        personHeading="Developer"
                        personContent="You're manually writing test cases and running unit tests, spending hours or even days to ensure the code works as expected."
                        personTime="Hours/days"
                        drcodeMessage="I'll take care of all the unit testing for you, and it'll be done in less than 5 minutes. You focus on building great code, and I'll handle the rest!"
                        drcodeTime="5 min"
                        employee_img={developer_img}
                        index={1}
                    />
                </div>
                <div className="md:absolute top-[140vh] w-full flex justify-center pr-60">
                    <FlowStep
                        heading="REVIEWER"
                        personHeading="Reviewer"
                        personContent="You're going through lines of code, manually reviewing for logic, company guidelines, and best practices. It can take anywhere from 20 to 60 minutes for each review."
                        personTime="Hours/days"
                        drcodeMessage="I'll automatically review your code for logic, guidelines, and best practices in just 1 minute. You relax and leave the heavy lifting to me!"
                        drcodeTime="1 min"
                        employee_img={reviewer_img}
                        index={2}
                    />

                </div>
                <div className="md:absolute top-[230vh] w-full flex justify-center pl-20">
                    <FlowStep
                        heading="TESTER"
                        personHeading="Tester"
                        personContent="You're going through lines of code, manually reviewing for logic, company guidelines, and best practices. It can take anywhere from 20 to 60 minutes for each review."
                        personTime="Hours/days"
                        drcodeMessage="Why spend so much time testing? I've got it under control! In just 5 minutes, I'll run all the front-end and API test cases, so you can keep moving forward."
                        drcodeTime="5 min"
                        employee_img={tester_img}
                        index={3}
                    />
                </div>
                <div className="md:absolute top-[315vh] w-full flex justify-center pr-60">
                    <FlowStep
                        heading="PRODUCTION"
                        personHeading="Production"
                        personContent="After the release, you spend hours analyzing logs, trying to identify functional, security, or performance issues and tracing them back to the root cause."
                        personTime="Hours/days"
                        drcodeMessage="Let me take over! I'll track down those production issues, pinpoint the root cause, and have it all sorted out in just 5 minutes. You can trust me to keep things running smoothly!"
                        drcodeTime="5 min"
                        employee_img={production_img}
                        index={4}
                    />
                </div>

            </div>

            <div className="block md:hidden relative flex flex-col space-y-24 items-center justify-start w-full h-[270rem]">
                <img
                    src={FlowStepMb}
                    alt="Flow step"
                    className="md:hidden top-0   h-[520vh] md:h-[450vh]"
                />
                <div className="absolute top-[17vh] md:top-[15vh] w-full flex justify-center">
                    <FlowStepMobile
                        heading="MANUAL WORK"
                        personHeading="Developer"
                        personContent="You're manually writing test cases and running unit tests, spending hours or even days to ensure the code works as expected."
                        personTime="Hours/days"
                        drcodeMessage="I'll take care of all the unit testing for you, and it'll be done in less than 5 minutes. You focus on building great code, and I'll handle the rest!"
                        drcodeTime="5 min"
                        employee_img={developer_img}
                        index={1}
                    />
                </div>
                <div className="absolute top-[145vh] md:top-[127vh] w-full flex justify-center">
                    <FlowStepMobile
                        heading="REVIEWER"
                        personHeading="Reviewer"
                        personContent="You're going through lines of code, manually reviewing for logic, company guidelines, and best practices. It can take anywhere from 20 to 60 minutes for each review."
                        personTime="Hours/days"
                        drcodeMessage="I'll automatically review your code for logic, guidelines, and best practices in just 1 minute. You relax and leave the heavy lifting to me!"
                        drcodeTime="1 min"
                        employee_img={reviewer_img}
                        index={2}
                    />
                </div>
                <div className="absolute top-[270vh] md:top-[234vh] w-full flex justify-center">
                    <FlowStepMobile
                        heading="TESTER"
                        personHeading="Tester"
                        personContent="You're going through lines of code, manually reviewing for logic, company guidelines, and best practices. It can take anywhere from 20 to 60 minutes for each review."
                        personTime="Hours/days"
                        drcodeMessage="Why spend so much time testing? I've got it under control! In just 5 minutes, I'll run all the front-end and API test cases, so you can keep moving forward."
                        drcodeTime="5 min"
                        employee_img={tester_img}
                        index={3}
                    />
                </div>
                <div className="absolute top-[410vh] md:top-[352vh] w-full flex justify-center">
                    <FlowStepMobile
                        heading="PRODUCTION"
                        personHeading="Production"
                        personContent="After the release, you spend hours analyzing logs, trying to identify functional, security, or performance issues and tracing them back to the root cause."
                        personTime="Hours/days"
                        drcodeMessage="Let me take over! I'll track down those production issues, pinpoint the root cause, and have it all sorted out in just 5 minutes. You can trust me to keep things running smoothly!"
                        drcodeTime="5 min"
                        employee_img={production_img}
                        index={4}
                    />
                </div>
            </div>
        </main>
    );
};

export default Flow;
