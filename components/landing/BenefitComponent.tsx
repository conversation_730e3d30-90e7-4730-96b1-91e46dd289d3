import { motion } from "framer-motion";
import React from "react";
const dashboard_1 = "/Assets/Svg/rocket_productivity_1.svg";

interface BenefitComponentProps {
    image: string;
    title: string;
    description: string;
    id: number
}

const BenefitComponent: React.FC<BenefitComponentProps> = ({ id, image, title, description }) => {
    const getGradient = (id: number) => {
        if (id === 1 || id === 2 || id === 3) {
            return "from-[#000000]/40 to-[#1A1749]/60";
        } else if (id === 4 || id === 5 || id === 6) {
            return "from-[#1A1749]/60 to-[#000000]/60";
        }
    };
    return (
        <div className={`flex flex-col p-8 border border-[#392668] rounded-[24px] h-[37vh] md:h-[40vh] bg-gradient-to-b ${getGradient(id)}`}>
            <img
                src={image}
                alt="benefit"
                className="w-[70px] h-[70px]"
            />
            <div className="mt-2 text-[16px] md:text-[18px] text-left font-bold w-full text-[#FFFFFF] ">
                {title}
            </div>
            <div className="mt-2 text-[14px] md:text-[16px] font-[400] text-left w-full text-[#D1D1E3]">
                {description}
            </div>
        </div>
    );
};
export default BenefitComponent;