import React from 'react'
interface AgentDetailsProps {
    agent: {
        img: string;
        title: string;
        desc: string;
        bullet_pts: string[];
    };
}

const AgentDetails: React.FC<AgentDetailsProps> = ({ agent }) => {
    return (
        <div className='box1 md:h-[95%] w-[98%] ml-[5px] mt-[10px] md:mt-[5px] rounded-3xl flex flex-col bg-gradient-to-b from-[#000000] to-[#1A1749] p-3 md:p-2  ' style={{ border: "1px solid #875BF8" }}>
            <img src={agent.img} alt={agent.title} className="w-full h-[50%] object-left md:object-top object-cover rounded-t-3xl" />
            <div className="w-full h-[45%] flex flex-col md:ml-4 mt-[5vh] md:mt-[7vh]">
                <h2 className="text-xl font-bold">{agent.title}</h2>
                <p className="text-lg mt-2 text-[#D1D1E3] mr-[10px]">{agent.desc}</p>
                <ul className="list-disc ml-4 mt-2">
                    {agent.bullet_pts.map((bullet) => (
                        <li key={bullet} className="text-lg text-[#D1D1E3] ">{bullet}</li>
                    ))}
                </ul>
            </div>
        </div>
    )
}

export default AgentDetails