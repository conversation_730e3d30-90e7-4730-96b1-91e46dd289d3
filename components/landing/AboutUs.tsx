"use client";
import { motion } from "framer-motion";
import React from "react";
import Image from "next/image";
// const EyeScan = "/Assets/Svg/EyeScan.svg";
// const Targets = "/Assets/Svg/Target.svg";
// const HeartHand = "/Assets/Svg/HandHeart.svg";

const AboutUs = () => {

  const fadeIn = (type: any, delay: any, duration: any) => {
    return {
      hidden: {
        y: 10,
        opacity: 0,
      },
      show: {
        y: 0,
        opacity: 1,
        transition: {
          type: type,
          delay: delay,
          duration: duration,
          ease: "easeOut",
        },
      },
    };
  };

  return (
    <div className="text-white w-full px-6 md:px-12 lg:px-32 py-4 flex flex-col items-center justify-center mt-10 mx-auto md:w-[85%]">
      <motion.div initial='hidden' whileInView='show' viewport={{ once: true, amount: 0.25 }} variants={fadeIn("spring", 0.5, 1.5)} className="mb-12 w-full max-w-8xl flex flex-col items-center">
        <h3 className="text-[#875BF8] font-semibold text-start mb-2 text-[20px]">
          Know more about Dr Code
        </h3>
        <h1 className="text-3xl md:text-4xl lg:text-5xl text-center font-bold text-[#D1D1E3]">
          Join us on a journey of Innovation
        </h1>
      </motion.div>
      <div className="flex flex-col md:flex-row justify-center gap-20 w-full max-w-full">
        <motion.div initial='hidden' whileInView='show' viewport={{ once: true, amount: 0.25 }} variants={fadeIn("spring", 1.0, 1.5)} className="flex flex-col items-center md:items-start md:w-1/3 text-center md:text-left">
          <div className="mb-4">
            <Image
              src={"/EyeScan.svg"}
              width={80}
              height={80}
              alt="Our Vision"
            />
          </div>
          <h3 className="text-lg font-semibold text-[#D1D1E3]">Our Vision</h3>
          <p className="mt-2 text-[#8B8B99] break-words max-w-xs">
            To simplify technology and make it usable and accessible to everyone
            in the world.
          </p>
        </motion.div>
        <motion.div initial='hidden' whileInView='show' viewport={{ once: true, amount: 0.25 }} variants={fadeIn("spring", 1.3, 1.5)} className="flex flex-col items-center md:items-start md:w-1/3 text-center md:text-left">
          <div className="mb-4">
            <Image
              width={80}
              height={80}
              src={"/Target.svg"}
              alt="Our Mission"
            />
          </div>
          <h3 className="text-lg font-semibold text-[#D1D1E3]">Our Mission</h3>
          <p className="mt-2 text-[#8B8B99] break-words max-w-xs">
            To help users build, grow, and scale by adapting to scalable and
            affordable technology.
          </p>
        </motion.div>
        <motion.div initial='hidden' whileInView='show' viewport={{ once: true, amount: 0.25 }} variants={fadeIn("spring", 1.6, 1.5)} className="flex flex-col items-center md:items-start md:w-1/3 text-center md:text-left">
          <div className="mb-4">
            <Image
              src={"/HandHeart.svg"}
              width={80}
              height={80}
              alt="Our Values"
            />
          </div>
          <h3 className="text-lg font-semibold text-[#D1D1E3]">Our Values</h3>
          <p className="mt-2 text-[#8B8B99] break-words max-w-xs">
            We're obsessed with ownership, integrity, and agility.
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default AboutUs;
