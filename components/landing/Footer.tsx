"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import {CONTACT_US,Privacy_Policy,TNC,SLA} from "@/temp-utils/Constants/RouteConstant";


const ScrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
};

const Footer: React.FC = () => {
  return (
    <div className="text-center text-white p-8 max-w-[1400px] mx-auto h-full">
      <div className="flex flex-col sm:flex-row items-center justify-between w-full">
        <div className="flex items-center mb-4 sm:mb-0 cursor-pointer gap-3">
          <Image
            src={"/Logo.svg"}
            alt="Dr.Code AI automation for software development lifecycle"
            width={190}
            height={128}
            style={{ verticalAlign: "middle" }}
            onClick={ScrollToTop}
          />
          <p className="text-sm text-gray-400 mb-4 sm:mb-0">
            © All rights reserved.
          </p>
        </div>
        <div className="flex gap-4">
          <ActiveLink href={CONTACT_US}>Contact Us</ActiveLink>
          <ActiveLink href={TNC}>Terms and Conditions</ActiveLink>
          <ActiveLink href={Privacy_Policy}>Privacy Policy</ActiveLink>
          {/* <ActiveLink href={SLA}>Service Level Agreement</ActiveLink> */}
        </div>
      </div>
    </div>
  );
};

interface ActiveLinkProps {
  children: React.ReactNode;
  href: string;
}

const ActiveLink: React.FC<ActiveLinkProps> = ({ children, href }) => {
  const [isActive, setIsActive] = useState<boolean>(false);

  useEffect(() => {
    const { pathname } = window.location;
    setIsActive(pathname === href);
  }, [href]);

  return (
    <Link href={href}>
      <p
        className={`text-[16px] text-gray-400 mb-4 sm:mb-0 ${
          isActive ? "underline underline-offset-4 decoration-[#875BF8]" : ""
        }`}
      >
        {children}
      </p>
    </Link>
  );
};

export default Footer;
