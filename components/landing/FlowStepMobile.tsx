"use client";

import React, { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
const drcode_flow_message_box_top = "../../drcode_flow_message_box_top.svg";

import "./FlowStep.css";
import { motion } from "framer-motion"
import LogoWhite from "../../public/LogoWhite.svg";
import { StaticImageData } from "next/image";
import Image from "next/image";
interface FlowStepProps {
    heading: string;
    personHeading: string;
    personContent: string;
    personTime: string;
    drcodeMessage: string;
    drcodeTime: string;
    employee_img: string;
    index: number;
}

const FlowStepMobile: React.FC<FlowStepProps> = ({
    heading, personHeading, personContent, personTime,
    drcodeMessage, drcodeTime, employee_img, index
}) => {
    const router = useRouter();
    const convertToPixels = (value: number, unit: string) => {
        if (unit === "vw") {
            return (window.innerWidth * value) / 100;
        } else if (unit === "rem") {
            const fontSize = parseFloat(getComputedStyle(document.documentElement).fontSize);
            return fontSize * value;
        }
        return value;
    };

    const fadeUp = (type: any, delay: any, duration: any) => {
        return {
            hidden: {
                y: convertToPixels(10, "vw"),
                opacity: 0,
            },
            show: {
                y: 0,
                opacity: 1,
                transition: {
                    type: type,
                    delay: delay,
                    duration: duration,
                    ease: "easeOut",
                },
            },
        };
    };

    return (
        <div className="relative flex flex-col items-center justify-center w-full">
            <div className="absolute top-4">
                <div className="flex flex-col justify-center items-center gap-[15vh]">
                    <motion.div initial='hidden' whileInView='show' viewport={{ once: true, amount: 0.25 }} variants={fadeUp("spring", 0.5, 1.5)} className="flex items-center relative">
                        <img
                            src={employee_img}
                            alt="flow_step"
                            className="h-[45vh] w-[20rem]"
                        />
                        <div
                            className="absolute top-[-104px] right-[28px] mt-4 p-6 border-2 border-[#2E2E60] rounded-[32px] w-[60vw]"
                            style={{
                                backgroundColor: '#16161CBF',
                                backdropFilter: 'blur(8px)',
                            }}
                        >
                            <div className="relative flex flex-col justify-center z-10">
                                <p className="text-[18px] font-semibold text-[#E2E2ED]">{personHeading}</p>
                                <p className="text-[16px] font-normal text-[#E2E2ED]">{personContent}</p>
                                <p className="mt-2 text-[14px] font-400 text-[#E2E2ED]">
                                    Average Time: <span className="text-[#9F7CF9]">{personTime}</span>
                                </p>
                            </div>
                        </div>
                    </motion.div>
                    <motion.div initial='hidden' whileInView='show' viewport={{ once: true, amount: 0.25 }} variants={fadeUp("spring", 0.5, 1.5)} className={`flex items-center justify-center relative`}>
                        <img
                            src={drcode_flow_message_box_top}
                            alt="DrCode Message on development lifecycle"
                            className="h-100 w-[85vw]"
                        />
                        <div className="absolute inset-0 flex flex-col justify-center top-4 z-10 w-[80%] ml-10">
                            <div className='flex space-x-2'>
                                <Image src={LogoWhite} alt="Logo" width={30} height={30} className="" />
                                <p className="text-[17px] font-semibold text-[#E2E2ED]">DrCode</p>
                            </div>
                            <p className="mt-2 text-[16px] font-normal text-[#E2E2ED]">I've got this!</p>
                            <p className="text-[16px] font-normal text-[#E2E2ED]">{drcodeMessage}</p>
                            <p className="mt-2 text-[14px] font-400 text-[#E2E2ED]">
                                Average Time: <span className="text-[#9F7CF9]">{drcodeTime}</span>
                            </p>
                        </div>
                    </motion.div>
                </div>
            </div>
        </div >
    );
};

export default FlowStepMobile;
