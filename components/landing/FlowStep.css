@keyframes pop {
    from {
        opacity: 0;
        scale: 0.5;
    }
    to {
        opacity: 1;
        scale: 1;
    }
}

.pop-animation {
    animation: pop 1s ease-out forwards;
    animation-timeline: view();
    animation-range: entry 0% cover 50%;
}

@media (max-width: 768px) {
    .drcode-container {
        right: 0;
        top: 0;
    }

    .drcode-content {
        width: 100%;
        margin-left: 0;
        padding: 2rem;
    }

    .drcode-image {
        width: 100%;
        height: auto;
    }
}