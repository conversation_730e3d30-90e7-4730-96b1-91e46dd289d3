import React, { useState, useRef, useEffect, useCallback } from 'react'
import AgentDetails from './AgentDetails'
import './Agents.css'
import { motion } from "framer-motion";

const Agents = () => {
    const [active, setActive] = useState(0)
    const [isVisible, setIsVisible] = useState(false);
    const sectionRef = useRef(null);
    const agentsRef: React.RefObject<HTMLDivElement>[] = [
        useRef(null),
        useRef(null),
        useRef(null)
    ]
    const agentDetails = [
        {
            img: "/qa-agent.png",
            title: "Quality Assurance Agent",
            desc: `
            The Quality Assurance Agent simplifies the QA process by automating unit tests, saving time, and ensuring seamless adaptability to API changes.
        `,
            bullet_pts: [
                `⁠Automate routine unit testing with the Testing Agent, reducing testing time from hours to 
            just 5 minutes.`,
                `Effortlessly handle API changes by generating new test cases and managing their impacts across multiple areas, tailored to your specific industry needs.`
            ],
        },
        {
            img: "/codereview-agent.png",
            title: "Code Review Agent",
            desc: `
            The Code Review Agent revolutionizes pull request (PR) reviews by analyzing code for logic, guidelines, and best practices while offering advanced features like sequence diagram generation and code walkthroughs, all in just 1 minute.
            `,
            bullet_pts: [
                `⁠Automate PR reviews with the Code Reviewer, reducing review time from 20–60 minutes to just 1 minute.`,
                `Access features like sequence diagram generation, code walkthroughs, file reports, and early bug detection for quality merges aligned with your standards.`
            ]
        },
        {
            img: "/pm-agent.png",
            title: "Performance Monitoring Agent",
            desc: `
            The Performance Monitoring Agent automates log analysis and root cause identification, resolving functional, security, or performance issues in minutes.
            `,
            bullet_pts: [
                `⁠Automate real-time bug tracking and resolution, pinpointing root causes within 5 minutes.`,
                `⁠Proactively address issues by generating insights tailored to your system's unique 
operational needs`
            ]
        }
    ];

    const fadeIn = (type: any, delay: any, duration: any) => {
        return {
            hidden: {
                y: 10,
                opacity: 0,
            },
            show: {
                y: 0,
                opacity: 1,
                transition: {
                    type: type,
                    delay: delay,
                    duration: duration,
                    ease: "easeOut",
                },
            },
        };
    };

    const containerRef = useRef<HTMLDivElement>(null);
    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                setIsVisible(entry.isIntersecting);
            },
            { threshold: 1 }
        );

        if (containerRef.current) observer.observe(containerRef.current);

        return () => {
            if (containerRef.current) observer.unobserve(containerRef.current);
        };
    }, []);

    const useDebouncedCallback = (callback: (...args: any[]) => void, delay: number) => {
        const timeoutRef = useRef<NodeJS.Timeout | null>(null);

        return useCallback(
            (...args: any) => {
                if (timeoutRef.current) {
                    clearTimeout(timeoutRef.current);
                }
                timeoutRef.current = setTimeout(() => {
                    callback(...args);
                }, delay);
            },
            [callback, delay]
        );
    };
    const handleScroll = useDebouncedCallback(() => {
        if (containerRef.current) {
            const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
            const scrolled = Math.round((scrollTop / (scrollHeight - clientHeight)) * 100);

            if (scrolled >= 0 && scrolled < 33 && active !== 0) {
                setActive(0);
            } else if (scrolled >= 33 && scrolled < 66 && active !== 1) {
                setActive(1);
            } else if (scrolled >= 66 && scrolled <= 100 && active !== 2) {
                setActive(2);
            }
        }
    }, 10);

    useEffect(() => {
        const container = containerRef.current;
        if (container) {
            container.addEventListener("scroll", handleScroll);
        }
        return () => {
            if (container) {
                container.removeEventListener("scroll", handleScroll);
            }
        };
    }, [handleScroll]);




    // const scrollTo = (index: number) => {
    //     setActive(index);
    //     if (index === 0) {
    //         agentsRef[0].current?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    //     } else if (index === 1) {
    //         agentsRef[1].current?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    //     } else if (index === 2) {
    //         agentsRef[2].current?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    //     }
    // }

    const scrollTo = (index: number) => {
        setActive(index);
        if (containerRef.current) {
            const sectionHeight = 700; // Get the height of the container
            const scrollToPosition = index * sectionHeight; // Calculate the scroll position
            containerRef.current.scrollTo({
                top: scrollToPosition,
                behavior: 'smooth'
            });
        }
    };

    return (
        <div ref={sectionRef} className='w-[100%] md:h-[120vh] flex justify-center items-center mb-[125px]'>
            <div className='w-[80%] md:h-[95%] flex justify-evenly items-center flex-col'>
                <div className='basis-[15%] w-[100%] h-[100%] flex justify-center items-center flex-col'>
                    <h4 className='text-[#875BF8] text-[16px] font-bold'>Features</h4>
                    <h2 className='text-[40px] font-bold text-[#D1D1E3] text-center'>AI that feels part of the team</h2>
                    <p className='text-[18px] font-[400] text-[#B2B2C1] text-center'>AI agents designed to accelerate development, ensure quality, and boost team's productivity.</p>
                </div>
                <div className='basis-[77%] w-[100%] md:h-[99vh] flex justify-evenly items-center flex-row md:mt-[10vh] '>
                    <div className='hidden  md:basis-[20%] w-[100%] h-[90%] md:flex justify-around items-center flex-row relative pt-5 '>
                        <div className='absolute w-[5px] bg-[#875BF8] h-[40px] top-[1.25rem] left-[12.5px]' style={{
                            zIndex: "9",
                            transition: "0.5s",
                            transform: `${active === 0 ? "translateY(0)" : active === 1 ? "translateY(40px)" :
                                active === 2 ? "translateY(80px)" : ""
                                }`
                        }}></div>
                        <div className='bg-[#1B1B41] w-[3px] h-[100%] mx-[5px] pb-5'></div>
                        <div className='basis-[80%] h-[100%] w-[100%] flex gap-[15px] flex-col py-[10px]'>
                            <h4 className={`${active === 0 ? "font-bold" : ""} cursor-pointer text-[16px]`} onClick={() => {
                                scrollTo(0)
                            }}>Quality Assurance</h4>
                            <h4 className={`${active === 1 ? "font-bold" : ""} cursor-pointer text-[16px]`} onClick={() => {
                                scrollTo(1)
                            }}>Code Review</h4>
                            <h4 className={`${active === 2 ? "font-bold" : ""} cursor-pointer text-[15px]`} onClick={() => {
                                scrollTo(2)
                            }}>Performance Monitoring</h4>
                        </div>
                    </div>
                    <div
                        className={`hidden md:block basis-[100%] md:basis-[77%] w-[100%] h-[90%] ${isVisible ? 'overflow-auto' : 'overflow-hidden'} scrollbar-hidden `}
                        ref={containerRef}
                    >
                        {agentDetails.map((agent, index) => (
                            <div className="h-[100%] md:pt-5" ref={agentsRef[index]} key={index} >
                                <AgentDetails agent={agent} />
                            </div>
                        ))}
                    </div>
                    <div className={`md:hidden block w-[100%] tw-mt-2`}>
                        {agentDetails.map((agent, index) => (
                            <motion.div initial='hidden' className="h-[100%] md:pt-5" whileInView='show' viewport={{ once: true, amount: 0.25 }} variants={fadeIn("spring", 0.7, 1.5)} ref={agentsRef[index]} key={index}>
                                <AgentDetails agent={agent} />
                            </motion.div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Agents