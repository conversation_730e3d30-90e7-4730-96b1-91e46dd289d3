"use client"
import React, { useEffect } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import Image from "next/image";
// const NetworkTag = "/Assets/Svg/NetworkTag.svg";
// const ShieldStar = "/Assets/Svg/ShieldStar.svg";

const DescriptionSection = () => {
  function initializeAnimation(container_id: string) {
    const path = document.querySelector(container_id + " .rg_svg_path");
    if (path) {
      path.classList.remove("rg_animated_path");
    }
  }

  function triggerAnimation(container_id: string) {
    const path = document.querySelector(container_id + " .rg_svg_path");
    if (path) {
      path.classList.add("rg_animated_path");
    }
  }

  function initializeElement(container_id: string) {
    if (window.ResizeObserver) {
      const divElem = document.querySelector(container_id) as HTMLElement;
      const resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          const span = document.querySelector(
            container_id + " .rg_underline_this"
          ) as HTMLElement;
          const underline = document.querySelector(
            container_id + " .rg_underline"
          ) as HTMLElement;
          if (span && underline) {
            underline.style.left = `${span.offsetLeft + document.body.scrollLeft}px`;
            underline.style.width = `${span.offsetWidth}px`;
          }
        }
      });
      if (divElem) {
        resizeObserver.observe(divElem);
      }
    } else {
      console.log("Resize observer not supported!");
    }

    if ("IntersectionObserver" in window && "IntersectionObserverEntry" in window) {
      let observer = new IntersectionObserver((entries) => {
        const entry = entries[0];
        if (entry.isIntersecting) {
          // Reset animation first
          initializeAnimation(container_id);
          // Then trigger after a small delay
          setTimeout(() => {
            triggerAnimation(container_id);
          }, 200);
        } else {
          // Reset animation when out of view
          initializeAnimation(container_id);
        }
      }, {
        threshold: 0.1, // Trigger when even 10% of the element is visible
        rootMargin: '-50px' // Small offset to make the trigger point more natural
      });

      const element_to_observe = document.querySelector(
        container_id + " .rg_underline"
      );
      if (element_to_observe) {
        observer.observe(element_to_observe);
      }
    }
  }

  useEffect(() => {
    initializeElement("#rg_anim_container");
  }, []);

  const fadeIn = (direction: string, type: any, delay: any, duration: any) => {
    return {
      hidden: {
        x: direction === "right" ? 20 : 0,
        y: direction === "bottom" ? 10 : 0,
        opacity: 0,
      },
      show: {
        x: 0,
        y: 0,
        opacity: 1,
        transition: {
          type: type,
          delay: delay,
          duration: duration,
          ease: "easeOut",
        },
      },
    };
  };

  return (
    <div className="text-white py-16 px-6 lg:px-20">
      <style jsx>{`
        .rg_svg_path {
          fill: none;
          stroke: #875BF8;
          stroke-width: 4;
          stroke-linecap: round;
          stroke-miterlimit: 10;
          stroke-dasharray: 200;
          stroke-dashoffset: 200;
          transition: none; /* Ensure clean animation reset */
        }

        .rg_animated_path {
          animation: draw 0.8s forwards;
        }

        @keyframes draw {
          to {
            stroke-dashoffset: 0;
          }
        }

        .rg_underline {
          position: absolute;
          pointer-events: none;
        }

        .rg_underline_this {
          font-size: 1.125rem;
          font-weight: bold;
          color: #D1D1E3;
          position: relative;
          cursor: pointer;
        }
      `}</style>
      <div>

      </div>
      <div className="flex flex-col md:flex-row justify-center items-start md:w-[85%] md:mx-auto">
        <motion.div initial='hidden' whileInView='show' viewport={{ once: true, amount: 0.25 }} variants={fadeIn("bottom", "spring", 0.5, 1.5)} className="lg:w-1/2 w-full">
          <p className="text-[20px] text-[#875BF8] font-semibold">
            You're in safe hands!
          </p>
          <h1 className="text-4xl lg:text-[48px] font-bold mt-2 leading-tight text-[#D1D1E3]">
            We prioritize <br /> security, privacy, <br /> and compliance.
          </h1>
        </motion.div>
        <motion.div initial='hidden' whileInView='show' viewport={{ once: true, amount: 0.25 }} variants={fadeIn("bottom", "spring", 0.8, 1.5)} className="mt-10 lg:mt-0 lg:w-1/2 flex flex-col space-y-8">
          <div className="flex items-start">
            <div className="mr-4 text-purple-400 text-3xl flex-none">
              <Image width={50} height={50} src={"/NetworkTag.svg"} alt="network" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-[#D1D1E3]">
                Reviews only the essential code
              </h3>
              <p className="text-[#8B8B99] mt-1 font-normal">
                Ensures that only the necessary parts of your code are accessed,
                safeguarding sensitive information and maintaining privacy.
              </p>
            </div>
          </div>
          <div className="flex items-start">
            <div className="mr-4 text-purple-400 text-3xl flex-none">
              <Image width={50} height={50} className="h-13 w-13" src={"/NetworkTag.svg"} alt="shield" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-[#D1D1E3]">
                SSL encrypted data
              </h3>
              <p className="text-[#8B8B99] mt-1 font-normal">
                Secures data transmission with SSL encryption, protecting from
                unauthorized access and ensuring a safe and secure connection at
                all times.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default DescriptionSection;