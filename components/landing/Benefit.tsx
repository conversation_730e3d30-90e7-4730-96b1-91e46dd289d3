
import { motion } from "framer-motion";
import React from "react";
import BenefitComponent from "./BenefitComponent";
const benefit_1 = "/rocket_productivity_1.svg";
const benefit_2 = "/clock_2.svg";
const benefit_3 = "/risk_3.svg";
const benefit_4 = "/quality_4.svg";
const benefit_5 = "/integration_5.svg";
const benefit_6 = "/circuit_ai_6.svg";


const BenefitSection = () => {

  const fadeIn = (type: any, delay: any, duration: any) => {
    return {
      hidden: {
        y: 10,
        opacity: 0,
      },
      show: {
        y: 0,
        opacity: 1,
        transition: {
          type: type,
          delay: delay,
          duration: duration,
          ease: "easeOut",
        },
      },
    };
  };

  const BenefitsContent = [
    { id: 1, image: benefit_1, title: "Boost Team Productivity", description: "Automate code reviews, testing, and monitoring with DrCode to free your team for high-impact tasks. Save hours every week during the development lifecycle and focus on what truly matters." },
    { id: 2, image: benefit_2, title: "Empower Your Testing Team", description: "Streamline developer-tester collaboration with DrCode's VS Code extension, enabling seamless API-level testing for faster, more efficient results." },
    { id: 3, image: benefit_3, title: "Effective Risk Mitigation", description: "Prevent code issues with DrCode's proactive solutions, ensuring a smooth development process while minimizing downtime and costly errors." },
    { id: 4, image: benefit_4, title: "Improve Code Quality and Reliability", description: "Boost code quality and reliability with advanced automation and intelligent insights, streamlining workflows to deliver robust, error-free results with confidence." },
    { id: 5, image: benefit_5, title: "Seamless Integration", description: "Get started effortlessly with our 1-click installation VS Code. Experience the tool without the hassle of complex setup or high integration costs." },
    { id: 6, image: benefit_6, title: "Stay Ahead with AI-Powered Innovation", description: "Transform your development lifecycle with AI tools built for modern challenges." }
  ]


  return (
    <div className="relative h-full md:h-[120vh] w-full ">
      <div
        className="md:absolute md:inset-0"
        style={{
          backgroundImage: `url('/background.png')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          maskImage: 'linear-gradient(to bottom, transparent, #000 20%, #000 50%, transparent 100%)',
          WebkitMaskImage: 'linear-gradient(to bottom, transparent, #000 20%, #000 80%, transparent 100%)',
        }}
      />

      <div
        className="md:absolute md:inset-0"
        style={{
          background: 'linear-gradient(to bottom, #0A0A1A, transparent 70%), linear-gradient(to top, #0A0A1A, transparent 50%)',
        }}
      />

      <div className="md:absolute md:left-1/2 md:transform md:-translate-x-1/2 md:w-full text-center">
        <motion.div initial='hidden' whileInView='show' viewport={{ once: true }} variants={fadeIn("spring", 0.7, 1.5)} className="mb-12 w-full max-w-8xl flex flex-col items-center leading-tight tracking-wide p-2">
          <h3 className="text-[#875BF8] font-semibold text-start mb-2 text-[20px]">
            Key benefits
          </h3>
          <h1 className="text-3xl md:text-4xl lg:text-5xl text-center font-bold text-[#D1D1E3]">
            Why choose DrCode?
          </h1>
        </motion.div>
      </div>
      <div className="px-4 md:w-[80%] md:absolute md:left-1/2 md:transform md:-translate-x-1/2 text-center md:top-[15%] md:grid md:grid-cols-3">
        {BenefitsContent.map((content, index) => (
          <motion.div initial='hidden' whileInView='show' viewport={{ once: true, amount: 0.25 }} variants={fadeIn("spring", 0.5, 1.5)} className="flex flex-col p-4" key={index}>
            <BenefitComponent id={content.id} image={content.image} title={content.title} description={content.description} />
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default BenefitSection;