"use client";
import React from 'react'
import { useRouter } from "next/navigation";
import ReactPlayer from "react-player";
import { motion } from "framer-motion";
import { useState } from 'react';
import Flow from '@/components/landing/Flow';

const HomeContent = () => {
    const router = useRouter();
    const [isPlaying, setIsPlaying] = useState(false);
    const [isMuted, setIsMuted] = useState(true);
    const [showThumbnail, setShowThumbnail] = useState(true);


    const handlePlayPauseClick = () => {
        if (showThumbnail) {
            setShowThumbnail(false);
        }
        setIsPlaying((prev) => !prev);
    };
    const fadeIn = (type: any, delay: any, duration: any) => {
        return {
            hidden: {
                y: 10,
                opacity: 0,
            },
            show: {
                y: 0,
                opacity: 1,
                transition: {
                    type: type,
                    delay: delay,
                    duration: duration,
                    ease: "easeOut",
                },
            },
        };
    };


    return (
        <main>
            <div className="text-center pt-12 relative z-10">
            <motion.div initial='hidden'whileInView='show'viewport={{ once: true, amount: 0.25 }} variants={fadeIn("spring", 0.4, 1.5)} className="md:justify-center flex flex-col md:flex-row text-[30px] md:text-[64px] font-bold mb-2 md:items-center md:space-x-2">
                    <div className=" text-[#D1D1E3] leading-tight tracking-wide">
                        Ship your features
                    </div>
                    <div className="bg-gradient-to-b from-[#A380FF] via-[#9975F4] to-[#290781] bg-clip-text text-transparent">
                        {` 2x faster `}
                    </div>
                </motion.div>
                <motion.div initial='hidden' whileInView='show' viewport={{ once: true, amount: 0.25 }} variants={fadeIn("spring", 0.5, 1.5)} className="px-2 text-[18px] mb-8 text-[#B2B2C1] font-400">
                    Affordable AI agents that save time, reduce cost & boost your team's impact.
                </motion.div>
                <motion.div initial='hidden' whileInView='show' viewport={{ once: true, amount: 0.25 }} variants={fadeIn("spring", 0.6, 1.5)}>
                    <button
                        onClick={() => window.open("https://marketplace.visualstudio.com/items?itemName=DrCodeAI.drcode-vscode-tool", "_blank")}
                        className="bg-[#875BF8] border border-[#875BF8] rounded-lg text-white text-[18px] font-base py-4 md:px-20 px-10 transition hover:bg-[#7649d9]"
                    >
                        <span>Start for free</span>
                    </button>
                    <p className="text-[14px] mb-8 text-[#B2B2C1] font-400 mt-4">14-day free trial | No credit card needed</p>
                </motion.div>
            </div>

            <div className="relative h-[40vh] md:h-[1000px] mt-8 w-full">
                <div
                    className="absolute inset-0"
                    style={{
                        backgroundImage: `url('/background.png')`,
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                        maskImage: "linear-gradient(to bottom, transparent, #000 30%, #000 20%, transparent 90%)",
                        WebkitMaskImage: "linear-gradient(to bottom, transparent, #000 30%, #000 20%, transparent 90%)",
                    }}
                />

                <div
                    className="absolute inset-0"
                    style={{
                        background: "linear-gradient(to bottom, #0A0A1A, transparent 20%), linear-gradient(to top, #0A0A1A, transparent 20%)",
                    }}
                />

                {/* Video Element */}
                <div className="absolute inset-0 flex items-center justify-center">
                    <div className="relative flex items-center justify-center" style={{ width: "70%" }}>
                        {showThumbnail ? (
                            <motion.div initial='hidden' whileInView='show' viewport={{ once: true }} variants={fadeIn("spring", 0.7, 1.5)} className="w-full h-full">
                                <img
                                    src="/video_thumbnail2.png"
                                    alt="Dr Code AI automation for software development lifecycle"
                                    className="rounded-lg  w-full h-full  object-cover cursor-pointer"
                                    onClick={handlePlayPauseClick}
                                />
                            </motion.div>
                        ) : (
                            <div className="video-container">
                                <ReactPlayer
                                    url="https://cdn.drcode.ai/assets/app-images/CodeTestNew.mp4"
                                    playing={isPlaying}
                                    muted={!isMuted}
                                    loop
                                    width="100%"
                                    height="100%"
                                    controls
                                />
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </main>
    )
}

export default HomeContent