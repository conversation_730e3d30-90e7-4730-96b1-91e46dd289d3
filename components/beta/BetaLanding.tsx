"use client";

import React, { useEffect, useRef, useState } from "react";
const Logo = "../../Logo.svg";
import Image from "next/image";
import axios from "axios";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import useToast from "@/hooks/useToast";
const API_URL = "https://devapi.drcode.ai/operations?bucket=auth&operationName=contactus"
// import { HOME } from "@/Utils/Constants/RouteConstant";
// import { getDataFromLocalStorage, removeDataFromLocalStorage, setDataOnLocalStorage } from "@/Utils/globalUtilities";
// import { LOCAL_STORAGE_DATA_KEYS } from "@/Utils/Constants/localStorageDataModels";

const BetaLanding = () => {
    const {showToast} = useToast()
    const [name, setName] = useState("");
    const [email, setEmail] = useState("");
    const [phone, setPhone] = useState("");
    const [company, setCompany] = useState("");
    const [role, setRole] = useState("");
    const inputRef = useRef(null);

    const [visible, setVisible] = useState(false);

    const [nameError, setNameError] = useState<string | null>(null);
    const [emailError, setEmailError] = useState<string | null>(null);

    const [betaSuccess, setBetaSuccess] = useState<boolean>(false)

    useEffect(() => {
        if (betaSuccess) {
            setTimeout(() => setVisible(true), 10); // Small delay for smooth transition
        } else {
            setVisible(false);
        }
    }, [betaSuccess]);

    useEffect(() => {
        inputRef.current?.focus(); // Set focus after the component mounts
    }, []);

    const validateFields = () => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const phoneRegex = /^[0-9]{10}$/;

        let isValid = true;

        if (!name) {
            setNameError("Oops! We need your name to personalize things.");
            isValid = false;
        } else {
            setNameError(null);
        }

        if (!email || !emailRegex.test(email)) {
            setEmailError(
                "We promise not to spam! But please provide a valid email."
            );
            isValid = false;
        } else {
            setEmailError(null);
        }
        return isValid;
    };

    const submitHandler = async () => {
        if (!validateFields()) return;
        try {
            const response = await axios.post(
                `${API_URL}`,
                { name, email, phone, company, role, utm_source: "beta-landing-page", utm_medium: "Beta-list", utm_campaign: "Beta-testing" },
                { headers: { "Content-Type": "application/json" } }
            );

            const responseMessage = response.data.message;

            if (responseMessage === "ok") {

                // toast.success("Thanks for reaching out! We'll get in touch with you soon.");
                setBetaSuccess(true)
                // setTimeout(() => {
                //     router.push(HOME);
                // }, 5000);
            } else {
                // toast.error(responseMessage);
                showToast(responseMessage, "error")
                
            }
        } catch (error) {
            console.error("Error sending contact request:", error);
            // toast.error("An error occurred while sending your request.");
            showToast("An error occurred while sending your request.", "error")
        }
    }

    return (
        <div className='w-[100%] h-[100%] fixed z-10 bg-[url("/mobileBeta.png")] md:bg-[url("/betaBg.png")] bg-cover bg-center flex justify-between items-center flex-col'>
            <div className='basis-[8%] w-[85%] md:w-[70%] h-full flex justify-between items-end flex-row'>
                <a className={`h-[50px] w-[175px] mt-[25px]`} href="/">
                    <img src="/Logo.svg" alt="" />
                </a>
                <div className='h-[50px] w-[75px]  flex justify-evenly items-center flex-row'>
                    <a href="https://x.com/DrCodeAi" target="_blank">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 0C5.37281 0 0 5.37281 0 12C0 18.6272 5.37281 24 12 24C18.6272 24 24 18.6272 24 12C24 5.37281 18.6272 0 12 0Z" fill="#B2B2C1" />
                            <path d="M13.312 10.9138L18.449 4.94238H17.2317L12.7712 10.1273L9.20864 4.94238H5.09961L10.4869 12.7829L5.09961 19.0448H6.317L11.0274 13.5694L14.7898 19.0448H18.8988L13.3117 10.9138H13.312ZM6.75563 5.85882H8.62546L17.2323 18.17H15.3625L6.75563 5.85882Z" fill="black" />
                        </svg>
                    </a>

                    <a href="https://www.linkedin.com/company/drcodeai/" target="_blank">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_4246_14322)">
                                <path d="M12 0C5.3736 0 0 5.3736 0 12C0 18.6264 5.3736 24 12 24C18.6264 24 24 18.6264 24 12C24 5.3736 18.6264 0 12 0ZM8.51294 18.1406H5.59039V9.34808H8.51294V18.1406ZM7.05176 8.14746H7.03271C6.052 8.14746 5.41772 7.47235 5.41772 6.6286C5.41772 5.76581 6.07141 5.10938 7.07117 5.10938C8.07092 5.10938 8.68616 5.76581 8.7052 6.6286C8.7052 7.47235 8.07092 8.14746 7.05176 8.14746ZM19.051 18.1406H16.1288V13.4368C16.1288 12.2547 15.7057 11.4485 14.6483 11.4485C13.8409 11.4485 13.3601 11.9923 13.1488 12.5173C13.0715 12.7051 13.0527 12.9677 13.0527 13.2305V18.1406H10.1303C10.1303 18.1406 10.1686 10.173 10.1303 9.34808H13.0527V10.593C13.441 9.9939 14.1359 9.14172 15.6865 9.14172C17.6093 9.14172 19.051 10.3984 19.051 13.099V18.1406Z" fill="#B2B2C1" />
                            </g>
                            <defs>
                                <clipPath id="clip0_4246_14322">
                                    <rect width="24" height="24" fill="white" />
                                </clipPath>
                            </defs>
                        </svg>
                    </a>




                </div>
            </div>
            {!betaSuccess && <div className=' w-[385px] md:w-[500px] md:h-[650px] h-[575px] bg-[rgba(13,16,46,0.78)] backdrop-blur-[20px] border-[1px] px-[20px] md:px-[40px] py-[15px] md:py-[30px] rounded-[16px] border-[#2E2E60] flex justify-around items-center flex-col'>
                <div className='basis-[28%]  h-full w-full flex flex-col gap-[16px]'>
                    <h1 className='font-[700] text-[26px] md:text-[32px] text-white'>Join the DrCode Beta! <br /> <span className='text-[#875BF8]'>AI-Powered API Testing</span></h1>
                    <p className='font-[400] text-[14px] text-[#D1D1E3]'>Be among the first to try AI powered API Testing. Sign up now for exclusive beta access.</p>
                </div>
                <div className='basis-[72%] h-full w-full flex flex-col justify-between items-center'>

                    <div className='h-[50px] w-full flex flex-col gap-[2px]'>

                        <label htmlFor="" className='text-white text-[14px]'><span className='text-[red] mr-[5px]'>*</span>Name</label>
                        {nameError && (
                            <p className="text-red-500 text-[10px] ml-2">{nameError}</p>
                        )}
                        <input
                            ref={inputRef}
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            className={`bg-transparent hover:border-[#6B6BAA] transition duration-200 border-[1px] ${nameError ? "border-[#CF1322] placeholder-[#CF1322]" : "border-[#2E2E60]"}  focus:outline-[#875BF8] text-[14px] font-[400] rounded-[8px] py-[4px] px-[8px]`}
                            type="text" placeholder='Your Name' autoFocus />

                    </div>

                    <div className='h-[50px] w-full flex flex-col gap-[2px]'>
                        <label htmlFor="" className='text-white text-[14px]'><span className='text-[red] mr-[5px]'>*</span>Email</label>
                        {emailError && (
                            <p className="text-red-500 text-[10px] ml-2">{emailError}</p>
                        )}
                        <input
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            className={`bg-transparent hover:border-[#6B6BAA] transition duration-200 border-[1px] ${nameError ? "border-[#CF1322] placeholder-[#CF1322]" : "border-[#2E2E60]"}  focus:outline-[#875BF8] text-[14px] font-[400] rounded-[8px] py-[4px] px-[8px]`}
                            type="text" placeholder='Your Email Address' />
                    </div>
                    <div className='h-[50px] w-full flex flex-col gap-[2px]'>
                        <label htmlFor="" className='text-white text-[14px]'>Phone</label>
                        <input
                            value={phone}
                            onChange={(e) => setPhone(e.target.value)}
                            className='bg-transparent hover:border-[#6B6BAA] transition duration-200 border-[1px] border-[#2E2E60] focus:outline-[#875BF8] text-[14px] font-[400] rounded-[8px] py-[4px] px-[8px]' type="text" placeholder='Phone Number' />
                    </div>
                    <div className='h-[50px] w-full flex flex-col gap-[2px]'>
                        <label htmlFor="" className='text-white text-[14px]'>Company</label>
                        <input
                            value={company}
                            onChange={(e) => setCompany(e.target.value)}
                            className='bg-transparent hover:border-[#6B6BAA] transition duration-200 border-[1px] border-[#2E2E60] focus:outline-[#875BF8] text-[14px] font-[400] rounded-[8px] py-[4px] px-[8px]' type="text" placeholder='Company Name' />
                    </div>
                    <div className='h-[50px] w-full flex flex-col gap-[2px]'>
                        <label htmlFor="" className='text-white text-[14px]'>Role/Designation</label>
                        <input
                            value={role}
                            onChange={(e) => setRole(e.target.value)}
                            className='bg-transparent hover:border-[#6B6BAA] transition duration-200 border-[1px] border-[#2E2E60] focus:outline-[#875BF8] text-[14px] font-[400] rounded-[8px] py-[4px] px-[8px]' type="text" placeholder='Your Role' />
                    </div>
                    <button
                        onClick={
                            submitHandler
                        }
                        className='w-full h-[30px] flex justify-center items-center flex-row rounded-[8px] bg-[#875BF8] text-white'>
                        <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12.9239 10.4469L14.0682 7.01403C15.0679 4.01511 15.5677 2.51564 14.7762 1.72413C13.9847 0.93262 12.4852 1.43244 9.4863 2.43208L6.05341 3.57638C3.63299 4.38318 2.42278 4.78659 2.07887 5.37815C1.75171 5.94091 1.75171 6.63596 2.07887 7.19872C2.42278 7.79028 3.63299 8.19369 6.05341 9.00049C6.44204 9.13004 6.63636 9.19481 6.79878 9.30354C6.95619 9.40892 7.09141 9.54414 7.19678 9.70155C7.30552 9.86397 7.37029 10.0583 7.49984 10.4469C8.30664 12.8673 8.71004 14.0775 9.3016 14.4215C9.86437 14.7486 10.5594 14.7486 11.1222 14.4215C11.7137 14.0776 12.1171 12.8673 12.9239 10.4469Z" stroke="white" stroke-width="1.5" />
                            <path d="M11.4835 6.0769C11.778 5.78563 11.7806 5.31077 11.4894 5.01625C11.1981 4.72174 10.7233 4.71911 10.4287 5.01038L11.4835 6.0769ZM7.78422 9.73541L11.4835 6.0769L10.4287 5.01038L6.72945 8.66889L7.78422 9.73541Z" fill="white" />
                        </svg>
                        Submit</button>
                    <a href='/' className='w-full h-[20px] flex justify-center items-center text-[14px] font-[500] text-[#875BF8]'>
                        Skip & Explore DrCode
                    </a>
                </div>
            </div>}
            {betaSuccess && <div className={`basis-[40%] md:basis-[50%] p-[40px] w-[380px] md:w-[475px] bg-[#1A1D3D99] backdrop-blur-[20px] border-[1px] border-[#1B1B41] rounded-[16px] overflow-hidden flex flex-col items-center justify-evenly 
        transition-opacity duration-500 ease-out ${visible ? "opacity-100" : "opacity-0"}`}>
                <svg width="61" height="60" viewBox="0 0 61 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="30.5" cy="30" r="30" fill="#15B097" />
                    <path d="M25.6002 42.0002L14.2002 30.6002L17.0502 27.7502L25.6002 36.3002L43.9502 17.9502L46.8002 20.8002L25.6002 42.0002Z" fill="white" />
                </svg>
                <h2 className="font-[700] text-[32px] text-white">You're In !</h2>
                <p className="text-center font-[400] text-[14px]">Thank you for joining the DrCode Beta. Click on the link below to begin your journey!</p>
                <a className="w-full h-[35px] bg-[#875BF8] rounded-[8px] text-white flex justify-center items-center cursor-pointer" href="/login">Try Out DrCode Now</a>

            </div>}
            <div className='basis-[5%]  w-[70%] md:w-[35%] h-full flex justify-evenly items-center flex-col md:flex-row'>
                <p className='text-[#9494A1] font-[400] text-[13px]'>© All rights reserved.</p>
                <div className="w-full md:w-[60%] flex justify-between items-center flex-row">
                    <a className='text-[#9494A1] font-[400] text-[13px] cursor-pointer' href='/contact-us'>Contact Us</a>
                    <a className='text-[#9494A1] font-[400] text-[13px] cursor-pointer' href='/terms-and-conditions'>Terms and Conditions</a>
                    <a className='text-[#9494A1] font-[400] text-[13px] cursor-pointer' href='/privacy-policy'>Privacy Policy</a>
                </div>
            </div>
        </div>
    )
}

export default BetaLanding
