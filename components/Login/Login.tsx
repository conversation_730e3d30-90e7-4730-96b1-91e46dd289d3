"use client";
import React from "react";
import { Fa<PERSON><PERSON><PERSON>, FaBitbucket, FaGoogle } from "react-icons/fa";
import { setDataOnLocalStorage } from "@/temp-utils/globalUtilities";
import { LOCAL_STORAGE_DATA_KEYS } from "@/temp-utils/Constants/localStorageDataModels";
import { useSearchParams } from "next/navigation";
import Image from "next/image";
import { NEXT_PUBLIC_DR_CODE_AUTH_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
const Login = () => {
  const searchParams = useSearchParams();
  const vscodeParam = searchParams.get("vscode");

  // Store the 'vscode' parameter in local storage, if it exists
  if (vscodeParam) {
    setDataOnLocalStorage(LOCAL_STORAGE_DATA_KEYS.VSCODE, vscodeParam);
  }

  console.log("log:: search params ", searchParams.get("redirect-page"));
  const handleLogin = async (carrier: string) => {
    const isAudit = searchParams.get("redirect-page") === "audit";
    const apiURL = isAudit
      ? `${NEXT_PUBLIC_DR_CODE_AUTH_BASE_API_URL}bucket=auth&operationName=login&carrier=${carrier}&is_audit=1`
      : `${NEXT_PUBLIC_DR_CODE_AUTH_BASE_API_URL}bucket=auth&operationName=login&carrier=${carrier}`;
    setDataOnLocalStorage(LOCAL_STORAGE_DATA_KEYS.CARRIER, carrier);
    setDataOnLocalStorage(
      LOCAL_STORAGE_DATA_KEYS.IS_AUDIT_LOGIN,
      isAudit ? "1" : "0"
    );

    try {
      // Make a request to the loginWithGithub endpoint
      const response = await fetch(apiURL, {
        method: "GET",
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Get the redirect URL from the response
      const responseData = await response.json();

      console.log("log:: response data is ", responseData);

      // Get the redirect URL from the response
      const githubAuthUrl = isAudit
        ? `${responseData.data.url}&is_audit=1`
        : responseData.data.url;

      // Redirect the browser to GitHub for authentication
      window.location.href = githubAuthUrl;
    } catch (error) {
      console.error("Error initiating GitHub login:", error);
    }
  };

  return (
    <div className="relative min-h-screen bg-[#080814] bg-[url('/login-blur.png')] bg-cover bg-center backdrop-blur-md">
      <div className="flex justify-center items-center min-h-screen">
        <div className="bg-[#131330] shadow-md rounded-3xl mt-4 backdrop-blur-lg py-8 px-14 h-full">
          <div className="text-center space-y-4 ">
            <div className="flex items-center ml-5 sm:mb-0">
              <Image
                src={"/Logo.svg"}
                width={180}
                height={50}
                alt="DrCode.ai"
                className="h-100 w-100 mr-2"
              />
              {/*<h1 className="text-3xl font-semibold text-white font-spaceGrotesk">DrCode AI</h1>*/}
            </div>
            <p className="text-white">Get started in minutes</p>
            <div className="space-y-4">
              <button
                onClick={() => handleLogin("github")}
                className="flex items-center justify-center w-full text-white px-6 py-2 rounded-xl shadow-lg border border-[#2D2D45] border-solid"
              >
                <FaGithub className="mr-3" />
                Continue with GitHub
              </button>
              <button
                onClick={() => handleLogin("bitbucket")}
                className="flex items-center justify-center w-full text-white px-6 py-2 rounded-xl shadow-lg border border-[#2D2D45] border-solid"
              >
                <FaBitbucket className="mr-3" />
                Continue with BitBucket
              </button>
              <button
                onClick={() => handleLogin("google")}
                className="flex items-center justify-center w-full text-white px-6 py-2 rounded-xl shadow-lg border border-[#2D2D45] border-solid"
              >
                <FaGoogle className="mr-3" />
                Continue with Google
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
