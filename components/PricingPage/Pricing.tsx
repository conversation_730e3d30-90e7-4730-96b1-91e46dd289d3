import React from "react";
import Header from "../landing/Headers";
import PriceBox from "./PriceBox";
import Footer from "../landing/Footer";
import Banner from "./Banner";
import FAQ from "./FAQ";
import FeatureComparisonTable from "./FeatureComparisonTable";
import PriceTable from "./PriceTable";
import HeroTxt from "../common/hero-txt/HeroTxt";


const Pricing = () => {
  return (
    <div className="w-full h-full pb-10 ">
      <div
        className="w-full pt-2"
        style={{
          background:
            "linear-gradient(180deg, rgba(77, 104, 255, 0.15) 0%, rgba(10, 10, 31, 0) 100%)",
        }}
      >
        <Header />
        <HeroTxt
          title="Our pricing"
          para="Simple and flexible per-user pricing"
          subtext="Free for open source projects"
        />
      </div>
      <PriceBox />
      <PriceTable />
      <FeatureComparisonTable />
      <FAQ />
      <div className="my-32">
        <Banner />
      </div>
      <Footer />
    </div>
  );
};

export default Pricing;
