import { DiffData } from './types';

export interface DiffOptions {
    title?: string;
    beforeLabel?: string;
    afterLabel?: string;
    type?: 'json' | 'text' | 'object';
}

/**
 * Creates diff data for step request edits
 */
export const createStepRequestDiff = (
    currentRequest: unknown,
    updatedRequest: unknown,
    stepName: string,
    options: DiffOptions = {}
): DiffData => {
    return {
        before: currentRequest,
        after: updatedRequest,
        type: options.type || 'json',
        title: options.title || `Edit Step Request: ${stepName}`,
        beforeLabel: options.beforeLabel || 'Current Request',
        afterLabel: options.afterLabel || 'Updated Request'
    };
};

/**
 * Creates diff data for API configuration changes
 */
export const createApiConfigDiff = (
    currentConfig: unknown,
    updatedConfig: unknown,
    apiName: string,
    options: DiffOptions = {}
): DiffData => {
    return {
        before: currentConfig,
        after: updatedConfig,
        type: options.type || 'json',
        title: options.title || `Edit API Configuration: ${apiName}`,
        beforeLabel: options.beforeLabel || 'Current Configuration',
        afterLabel: options.afterLabel || 'Updated Configuration'
    };
};

/**
 * Creates diff data for text-based changes
 */
export const createTextDiff = (
    currentText: string,
    updatedText: string,
    title: string,
    options: DiffOptions = {}
): DiffData => {
    return {
        before: currentText,
        after: updatedText,
        type: 'text',
        title: options.title || title,
        beforeLabel: options.beforeLabel || 'Before',
        afterLabel: options.afterLabel || 'After'
    };
};

/**
 * Creates diff data for generic object changes
 */
export const createObjectDiff = (
    currentObject: unknown,
    updatedObject: unknown,
    title: string,
    options: DiffOptions = {}
): DiffData => {
    return {
        before: currentObject,
        after: updatedObject,
        type: options.type || 'object',
        title: options.title || title,
        beforeLabel: options.beforeLabel || 'Before',
        afterLabel: options.afterLabel || 'After'
    };
};

/**
 * Creates diff data for environment variable changes
 */
export const createEnvVarDiff = (
    currentEnvVars: Record<string, string>,
    updatedEnvVars: Record<string, string>,
    options: DiffOptions = {}
): DiffData => {
    return {
        before: currentEnvVars,
        after: updatedEnvVars,
        type: 'json',
        title: options.title || 'Environment Variables Changes',
        beforeLabel: options.beforeLabel || 'Current Variables',
        afterLabel: options.afterLabel || 'Updated Variables'
    };
};

/**
 * Creates diff data for database schema changes
 */
export const createSchemaDiff = (
    currentSchema: unknown,
    updatedSchema: unknown,
    tableName: string,
    options: DiffOptions = {}
): DiffData => {
    return {
        before: currentSchema,
        after: updatedSchema,
        type: 'json',
        title: options.title || `Schema Changes: ${tableName}`,
        beforeLabel: options.beforeLabel || 'Current Schema',
        afterLabel: options.afterLabel || 'Updated Schema'
    };
};

interface DiffChange {
    type: 'added' | 'removed' | 'modified';
    message: string;
}

interface TextDiffLine {
    type: 'added' | 'removed' | 'unchanged';
    content: string;
}

export const formatJsonDiff = (before: unknown, after: unknown): DiffChange[] => {
    console.log('\n=== Formatting JSON Diff ===');
    console.log('Before:', before);
    console.log('After:', after);
    
    const changes: DiffChange[] = [];
    
    // Handle null/undefined cases
    if (!before) before = {};
    if (!after) after = {};
    
    // Convert to objects if they're strings
    const beforeObj = typeof before === 'string' ? JSON.parse(before) : before;
    const afterObj = typeof after === 'string' ? JSON.parse(after) : after;
    
    // Get all keys from both objects
    const allKeys = new Set([
        ...Object.keys(beforeObj as object || {}),
        ...Object.keys(afterObj as object || {})
    ]);
    
    allKeys.forEach(key => {
        const beforeVal = (beforeObj as Record<string, unknown>)?.[key];
        const afterVal = (afterObj as Record<string, unknown>)?.[key];
        
        if (!(key in (beforeObj as object))) {
            changes.push({
                type: 'added',
                message: `Added "${key}": ${JSON.stringify(afterVal)}`
            });
        } else if (!(key in (afterObj as object))) {
            changes.push({
                type: 'removed',
                message: `Removed "${key}": ${JSON.stringify(beforeVal)}`
            });
        } else if (JSON.stringify(beforeVal) !== JSON.stringify(afterVal)) {
            changes.push({
                type: 'modified',
                message: `Changed "${key}": ${JSON.stringify(beforeVal)} → ${JSON.stringify(afterVal)}`
            });
        }
    });
    
    console.log('Generated changes:', changes);
    return changes;
};

export const formatTextDiff = (before: string, after: string): TextDiffLine[] => {
    console.log('\n=== Formatting Text Diff ===');
    console.log('Before:', before);
    console.log('After:', after);
    
    const beforeLines = before.split('\n');
    const afterLines = after.split('\n');
    const diff: TextDiffLine[] = [];
    
    // Simple line-by-line comparison
    const maxLines = Math.max(beforeLines.length, afterLines.length);
    
    for (let i = 0; i < maxLines; i++) {
        const beforeLine = beforeLines[i];
        const afterLine = afterLines[i];
        
        if (beforeLine === undefined) {
            diff.push({
                type: 'added',
                content: afterLine
            });
        } else if (afterLine === undefined) {
            diff.push({
                type: 'removed',
                content: beforeLine
            });
        } else if (beforeLine !== afterLine) {
            diff.push({
                type: 'removed',
                content: beforeLine
            });
            diff.push({
                type: 'added',
                content: afterLine
            });
        } else {
            diff.push({
                type: 'unchanged',
                content: beforeLine
            });
        }
    }
    
    console.log('Generated diff:', diff);
    return diff;
}; 