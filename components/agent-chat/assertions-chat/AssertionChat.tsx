import React, { useState } from 'react';
import SuggestionsInput, { MentionsData } from "./SuggestionsInput";

interface AssertionChatProps {
    suggestionsOptions: MentionsData[];
    prompt: string;
    setPrompt: (prompt: string) => void;
    setPromptIds: (ids: string[]) => void;
    customAssertionsIds: string[];
    handleGenerateAssertions: () => void;
    setMentionsData: (mentionsData: MentionsData[]) => void;
}

interface ChatHistory {
    id: string | number;
    title: string;
    content: string;
    sendBy: 'user' | 'agent';
}

const AssertionChat: React.FC<AssertionChatProps> = ({
    suggestionsOptions,
    prompt,
    setPrompt,
    setPromptIds,
    customAssertionsIds,
    handleGenerateAssertions,
    setMentionsData
}) => {
    const [chatHistory, setChatHistory] = useState<ChatHistory[]>([]);
return (
        <div className="h-full flex flex-col justify-between">

            <div className="h-[90%]">
                <div className="text-[#D9D9E8] text-[16px] font-semibold">
                    Custom Assertions
                </div>
                <div className="text-[#9494A1] text-[14px] font-normal">
                    Custom Assertions are assertions that are not part of the default assertions.
                </div>

            </div>



            <div className="mt-auto relative ">
                <div className="flex flex-col gap-2">
                    {
                        chatHistory.map((item) => (
                            <div key={item.id} className={`flex  items-center gap-1 my-2 ${item.sendBy === "user" ? "flex-row-reverse" : "flex-row"}`}>
                                <div className={`text-[#D9D9E8] text-[12px]  font-semibold bg-[#1B1B41] rounded-full w-8 h-8 flex items-center justify-center ${item.sendBy === "user" ? "bg-[#875BF8] text-white" : "bg-[#1B1B41] text-[#9494A1]"}`}>{item.sendBy.slice(0, 1).toUpperCase()}</div>
                                <div className={`text-[#9494A1] text-[14px] font-normal ${item.sendBy === "user" ? "bg-[#1B1B41] rounded-lg p-2 text-right" : "bg-[#1B1B41] rounded-lg p-2"}`}>{item.content}</div>
                            </div>
                        ))
                    }
                </div>

                <div className="flex gap-2 items-center">

                    <SuggestionsInput suggestionsOptions={suggestionsOptions} text={prompt} setText={setPrompt} setSuggestionsValues={setPromptIds} suggestionsValues={customAssertionsIds} setMentionsData={setMentionsData} />
                    <button onClick={() => {

                        handleGenerateAssertions()
                        setChatHistory([...chatHistory, {
                            id: chatHistory.length + 1,
                            title: "User",
                            content: prompt,
                            sendBy: "user"
                        }, {
                            id: chatHistory.length + 2,
                            title: "Agent",
                            content: "Generating assertions...",
                            sendBy: "agent"
                        }])

                        setPrompt("")

                    }} className='bg-[#875BF8] rounded-full p-2 text-white -rotate-45 text-sm'>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-6 h-6">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M6 12 3.269 3.126A59.768 59.768 0 0 1 21.485 12 59.768 59.768 0 0 1 3.27 20.874L5.999 12Zm0 0h7.5" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    )
}

export default AssertionChat;