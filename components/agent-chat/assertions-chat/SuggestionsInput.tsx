import React, { useState, useRef, useEffect } from "react";
// import { MentionData } from "../types";

// export type Mention = {
//   id: string | number;
//   text: string;
//   displayName: string;
//   type: "flow" | "step" | "sequence";
// };

export type MentionsData = {
  id: string | number;
  type: "flow" | "step" | "sequence";
  flowId?: string | number;
  stepId?: string | number;
  sequenceId: string | number;
  displayName: string;
  text: string;
};

const SuggestionsInput = ({
  suggestionsOptions,
  text,
  setText,
  setSuggestionsValues,
  suggestionsValues,
  setMentionsData,
}: {
  suggestionsOptions: MentionsData[];
  text: string;
  setText: (text: string) => void;
  setSuggestionsValues: (value: string[]) => void;
  suggestionsValues: string[];
  setMentionsData: (mentionsData: MentionsData[]) => void;
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<MentionsData[]>([]);
  const [cursor, setCursor] = useState(0);
  const [dropAbove, setDropAbove] = useState(false);
  const [selectedMentions, setSelectedMentions] = useState<MentionsData[]>([]);
  const inputRef = useRef<HTMLTextAreaElement>(null);

//   console.log(suggestionsOptions);

  const suggestionList: MentionsData[] = suggestionsOptions;

  const checkDropdownPosition = () => {
    if (inputRef.current) {
      const rect = inputRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;
      const threshold = 150; // suggestion box height

      setDropAbove(spaceBelow < threshold && spaceAbove > threshold);
    }
  };

  useEffect(() => {
    window.addEventListener("resize", checkDropdownPosition);
    return () => window.removeEventListener("resize", checkDropdownPosition);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const val = e.target.value;
    setText(val);
    checkDropdownPosition();

    // Check if any mentions were removed
    // Extract mentions by finding which selected mentions are still present in the text
    const currentMentionTexts: string[] = [];
    
    // Check each selected mention to see if it's still in the text
    selectedMentions.forEach((mention) => {
      const mentionPattern = `@${mention.text}`;
      if (val.includes(mentionPattern)) {
        currentMentionTexts.push(mention.text);
      }
    });

    console.log("DEBUG: text value:", val);
    console.log("DEBUG: currentMentionTexts:", currentMentionTexts);
    console.log("DEBUG: selectedMentions:", selectedMentions.map(m => m.text));

    // Filter out mentions that are no longer present in the text
    const updatedSelectedMentions = selectedMentions.filter((mention) => {
      const isPresent = currentMentionTexts.includes(mention.text);
      console.log(`DEBUG: checking mention "${mention.text}" - present: ${isPresent}`);
      return isPresent;
    });

    console.log("DEBUG: updatedSelectedMentions length:", updatedSelectedMentions.length, "vs selectedMentions length:", selectedMentions.length);

    // Only update state if there's actually a change
    if (updatedSelectedMentions.length !== selectedMentions.length) {
      setSelectedMentions(updatedSelectedMentions);
      const newSuggestionsValues = updatedSelectedMentions.map((m) =>
        m.id.toString()
      );
      setSuggestionsValues(newSuggestionsValues);
      console.log("log:: updatedSelectedMentions is ", newSuggestionsValues, updatedSelectedMentions);
      setMentionsData(updatedSelectedMentions);
    }
    
    if (inputRef.current) {
      const cursorPos = inputRef.current.selectionStart;
      const textBeforeCursor = val.slice(0, cursorPos);
      const match = textBeforeCursor.match(/@([a-zA-Z0-9_-]*)$/);

      if (match) {
        const query = match[1].toLowerCase();
        const matched = suggestionList.filter((item) =>
          item.text.toLowerCase().startsWith(query)
        );
        setFilteredSuggestions(matched);
        setShowSuggestions(true);
        setCursor(0);
      } else {
        setShowSuggestions(false);
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (!showSuggestions) return;

    if (e.key === "ArrowDown") {
      e.preventDefault();
      setCursor((prev) => (prev + 1) % filteredSuggestions.length);
    } else if (e.key === "ArrowUp") {
      e.preventDefault();
      setCursor(
        (prev) =>
          (prev - 1 + filteredSuggestions.length) % filteredSuggestions.length
      );
    } else if (e.key === "Enter") {
      e.preventDefault();
      handleSelect(filteredSuggestions[cursor]);
    }
  };

  // const handleSelect = (mention: Mention) => {
  //     const words = text.trimEnd().split(" ");
  //     const newWords = [...words];
  //     const lastWord = words[words.length - 1];

  //     if (lastWord.startsWith("@")) {
  //         newWords[words.length - 1] = `@${mention.text}`;
  //     }

  //     const newText = newWords.join(" ") + " ";
  //     setText(newText);
  //     setShowSuggestions(false);
  //     setFilteredSuggestions([]);

  //     // Add the selected mention to our tracking state (avoid duplicates)
  //     const isAlreadySelected = selectedMentions.some(m => m.id === mention.id);
  //     if (!isAlreadySelected) {
  //         const updatedSelectedMentions = [...selectedMentions, mention];
  //         setSelectedMentions(updatedSelectedMentions);

  //         // Update suggestionsValues with the IDs of all selected mentions
  //         const newSuggestionsValues = updatedSelectedMentions.map(m => m.id.toString());
  //         setSuggestionsValues(newSuggestionsValues);

  //     }
  // };



  const handleSelect = (mention: MentionsData) => {
    if (!inputRef.current) return;

    const textarea = inputRef.current;
    const cursorPos = textarea.selectionStart;
    const textBefore = text.slice(0, cursorPos);
    const textAfter = text.slice(cursorPos);

    // Match "@mention" just before the cursor
    const mentionMatch = textBefore.match(/@([a-zA-Z0-9_-]*)$/);

    if (mentionMatch) {
      const matchStart = cursorPos - mentionMatch[0].length;
      const newTextBefore =
        textBefore.slice(0, matchStart) + "@" + mention.text;
      const newText = newTextBefore + " " + textAfter;

      setText(newText);
      setShowSuggestions(false);
      setFilteredSuggestions([]);

      // Move cursor to just after the inserted mention
      setTimeout(() => {
        const newCursorPos = newTextBefore.length + 1; // +1 for space
        textarea.setSelectionRange(newCursorPos, newCursorPos);
        textarea.focus();
      }, 0);

      // Avoid duplicates
      const isAlreadySelected = selectedMentions.some(
        (m) => m.id === mention.id
      );
      if (!isAlreadySelected) {
        const updatedSelectedMentions = [...selectedMentions, mention];
        setSelectedMentions(updatedSelectedMentions);
        setSuggestionsValues(
          updatedSelectedMentions.map((m) => m.id.toString())
        );
        console.log("log:: selected mentions is ", updatedSelectedMentions);
        setMentionsData(updatedSelectedMentions);
      }
    }
  };

  return (
    <div className="relative w-full">
      <textarea
        ref={inputRef}
        value={text}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        placeholder="Type @ to mention someone..."
        className="w-full p-2 text-sm text-[#E2E2ED] bg-[#1B1B41] rounded h-16"
        onFocus={checkDropdownPosition}
      />
      {showSuggestions && filteredSuggestions.length > 0 && (
        <ul
          className={`absolute z-10 bg-[#131330] border shadow rounded w-full max-h-40 overflow-auto
          ${dropAbove ? "bottom-full mb-2" : "top-full mt-1"}`}
        >
          {filteredSuggestions.map((item, idx) => (
            <li
              key={item.id}
              onClick={() => handleSelect(item)}
              className={`px-4 py-2  flex justify-between cursor-pointer ${
                idx === cursor
                  ? "bg-[#2E2F52] text-white"
                  : "hover:bg-[#2E2E60]"
              }`}
            >
              <span className="text-sm text-[#E2E2ED]">{item.displayName}</span>
              <span className="text-xs text-[#E2E2ED]">{item.type}</span>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default SuggestionsInput;
