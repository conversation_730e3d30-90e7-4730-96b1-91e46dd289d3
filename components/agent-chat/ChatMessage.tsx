import React, { useState, useEffect } from 'react';
import { Message } from './types';
import { MarkdownContent } from './MarkdownContent';
import { ToolCallApproval } from './ToolCallApproval';
import { BsArrowDown, BsArrowUp } from 'react-icons/bs';


interface ChatMessageProps {
    message: Message;
    onToolApproval?: (approved: boolean) => void;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message, onToolApproval }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    
    // Auto-expand tool calls that need approval
    useEffect(() => {
        if (message.role === 'tool-call' && message.pending && onToolApproval) {
            setIsExpanded(true);
        }
    }, [message.role, message.pending, onToolApproval]);

    console.log('\n=== ChatMessage Render Debug ===');
    console.log('Message type:', message.role);
    console.log('Has diffData:', !!message.diffData);
    if (message.diffData) {
        console.log('Diff Data in Message:', {
            type: message.diffData.type,
            hasBeforeData: !!message.diffData.before,
            hasAfterData: !!message.diffData.after,
            title: message.diffData.title
        });
    }

    let bgColor = "bg-[#0D0E26]";
    let justifyClass = "justify-start";
    let toolIcon = "";
    let borderColor = "border-[#1E1F42]";
    let textColor = "text-gray-100";
    
    switch (message.role) {
        case 'user':
            bgColor = "bg-[#0D0E26]";
            justifyClass = "justify-end";
            borderColor = "border-[#2E2F52]";
            break;
        case 'agent':
            bgColor = "bg-transparent";
            borderColor = "border-transparent";
            break;
        case 'tool-call':
            bgColor = message.pending ? "bg-amber-900/20" : "bg-[#0D0E26]";
            borderColor = message.pending ? "border-amber-500" : "border-indigo-700";
            toolIcon = "🔧 ";
            break;
        case 'tool-execution':
            bgColor = "bg-[#161729]";
            borderColor = "border-blue-700";
            toolIcon = "⚙️ ";
            break;
        case 'tool-result':
            bgColor = "bg-[#0D0E26]";
            borderColor = "border-green-700";
            toolIcon = "✅ ";
            break;
        case 'tool-error':
            bgColor = "bg-[#1E1525]";
            borderColor = "border-red-700";
            toolIcon = "❌ ";
            break;
        case 'agent-action':
            bgColor = "bg-[#0D0E26]";
            borderColor = "border-purple-700";
            toolIcon = "🤖 ";
            textColor = "text-purple-200";
            break;
    }
    
    // Format the content for tool-related messages
    const content = message.content;
    let subtitle = '';
    const isToolRelated = message.role.startsWith('tool-') || message.role === 'agent-action';
    let summaryContent = '';

    if (isToolRelated) {
        if (message.toolName) {
            subtitle = `Tool: ${message.toolName}\n${message.toolDescription || ''}`;
            summaryContent = message.toolName;
        } else {
            summaryContent = content.split('\n')[0];
        }
    }

    const messageWidth = message.role === 'agent' ? 'w-full' : 'w-full';
    
    const renderToolCall = () => {
        console.log('\n=== Tool Call Render Debug ===');
        console.log('Rendering tool call with diffData:', !!message.diffData);
        
        return (
            <div className="bg-gray-800 rounded-lg p-4 text-gray-300 border border-gray-700">
                <ToolCallApproval
                    toolName={message.toolName || ''}
                    toolDescription={message.toolDescription || ''}
                    toolArgs={message.toolArgs || {}}
                    content={message.content}
                    comparisonMessage={message.comparisonMessage}
                    diffData={message.diffData}
                    onApprove={() => onToolApproval?.(true)}
                    onReject={() => onToolApproval?.(false)}
                />
            </div>
        );
    };

    if (isToolRelated) {
        return (
            <div className={`flex ${justifyClass} mb-3`}>
                <div className={`${messageWidth} rounded-lg ${bgColor} ${textColor} border ${borderColor} shadow-md overflow-hidden`}>
                    <button 
                        onClick={() => setIsExpanded(!isExpanded)}
                        className="w-full px-3 py-2 flex items-center justify-between hover:bg-opacity-50 hover:bg-gray-700 transition-colors"
                    >
                        <div className="flex items-center space-x-2">
                            <span>{toolIcon}</span>
                            <span className="font-medium">{summaryContent}</span>
                            {message.role === 'tool-call' && message.pending && onToolApproval && (
                                <span className="ml-2 text-amber-400 animate-pulse">Action Required</span>
                            )}
                        </div>
                        {isExpanded ? <BsArrowUp size={16} /> : <BsArrowDown size={16} />}
                    </button>
                    
                    {isExpanded  &&  (
                        <div className="px-3 py-2 border-t border-gray-700">
                            {/* <div className="font-medium whitespace-pre-wrap">{content}</div> */}
                            {subtitle && (
                                <div className="mt-2 text-sm opacity-90 whitespace-pre-line">
                                    {/* {subtitle} */}
                                </div>
                            )}
                            
                            {message.role === 'tool-call' && message.pending && onToolApproval && (
                                <div className="mt-4">
                                    {renderToolCall()}
                                </div>
                            )}
                            
                            <div className="text-xs opacity-50 mt-2 text-right">
                                {new Date(message.created_at).toLocaleTimeString()}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        );
    }

    return (
        <div className={`flex ${justifyClass} mb-3`}>
            <div className={`${messageWidth} rounded-lg ${bgColor} ${textColor} border ${borderColor} shadow-md overflow-hidden`}>
                <div className="px-3 py-2">
                    <MarkdownContent content={content} />
                    <div className="text-xs opacity-50 mt-2 text-right">
                        {new Date(message.created_at).toLocaleTimeString()}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ChatMessage; 