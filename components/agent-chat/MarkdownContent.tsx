import React from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface MarkdownContentProps {
    content: string;
}

// Helper function to format content
const formatContent = (content: string): string => {
    // Remove any unnecessary prefixes
    content = content.replace(/^Tool (.*?) result: /, '');
    content = content.replace(/^Error using tool (.*?): /, '');
    
    // Convert lists to markdown
    content = content.replace(/^(\s*)-\s+/gm, '$1* ');
    
    // Remove period after backtick code, e.g., `dev`.
    content = content.replace(/(`[^`]+`)\./g, '$1');
    content = content.replace(/(`[^`]+`)\,/g, '$1');

    // Handle inline code blocks (single backticks)
    content = content.replace(/(?<!`)`([^`\n]+)`(?!`)/g, '```$1```');
    
    // Handle multiline code blocks
    content = content.replace(/```([^`]+)```/g, (match, code) => {
        if (code.includes('\n')) {
            return '\n```\n' + code.trim() + '\n```\n';
        }
        return match;
    });
    
    return content;
};

export const MarkdownContent: React.FC<MarkdownContentProps> = ({ content }) => {
    return (
        <div className="prose prose-invert max-w-none">
            <ReactMarkdown
                components={{
                    code: ({ inline, className, children, ...props }: { 
                        inline?: boolean;
                        className?: string;
                        children: React.ReactNode;
                    }) => {
                        const match = /language-(\w+)/.exec(className || '');
                        const language = match ? match[1] : 'text';
                        
                        if (inline) {
                            return (
                                <code className="bg-gray-800 px-1 py-0.5 rounded text-sm" {...props}>
                                    {children}
                                </code>
                            );
                        }
                        
                        return (
                            <SyntaxHighlighter
                                language={language}
                                PreTag="div"
                                showLineNumbers
                                wrapLines
                                style={oneDark}
                            >
                                {String(children).replace(/\n$/, '')}
                            </SyntaxHighlighter>
                        );
                    },
                    p: ({ children }) => <p className="mb-2">{children}</p>,
                    ul: ({ children }) => <ul className="list-disc ml-4 mb-2">{children}</ul>,
                    ol: ({ children }) => <ol className="list-decimal ml-4 mb-2">{children}</ol>,
                    li: ({ children }) => <li className="mb-1">{children}</li>,
                    h1: ({ children }) => <h1 className="text-xl font-bold mb-2">{children}</h1>,
                    h2: ({ children }) => <h2 className="text-lg font-bold mb-2">{children}</h2>,
                    h3: ({ children }) => <h3 className="text-md font-bold mb-2">{children}</h3>,
                    blockquote: ({ children }) => (
                        <blockquote className="border-l-4 border-gray-600 pl-4 italic my-2">
                            {children}
                        </blockquote>
                    ),
                }}
            >
                {formatContent(content)}
            </ReactMarkdown>
        </div>
    );
};

export default MarkdownContent; 