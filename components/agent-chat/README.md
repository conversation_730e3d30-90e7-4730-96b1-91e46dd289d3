# Agent Chat Diff System

This document explains how to use the modular diff system for tool call approvals in the agent chat interface.

## Overview

The diff system allows you to show before/after comparisons when tools request changes to data. It's designed to be modular and reusable for different types of tools and data modifications.

## Components

### DiffViewer
The main component that renders the diff visualization.

```typescript
import { DiffViewer, DiffData } from './DiffViewer';

const diffData: DiffData = {
    before: originalData,
    after: modifiedData,
    type: 'json', // 'json' | 'text' | 'object'
    title: 'Changes to Configuration',
    beforeLabel: 'Current',
    afterLabel: 'Proposed'
};

<DiffViewer diffData={diffData} />
```

### DiffData Interface
```typescript
interface DiffData {
    before: unknown;           // Original data
    after: unknown;            // Modified data
    type: 'json' | 'text' | 'object';  // How to format the data
    title?: string;            // Optional title for the diff
    beforeLabel?: string;      // Label for the "before" section
    afterLabel?: string;       // Label for the "after" section
}
```

## Utility Functions

The `diffUtils.ts` file provides helper functions for common diff scenarios:

### Step Request Changes
```typescript
import { createStepRequestDiff } from './diffUtils';

const diffData = createStepRequestDiff(
    currentRequest,
    updatedRequest,
    stepName,
    { 
        title: 'Custom Title',
        beforeLabel: 'Original',
        afterLabel: 'Modified'
    }
);
```

### API Configuration Changes
```typescript
import { createApiConfigDiff } from './diffUtils';

const diffData = createApiConfigDiff(
    currentConfig,
    updatedConfig,
    apiName
);
```

### Text-based Changes
```typescript
import { createTextDiff } from './diffUtils';

const diffData = createTextDiff(
    originalText,
    modifiedText,
    'Script Changes'
);
```

### Generic Object Changes
```typescript
import { createObjectDiff } from './diffUtils';

const diffData = createObjectDiff(
    originalObject,
    modifiedObject,
    'Object Modifications'
);
```

## Backend Integration

### In Tool Classes
When creating a tool that modifies data, include diff data in the approval request:

```javascript
// In your tool's _call method
const diffData = {
    before: currentData,
    after: modifiedData,
    type: 'json',
    title: `Edit ${resourceName}`,
    beforeLabel: 'Current',
    afterLabel: 'Proposed'
};

const approved = await requestToolApproval(
    runManager.socket,
    this.name,
    { ...args, diffData },
    this.description
);
```

### In requestToolApproval Function
The function automatically extracts and forwards diffData:

```javascript
// Add diffData if it exists in args
if (args.diffData) {
    toolCallData.diffData = args.diffData;
    // Remove diffData from args to avoid duplication
    const { diffData, ...argsWithoutDiff } = args;
    toolCallData.args = argsWithoutDiff;
}
```

## Frontend Integration

### Message Types
Add diffData to your Message interface:

```typescript
interface Message {
    // ... other properties
    diffData?: DiffData;
}
```

### Socket Event Handling
The socket event handler automatically includes diffData:

```typescript
socket.on('agent-tool-call', (data) => {
    const toolCallMessage: Message = {
        // ... other properties
        diffData: data.diffData, // Include diff data
    };
});
```

### Tool Call Approval Component
The ToolCallApproval component automatically uses DiffViewer when diffData is present:

```typescript
{diffData ? (
    <DiffViewer diffData={diffData} className="mb-4" />
) : (
    renderComparisonMessage()
)}
```

## Features

### Change Summary
The DiffViewer automatically generates a summary of changes showing:
- Added fields (+ indicator)
- Removed fields (- indicator)
- Modified fields (~ indicator)

### Visual Styling
- Red background for "before" sections
- Green background for "after" sections
- Color-coded change indicators
- Collapsible sections for large diffs

### Data Type Support
- **JSON**: Pretty-printed JSON with syntax highlighting
- **Text**: Plain text comparison
- **Object**: JavaScript object formatting

## Adding New Tool Types

To add diff support for a new tool type:

1. **Create a utility function** in `diffUtils.ts`:
```typescript
export const createMyToolDiff = (
    currentData: MyDataType,
    updatedData: MyDataType,
    resourceName: string,
    options: DiffOptions = {}
): DiffData => {
    return {
        before: currentData,
        after: updatedData,
        type: 'json',
        title: options.title || `Edit ${resourceName}`,
        beforeLabel: options.beforeLabel || 'Current',
        afterLabel: options.afterLabel || 'Proposed'
    };
};
```

2. **Use it in your backend tool**:
```javascript
const diffData = createMyToolDiff(currentData, updatedData, resourceName);
const approved = await requestToolApproval(
    runManager.socket,
    this.name,
    { ...args, diffData },
    this.description
);
```

3. **No frontend changes needed** - the system automatically handles any tool with diffData.

## Best Practices

1. **Always provide meaningful titles** that describe what's being changed
2. **Use appropriate data types** (json for structured data, text for strings)
3. **Keep before/after labels descriptive** but concise
4. **Include context** in the title (e.g., "Edit Step Request: Login API")
5. **Test with large datasets** to ensure the UI remains responsive

## Examples

See `DiffViewerDemo.tsx` for a complete example of the diff system in action. 