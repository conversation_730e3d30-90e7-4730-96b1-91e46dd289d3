import { useState, useEffect } from 'react';
import axios from 'axios';
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from '@/temp-utils/Constants/globalVariables';
import { MentionData, Sequence, StructuredMention } from '../types';

interface UseMentionsProps {
  projectId: string;
}

export const useMentions = ({ projectId }: UseMentionsProps) => {
  const [mentionState, setMentionState] = useState<{
    isOpen: boolean;
    searchTerm: string;
    triggerIdx: number;
  }>({
    isOpen: false,
    searchTerm: '',
    triggerIdx: -1
  });

  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const [sequences, setSequences] = useState<Sequence[]>([]);
  const [selectedMentions, setSelectedMentions] = useState<MentionData[]>([]);

  useEffect(() => {
    const fetchSequences = async () => {
      try {
        const response = await axios.get(
          `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/sequences/hierarchy/${projectId}`
        );
        if (response.data.success) {
          setSequences(response.data.sequences);
        }
      } catch (error) {
        console.error('Error fetching sequences hierarchy:', error);
      }
    };

    if (projectId) {
      fetchSequences();
    }
  }, [projectId]);

  const handleInputChange = (newValue: string, cursorPosition: number) => {
    if (mentionState.isOpen) {
      const text = newValue.slice(0, cursorPosition);
      const match = /@([\w\-]*)$/.exec(text);
      const term = match ? match[1] : '';
      setMentionState(prev => ({ ...prev, searchTerm: term }));
    }
  };

  const openMentionDropdown = (triggerIdx: number, position: { top: number; left: number }) => {
    setMentionState({
      isOpen: true,
      searchTerm: '',
      triggerIdx
    });
    setDropdownPosition(position);
  };

  const closeMentionDropdown = () => {
    setMentionState(prev => ({ ...prev, isOpen: false }));
  };

  const addMention = (mention: MentionData) => {
    setSelectedMentions(prev => [...prev, mention]);
    closeMentionDropdown();
  };

  const removeMention = (mentionName: string) => {
    setSelectedMentions(prev => prev.filter(m => m.name !== mentionName));
  };

  const getStructuredMentions = (): StructuredMention[] => {
    return selectedMentions.map(mention => {
      const pathParts = mention.path.split('/');
      const structured: StructuredMention = {
        type: mention.type,
        id: mention.id,
        name: mention.name
      };

      if (mention.type === 'step') {
        structured.sequenceId = pathParts[0];
        structured.flowId = pathParts[1];
        structured.stepId = Number(mention.id);
      } else if (mention.type === 'flow') {
        structured.sequenceId = pathParts[0];
        structured.flowId = String(mention.id);
      } else if (mention.type === 'sequence') {
        structured.sequenceId = String(mention.id);
      }

      return structured;
    });
  };

  return {
    mentionState,
    dropdownPosition,
    sequences,
    selectedMentions,
    handleInputChange,
    openMentionDropdown,
    closeMentionDropdown,
    addMention,
    removeMention,
    getStructuredMentions,
  };
}; 