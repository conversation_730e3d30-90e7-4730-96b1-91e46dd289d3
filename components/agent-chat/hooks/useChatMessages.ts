import { useState, useRef } from 'react';
import { Message } from '../types';

interface UseChatMessagesProps {
  onScrollToBottom?: () => void;
}

export const useChatMessages = ({ onScrollToBottom }: UseChatMessagesProps = {}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [streamingMessage, setStreamingMessage] = useState<string>('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const addMessage = (message: Message) => {
    setMessages(prev => [...prev, message]);
    onScrollToBottom?.();
  };

  const addStreamingChunk = (chunk: string) => {
    setStreamingMessage(prev => prev + chunk);
    setIsStreaming(true);
    onScrollToBottom?.();
  };

  const finalizeStreamingMessage = () => {
    if (streamingMessage) {
      addMessage({
        role: 'agent',
        content: streamingMessage,
        created_at: new Date().toISOString()
      });
      setStreamingMessage('');
    }
    setIsStreaming(false);
    setIsLoading(false);
  };

  const clearMessages = () => {
    setMessages([]);
    setStreamingMessage('');
    setIsStreaming(false);
    setIsLoading(false);
  };

  const setMessageList = (newMessages: Message[]) => {
    setMessages(newMessages);
    onScrollToBottom?.();
  };

  return {
    messages,
    streamingMessage,
    isStreaming,
    isLoading,
    messagesEndRef,
    addMessage,
    addStreamingChunk,
    finalizeStreamingMessage,
    clearMessages,
    setMessageList,
    setIsLoading,
  };
}; 