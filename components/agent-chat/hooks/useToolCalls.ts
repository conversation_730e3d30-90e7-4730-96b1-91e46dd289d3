import { useState } from 'react';
import { Message } from '../types';

export const useToolCalls = () => {
  const [pendingToolCall, setPendingToolCall] = useState<Message | null>(null);

  const handleToolCall = (message: Message) => {
    setPendingToolCall(message);
  };

  const handleToolExecution = () => {
    setPendingToolCall(null);
  };

  const handleToolApproval = (approved: boolean) => {
    if (pendingToolCall?.approvalHandler) {
      pendingToolCall.approvalHandler(approved);
      setPendingToolCall(null);
    }
  };

  const clearToolCall = () => {
    setPendingToolCall(null);
  };

  return {
    pendingToolCall,
    handleToolCall,
    handleToolExecution,
    handleToolApproval,
    clearToolCall,
  };
}; 