import { useEffect, useRef, useState } from 'react';
import { Socket, io } from 'socket.io-client';
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL, NEXT_PUBLIC_DR_CODE_SOCKET_URL } from '@/temp-utils/Constants/globalVariables';
import useToast from '@/hooks/useToast';
import { Message } from '../types';

interface UseSocketConnectionProps {
  projectId: string;
  onMessageReceived: (message: Message) => void;
  onStreamingUpdate: (token: string) => void;
  onStreamingComplete: (sessionId: string, fullResponse: string) => void;
  onToolCall: (message: Message) => void;
  onToolExecution: (message: Message) => void;
  onToolResult: (message: Message) => void;
  onToolError: (message: Message) => void;
}

interface SocketEvent {
  event: string;
  data: unknown;
  timestamp: number;
}

export const useSocketConnection = ({
  projectId,
  onMessageReceived,
  onStreamingUpdate,
  onStreamingComplete,
  onToolCall,
  onToolExecution,
  onToolResult,
  onToolError,
}: UseSocketConnectionProps) => {
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [events, setEvents] = useState<SocketEvent[]>([]);
  const { showToast } = useToast();

  useEffect(() => {
    const socketUrl = NEXT_PUBLIC_DR_CODE_SOCKET_URL;

    console.log("log:: socketUrl is ", socketUrl);
    
    try {
      const socket = io(socketUrl, {
        path: "/testgpt/socket.io", // 👈 must match this path exactly
        transports: ['websocket', 'polling'],
        reconnection: true,
        reconnectionAttempts: 10,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        timeout: 20000,
        forceNew: true,
      });
      

      // Connection events
      socketRef.current.on('connect', () => {
        setIsConnected(true);
        addEvent('connect', { id: socketRef.current?.id });
        showToast('Connected to agent service', 'success');
      });

      // Agent events
      socketRef.current.on('agent-ping', (data, callback) => {
        addEvent('agent-ping', data);
        if (typeof callback === 'function') {
          callback({ success: true, timestamp: Date.now() });
        }
      });

      socketRef.current.on('agent-tool-call', (data) => {
        const toolCallMessage: Message = {
          role: 'tool-call',
          content: `Tool Request: ${data.tool}`,
          created_at: new Date().toISOString(),
          toolName: data.tool,
          toolArgs: data.args,
          toolDescription: data.description,
          comparisonMessage: data.comparisonMessage,
          pending: true,
          approvalHandler: (approved: boolean) => {
            socketRef.current?.emit('tool-call-response', { 
              approved,
              tool: data.tool,
              args: data.args
            });
          }
        };
        onToolCall(toolCallMessage);
      });

      socketRef.current.on('agent-tool-execution', (data) => {
        if (!data.tool) return;
        
        onToolExecution({
          role: 'tool-execution',
          content: `Executing: ${data.tool}`,
          created_at: new Date().toISOString(),
          toolName: data.tool,
          toolArgs: data.args,
          toolDescription: data.description
        });
      });

      socketRef.current.on('agent-tool-result', (data) => {
        if (!data.tool) return;
        
        onToolResult({
          role: 'tool-result',
          content: data.result || `[No result data from ${data.tool}]`,
          created_at: new Date().toISOString(),
          toolName: data.tool
        });
      });

      socketRef.current.on('agent-tool-error', (data) => {
        onToolError({
          role: 'tool-error',
          content: `Error using tool ${data.tool}: ${data.error}`,
          created_at: new Date().toISOString(),
          toolName: data.tool
        });
      });

      socketRef.current.on('agent-response-chunk', ({ token }) => {
        if (!token) return;
        addEvent('agent-response-chunk', { tokenLength: token.length });
        onStreamingUpdate(token);
      });

      socketRef.current.on('agent-response-complete', ({ chatSessionId, fullResponse }) => {
        onStreamingComplete(chatSessionId, fullResponse);
      });

      socketRef.current.on('agent-error', (data) => {
        showToast(data.error || 'An error occurred during streaming', 'error');
        onToolError({
          role: 'tool-error',
          content: `Error: ${data.error || 'An unknown error occurred'}`,
          created_at: new Date().toISOString()
        });
      });

      // Error handling
      socketRef.current.on('connect_error', (err) => {
        showToast('Connection error: ' + err.message, 'error');
      });

      socketRef.current.on('disconnect', (reason) => {
        setIsConnected(false);
        addEvent('disconnect', { reason });
        
        if (reason === 'io server disconnect' || reason === 'transport close') {
          socketRef.current?.connect();
        }
      });

      socketRef.current.on('reconnect', (attemptNumber) => {
        showToast('Reconnected to agent service', 'success');
      });

      socketRef.current.on('reconnect_failed', () => {
        showToast('Failed to reconnect to agent service', 'error');
      });

      return () => {
        if (socketRef.current) {
          socketRef.current.disconnect();
        }
      };
    } catch (error) {
      console.error('Error setting up socket connection:', error);
      showToast('Failed to connect to agent service', 'error');
    }
  }, [projectId]);

  const addEvent = (event: string, data: unknown) => {
    setEvents(prev => [...prev, {
      event,
      data,
      timestamp: Date.now()
    }]);
  };

  return {
    socket: socketRef.current,
    isConnected,
    events,
  };
}; 