import { useState, useCallback } from 'react';
import axios from 'axios';
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from '@/temp-utils/Constants/globalVariables';
import { ChatSession, Message } from '../types';
import useToast from '@/hooks/useToast';

interface UseChatSessionsProps {
  projectId: string;
  userId: string;
  onMessagesLoaded: (messages: Message[]) => void;
}

export const useChatSessions = ({
  projectId,
  userId,
  onMessagesLoaded,
}: UseChatSessionsProps) => {
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { showToast } = useToast();

  const loadChatHistory = useCallback(async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/agent/history/${projectId}/${userId}`
      );
      if (response.data.success) {
        setChatSessions(response.data.history);
      }
    } catch (error) {
      console.error('Error loading chat history:', error);
      showToast('Failed to load chat history', 'error');
    }
  }, [projectId, userId]);

  const loadChatSession = async (sessionId: string) => {
    try {
      setIsLoading(true);
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/agent/session/${sessionId}`
      );
      if (response.data.success) {
        const processedMessages = response.data.messages.map((msg: {
          role: string;
          content: string;
          created_at: string;
          toolName?: string;
          toolArgs?: Record<string, unknown>;
          toolDescription?: string;
          metadata?: {
            toolName?: string;
            toolArgs?: Record<string, unknown>;
            toolDescription?: string;
          };
        }) => ({
          role: msg.role,
          content: msg.content,
          created_at: msg.created_at,
          toolName: msg.toolName || msg.metadata?.toolName,
          toolArgs: msg.toolArgs || msg.metadata?.toolArgs,
          toolDescription: msg.toolDescription || msg.metadata?.toolDescription
        }));
        
        onMessagesLoaded(processedMessages);
        setCurrentSessionId(sessionId);
      }
    } catch (error) {
      console.error('Error loading chat session:', error);
      showToast('Failed to load chat session', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const startNewSession = () => {
    setCurrentSessionId(null);
  };

  return {
    chatSessions,
    currentSessionId,
    isLoading,
    loadChatHistory,
    loadChatSession,
    startNewSession,
    setCurrentSessionId,
  };
}; 