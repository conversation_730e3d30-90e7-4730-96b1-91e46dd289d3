export interface Step {
    id: number;
    name: string;
    step_number: number;
}

export interface Flow {
    id: string;
    name: string;
    steps: Step[];
}

export interface Sequence {
    id: string;
    name: string;
    flows: Flow[];
}

export interface SelectableItem {
    id: string | number;
    name: string;
    type: 'sequence' | 'flow' | 'step';
    path: string;
}

export interface MentionData {
    id: string | number;
    type: 'sequence' | 'flow' | 'step';
    name: string;
    path: string;
}

export interface StructuredMention {
    type: 'sequence' | 'flow' | 'step';
    id: string | number;
    name: string;
    sequenceId?: string;
    flowId?: string;
    stepId?: number;
}

export interface DiffData {
    before: unknown;
    after: unknown;
    type: 'json' | 'text' | 'object';
    title?: string;
    beforeLabel?: string;
    afterLabel?: string;
}

export interface Message {
    role: 'user' | 'agent' | 'tool-call' | 'tool-result' | 'tool-error' | 'tool-execution' | 'agent-action';
    content: string;
    created_at: string;
    toolName?: string;
    toolArgs?: {
        projectId?: string;
        [key: string]: string | number | boolean | undefined;
    };
    toolDescription?: string;
    pending?: boolean;
    approvalHandler?: (approved: boolean) => void;
    comparisonMessage?: string;
    diffData?: DiffData;
}

export interface ChatSession {
    chat_session_id: string;
    session_start: string;
    message_count: number;
} 