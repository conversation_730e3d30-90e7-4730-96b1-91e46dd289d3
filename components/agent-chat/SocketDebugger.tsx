import React from 'react';

interface SocketDebuggerProps {
    socketId: string | undefined;
    events: Array<{event: string; data: unknown; timestamp: number}>;
    isConnected: boolean;
}

export const SocketDebugger: React.FC<SocketDebuggerProps> = ({ 
    socketId, 
    events, 
    isConnected 
}) => {
    return (
        <div className="fixed top-0 left-0 bg-gray-900 text-gray-300 p-2 text-xs z-50 opacity-80 max-h-[300px] overflow-auto border border-gray-800 rounded-bl-md">
            <div>Socket ID: {socketId || 'Not connected'}</div>
            <div>Connected: {isConnected ? 'Yes' : 'No'}</div>
            <div>Events:</div>
            <div className="text-[10px]">
                {events.slice(-10).map((evt, idx) => (
                    <div key={idx} className="border-t border-gray-800 pt-1 mt-1">
                        <div>{new Date(evt.timestamp).toLocaleTimeString()}: {evt.event}</div>
                        <div className="text-gray-500">{JSON.stringify(evt.data).substring(0, 50)}</div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default SocketDebugger; 