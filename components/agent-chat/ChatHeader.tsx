import React from 'react';

interface ChatHeaderProps {
    currentSessionId: string | null;
    onNewChat: () => void;
    onToggleSidebar: () => void;
    onToggleDebug: () => void;
    onClose: () => void;
    showDebug: boolean;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({
    currentSessionId,
    onNewChat,
    onToggleSidebar,
    onToggleDebug,
    onClose,

}) => {
    return (
        <div className="p-3 bg-[#1B1B41] flex items-center justify-between border-b border-[#1E1F42]">
            <div className="flex items-center gap-2">
                <span className="font-semibold text-white">
                    {currentSessionId ? 'Current Chat' : 'New Chat'}
                </span>
            </div>
            <div className="flex items-center gap-3">
                {/* New Chat */}
                <button 
                    onClick={onNewChat}
                    className="p-2 text-gray-300 hover:text-white hover:bg-[#1E1F42] rounded-full transition-colors"
                    title="New Chat"
                >
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M12 5v14M5 12h14" />
                    </svg>
                </button>
                {/* History */}
                <button 
                    onClick={onToggleSidebar}
                    className="p-2 text-gray-300 hover:text-white hover:bg-[#1E1F42] rounded-full transition-colors"
                    title="Chat History"
                >
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
                {/* Debug */}
                <button
                    onClick={onToggleDebug}
                    className="p-2 text-gray-300 hover:text-white hover:bg-[#1E1F42] rounded-full transition-colors"
                    title="Debug"
                >
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z" />
                        <polyline points="13 2 13 9 20 9" />
                    </svg>
                </button>
                {/* Close */}
                <button
                    onClick={onClose}
                    className="p-2 text-gray-300 hover:text-white hover:bg-[#1E1F42] rounded-full transition-colors"
                    title="Close"
                >
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    );
};

export default ChatHeader; 