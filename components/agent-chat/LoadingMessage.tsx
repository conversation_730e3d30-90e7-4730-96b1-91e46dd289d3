import React from 'react';

export const LoadingMessage: React.FC = () => {
    return (
        <div className="flex justify-start mb-3">
            <div className="max-w-[85%] rounded-lg p-3 bg-[#0D0E26] text-gray-100 border border-[#1E1F42] shadow-md">
                <div className="flex items-center space-x-3">
                    <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                    </div>
                    <div className="text-sm text-gray-400">Agent is thinking...</div>
                </div>
            </div>
        </div>
    );
}; 