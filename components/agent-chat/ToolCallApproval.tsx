import React, { useState } from 'react';
import { DiffViewer } from './DiffViewer';
import { DiffData } from './types';

interface ToolCallApprovalProps {
    toolName: string;
    toolDescription: string;
    toolArgs: Record<string, unknown>;
    content?: string;
    comparisonMessage?: string;
    diffData?: DiffData;
    onApprove: () => void;
    onReject: () => void;
}

export const ToolCallApproval: React.FC<ToolCallApprovalProps> = ({
    toolName,
    toolDescription,
    toolArgs,
    content,
    comparisonMessage,
    diffData,
    onApprove,
    onReject
}) => {
    const [showComparison, setShowComparison] = useState(false);
    console.log('toolDescription', toolDescription.substring(0, 1));

    console.log('\n=== ToolCallApproval Render Debug ===');
    console.log('Tool:', toolName);
    console.log('Has diffData:', !!diffData);
    if (diffData) {
        console.log('Diff Data in ToolCallApproval:', {
            type: diffData.type,
            hasBeforeData: !!diffData.before,
            hasAfterData: !!diffData.after,
            title: diffData.title
        });
    }

    // Check if this is a special edit request with comparison data or diff data
    const isEditRequest = toolName === 'edit_step_request';

    return (
        <div className="bg-gray-800 rounded-lg p-4 w-full text-gray-300 border border-gray-700">
            <div className="flex justify-between items-start mb-4">
                <div>
                    <h4 className="text-lg font-semibold text-blue-400">{toolName}</h4>
                   
                </div>
                <div className="flex space-x-2">
                    <button
                        onClick={onApprove}
                        className="px-3 py-1 bg-green-600 hover:bg-green-700 rounded text-white transition-colors"
                    >
                        Approve
                    </button>
                    <button
                        onClick={onReject}
                        className="px-3 py-1 bg-red-600 hover:bg-red-700 rounded text-white transition-colors"
                    >
                        Reject
                    </button>
                </div>
            </div>

            {/* Show arguments if not an edit request */}
            {!isEditRequest && (
                <div className="mt-2">
                    <h5 className="text-sm font-semibold text-gray-400">Arguments:</h5>
                    <pre className="mt-1 p-2 bg-gray-900 rounded text-sm overflow-x-auto">
                        {JSON.stringify(toolArgs, null, 2)}
                    </pre>
                </div>
            )}

            {(diffData || comparisonMessage || content) && (
                <div>
                    <button 
                        onClick={() => setShowComparison(!showComparison)}
                        className="flex items-center space-x-2 text-sm text-blue-400 hover:text-blue-300 transition-colors mb-2"
                    >
                        <span>{showComparison ? '▼' : '▶'}</span>
                        <span>View Changes</span>
                    </button>
                    
                    {showComparison && (
                        <div className="mt-2">
                            {diffData ? (
                                <DiffViewer diffData={diffData} className="mt-4" />
                            ) : content ? (
                                <pre className="whitespace-pre-wrap text-sm bg-gray-900 p-4 rounded mt-4">
                                    {content}
                                </pre>
                            ) : comparisonMessage ? (
                                <pre className="whitespace-pre-wrap text-sm bg-gray-900 p-4 rounded mt-4">
                                    {comparisonMessage}
                                </pre>
                            ) : null}
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default ToolCallApproval; 