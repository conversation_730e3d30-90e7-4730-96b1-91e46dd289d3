import React from 'react';
import { ChatSession } from './types';

interface ChatSidebarProps {
    isOpen: boolean;
    onClose: () => void;
    onNewChat: () => void;
    chatSessions: ChatSession[];
    currentSessionId: string | null;
    onSelectSession: (sessionId: string) => void;
}

export const ChatSidebar: React.FC<ChatSidebarProps> = ({
    isOpen,
    onClose,
    onNewChat,
    chatSessions,
    currentSessionId,
    onSelectSession
}) => {
    if (!isOpen) return null;

    return (
        <div className="w-full bg-[#0D0E26] overflow-y-auto flex-shrink-0 absolute md:absolute z-10 h-full border-r border-[#1E1F42]">
            <div className="p-4">
                <h3 className="text-lg font-semibold mb-4 flex justify-between items-center">
                    Chat History
                    <button 
                        onClick={onClose}
                        className="text-gray-400 hover:text-white"
                    >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </h3>
                <div className="space-y-2">
                    <button
                        onClick={onNewChat}
                        className="w-full text-center p-2 rounded hover:bg-[#1E1F42] transition-colors border border-[#1E1F42]"
                    >
                        + New Chat
                    </button>
                    {chatSessions.map((session) => (
                        <button
                            key={session.chat_session_id}
                            onClick={() => onSelectSession(session.chat_session_id)}
                            className={`w-full text-left p-2 rounded hover:bg-[#1E1F42] transition-colors border ${
                                currentSessionId === session.chat_session_id ? 'bg-[#1E1F42] border-[#2E2F52]' : 'border-[#1E1F42]'
                            }`}
                        >
                            <div className="text-sm font-medium">
                                {new Date(session.session_start).toLocaleDateString()}
                            </div>
                            <div className="text-xs text-gray-400">
                                {session.message_count} messages
                            </div>
                        </button>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default ChatSidebar; 