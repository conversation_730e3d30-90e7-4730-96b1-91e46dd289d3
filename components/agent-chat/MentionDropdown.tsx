import React, { useEffect, useRef } from 'react';
import { Sequence, SelectableItem } from './types';

interface MentionDropdownProps {
    isOpen: boolean;
    sequences: Sequence[];
    onSelect: (item: SelectableItem) => void;
    onClose: () => void;
    position: { top: number; left: number };
    searchTerm?: string;
}

export const MentionDropdown: React.FC<MentionDropdownProps> = ({
    isOpen,
    sequences,
    onSelect,
    onClose,
    position,
    searchTerm = ''
}) => {
    const dropdownRef = useRef<HTMLDivElement>(null);
    const [dropdownPosition, setDropdownPosition] = React.useState(position);

    useEffect(() => {
        if (isOpen && dropdownRef.current) {
            const rect = dropdownRef.current.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            
            // Check if dropdown would go off screen at the bottom
            const wouldOverflowBottom = position.top + rect.height > viewportHeight;
            
            // Position above or below based on available space
            const newTop = wouldOverflowBottom 
                ? position.top - rect.height - 5  // Position above
                : position.top + 25;              // Position below
            
            setDropdownPosition({
                top: newTop,
                left: position.left
            });
        }
    }, [isOpen, position]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen, onClose]);

    // Flatten and filter all items
    const allItems: SelectableItem[] = sequences.flatMap(sequence => [
        {
            id: sequence.id,
            name: sequence.name,
            type: 'sequence' as const,
            path: sequence.name
        },
        ...sequence.flows.flatMap(flow => [
            {
                id: flow.id,
                name: flow.name,
                type: 'flow' as const,
                path: `${sequence.name}/${flow.name}`
            },
            ...flow.steps.map(step => ({
                id: step.id,
                name: step.name,
                type: 'step' as const,
                path: `${sequence.name}/${flow.name}/${step.name}`
            }))
        ])
    ]).filter(item => {
        const search = searchTerm.toLowerCase();
        return item.name.toLowerCase().includes(search) ||
               item.path.toLowerCase().includes(search);
    });

    if (!isOpen) return null;

    // Debug: log position
    console.log('MentionDropdown position:', dropdownPosition, 'isOpen:', isOpen);

    return (
        <div 
            ref={dropdownRef}
            className="fixed z-[9999] bg-[#0D0E26] border border-[#1E1F42] rounded-lg shadow-lg overflow-hidden"
            style={{ 
                top: dropdownPosition.top, 
                left: 20,
                maxHeight: '300px',
                width: '300px'
            }}
        >
            <div className="overflow-y-auto max-h-[300px]">
                {allItems.length === 0 ? (
                    <div className="p-4 text-gray-400">No matches found</div>
                ) : (
                    allItems.map(item => (
                        <div 
                            key={`${item.type}-${item.id}`}
                            className="p-2 hover:bg-[#161729] cursor-pointer"
                            onClick={() => {
                                onSelect(item);
                                onClose();
                            }}
                        >
                            <div className="font-medium text-white">{item.name}</div>
                            <div className="text-xs text-gray-400">
                                {item.type} • {item.path}
                            </div>
                        </div>
                    ))
                )}
            </div>
        </div>
    );
};

export default MentionDropdown; 