import React from 'react'

 // Add welcome message component
 const WelcomeMessage = () => {
    return (
        <div className="flex flex-col items-center justify-center h-full text-center px-4 py-8 text-[#B2B2C1]">
            <div className="text-4xl mb-4">👋</div>
            <h2 className="text-xl font-semibold mb-2 text-white">Hi, I&apos;m DrChat</h2>
            <p className="text-gray-400 text-sm font-normal mb-6">
                Use natural language to create assertions,<br/>modify steps, or generate sequences.
            </p>
            <div className=" rounded-lg p-4 w-full max-w-full border border-[#1B1B41] text-left text-[#777781]">
                <h3 className="text-sm font-normal  mb-3 text-left">Prompt Examples:</h3>
                <ul className="text-sm  space-y-2">
                    <li className='border-b border-[#1B1B41] pb-2' >Ensure @Login response email matches @Signup input.</li>
                    <li>Create a sequence using @Signup, @Login, and @GetAsset.</li>
                </ul>
            </div>
        </div>
    );
};

export default WelcomeMessage