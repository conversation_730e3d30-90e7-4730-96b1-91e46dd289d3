import React, { forwardRef, useEffect, useRef, ReactElement } from 'react';

interface EditableInputProps {
    value: string;
    onChange: (value: string) => void;
    onKeyDown: (e: React.KeyboardEvent<HTMLDivElement>) => void;
    disabled?: boolean;
    placeholder?: string;
    className?: string;
    selectedMentions: Set<string>;
}

export const EditableInput = forwardRef<HTMLDivElement, EditableInputProps>(
    (
        {
            value,
            onChange,
            onKeyDown,
            disabled = false,
            placeholder = '',
            className = '',
            selectedMentions,
        },
        ref
    ): ReactElement => {
        const divRef = useRef<HTMLDivElement>(null);

        // keep both refs in sync
        useEffect(() => {
            if (!ref) return;
            if (typeof ref === 'function') {
                ref(divRef.current!);
            } else {
                (ref as React.MutableRefObject<HTMLDivElement | null>).current =
                    divRef.current;
            }
        }, [ref]);

        // render the HTML + highlight mentions
        useEffect(() => {
            if (!divRef.current) return;
            let html = value;
            selectedMentions.forEach((mention) => {
                const esc = mention.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                html = html.replace(
                    new RegExp(esc, 'g'),
                    `<span class="mention-highlight">${mention}</span>`
                );
            });
            if (divRef.current.innerHTML !== html) {
                divRef.current.innerHTML = html;
            }
        }, [value, selectedMentions]);

        // bubble up textContent changes
        const handleInput = (e: React.FormEvent<HTMLDivElement>) => {
            onChange(e.currentTarget.textContent || '');
        };

        return (
            <div className={`relative  ${className}`}>
                <div
                    ref={divRef}
                    contentEditable={!disabled}
                    onInput={handleInput}
                    onKeyDown={onKeyDown}
                    data-placeholder={placeholder}
                    className={`
                        w-full min-h-[42px] bg-[#1B1B41] text-white p-2 rounded-lg
                        outline-none border border-[#1E1F42] focus:border-[#2E2F52]
                        transition-colors text-sm ${disabled ? 'cursor-not-allowed opacity-50' : ''}
                    `}
                    style={{
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-word',
                    }}
                />
                <style jsx>{`
                    [contenteditable='true']:empty:before {
                        content: attr(data-placeholder);
                        color: #6b7280;
                        pointer-events: none;
                    }
                    .mention-highlight {
                        background-color: rgba(79, 70, 229, 0.2);
                        color: rgb(165, 180, 252);
                        border-radius: 4px;
                        font-weight: 500;
                        padding: 2px 4px;
                        margin: 0 1px;
                    }
                `}</style>
            </div>
        );
    }
);

EditableInput.displayName = 'EditableInput';

export default EditableInput; 