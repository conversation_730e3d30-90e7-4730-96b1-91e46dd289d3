import React from 'react';
import { DiffData } from './types';
import { formatJsonDiff, formatTextDiff } from './diffUtils';

interface DiffViewerProps {
    diffData: DiffData;
    className?: string;
}

export const DiffViewer: React.FC<DiffViewerProps> = ({ diffData, className = '' }) => {
    console.log('\n=== DiffViewer Render Debug ===');
    console.log('Received diffData:', {
        type: diffData.type,
        hasBeforeData: !!diffData.before,
        hasAfterData: !!diffData.after,
        title: diffData.title,
        beforeLabel: diffData.beforeLabel,
        afterLabel: diffData.afterLabel
    });

    // Early return if missing required data
    if (!diffData.before || !diffData.after) {
        console.error('DiffViewer: Missing before or after data');
        return (
            <div className="text-red-500 p-4">
                Error: Missing diff data
            </div>
        );
    }

    const renderDiff = () => {
        console.log('Rendering diff of type:', diffData.type);
        
        try {
            switch (diffData.type) {
                case 'json':
                    const jsonDiff = formatJsonDiff(diffData.before, diffData.after);
                    console.log('Formatted JSON diff:', jsonDiff);
                    return (
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <h6 className="text-sm font-medium text-gray-400">
                                    {diffData.beforeLabel || 'Before'}
                                </h6>
                                <pre className="p-3 bg-gray-900 rounded text-sm overflow-x-auto">
                                    {JSON.stringify(diffData.before, null, 2)}
                                </pre>
                            </div>
                            <div className="space-y-2">
                                <h6 className="text-sm font-medium text-gray-400">
                                    {diffData.afterLabel || 'After'}
                                </h6>
                                <pre className="p-3 bg-gray-900 rounded text-sm overflow-x-auto">
                                    {JSON.stringify(diffData.after, null, 2)}
                                </pre>
                            </div>
                            <div className="col-span-2">
                                <h6 className="text-sm font-medium text-gray-400 mb-2">Changes</h6>
                                <ul className="space-y-1">
                                    {jsonDiff.map((diff, index) => (
                                        <li
                                            key={index}
                                            className={`text-sm p-1 rounded ${
                                                diff.type === 'added'
                                                    ? 'text-green-400 bg-green-900/20'
                                                    : diff.type === 'removed'
                                                    ? 'text-red-400 bg-red-900/20'
                                                    : diff.type === 'modified'
                                                    ? 'text-yellow-400 bg-yellow-900/20'
                                                    : 'text-gray-400'
                                            }`}
                                        >
                                            {diff.message}
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                    );

                case 'text':
                    const textDiff = formatTextDiff(
                        diffData.before.toString(),
                        diffData.after.toString()
                    );
                    console.log('Formatted text diff:', textDiff);
                    return (
                        <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <h6 className="text-sm font-medium text-gray-400">
                                        {diffData.beforeLabel || 'Before'}
                                    </h6>
                                    <pre className="p-3 bg-gray-900 rounded text-sm overflow-x-auto whitespace-pre-wrap">
                                        {diffData.before.toString()}
                                    </pre>
                                </div>
                                <div className="space-y-2">
                                    <h6 className="text-sm font-medium text-gray-400">
                                        {diffData.afterLabel || 'After'}
                                    </h6>
                                    <pre className="p-3 bg-gray-900 rounded text-sm overflow-x-auto whitespace-pre-wrap">
                                        {diffData.after.toString()}
                                    </pre>
                                </div>
                            </div>
                            <div>
                                <h6 className="text-sm font-medium text-gray-400 mb-2">Changes</h6>
                                <div className="bg-gray-900 rounded p-3">
                                    {textDiff.map((line, index) => (
                                        <div
                                            key={index}
                                            className={`text-sm font-mono ${
                                                line.type === 'added'
                                                    ? 'text-green-400 bg-green-900/20'
                                                    : line.type === 'removed'
                                                    ? 'text-red-400 bg-red-900/20'
                                                    : 'text-gray-400'
                                            }`}
                                        >
                                            {line.type === 'added' ? '+ ' : line.type === 'removed' ? '- ' : '  '}
                                            {line.content}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    );

                default:
                    console.error('Unknown diff type:', diffData.type);
                    return (
                        <div className="text-red-500 p-4">
                            Error: Unsupported diff type
                        </div>
                    );
            }
        } catch (error) {
            console.error('Error rendering diff:', error);
            return (
                <div className="text-red-500 p-4">
                    Error rendering diff: {error.message}
                </div>
            );
        }
    };

    return (
        <div className={`diff-viewer ${className}`}>
            {renderDiff()}
        </div>
    );
};

export default DiffViewer; 