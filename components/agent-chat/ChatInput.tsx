import React, { useRef } from "react";
import { MentionData, Sequence } from "./types";
import EditableInput from "./EditableInput";
import MentionDropdown from "./MentionDropdown";
import SuggestionsInput, {
  MentionsData,
} from "./assertions-chat/SuggestionsInput";

interface ChatInputProps {
  input: string;
  onInputChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  hasPendingToolCall: boolean;
  mentionState: {
    isOpen: boolean;
    searchTerm: string;
    triggerIdx: number;
  };
  dropdownPosition: { top: number; left: number };
  sequences: Sequence[];
  onMentionSelect: (item: MentionData) => void;
  onMentionClose: () => void;
  selectedMentions: Set<string>;
  onKeyDown: (e: React.KeyboardEvent<HTMLDivElement>) => void;
  inputRef?: React.RefObject<HTMLDivElement>;
  setSelectedMentions: (mentions: MentionsData[]) => void;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  input,
  onInputChange,
  onSubmit,
  isLoading,
  hasPendingToolCall,
  mentionState,
  dropdownPosition,
  sequences,
  onMentionSelect,
  onMentionClose,
  selectedMentions,
  onKeyDown,
  inputRef,
  setSelectedMentions,
}) => {
  const formRef = useRef<HTMLFormElement>(null);

  console.log("log:: sequences is ", sequences);

  let suggestionsOptions: MentionsData[] = [];

  if (sequences) {
    const sequenceOptions: MentionsData[] = sequences.map((sequence) => ({
      id: sequence.id,
      text: sequence.name,
      displayName: sequence.name,
      type: "sequence",
      sequenceId: sequence?.id,
      flowId: null,
      stepId: null,
    }));

    let flowOptions: MentionsData[] = [];
    let stepOptions: MentionsData[] = [];

    sequences.map((sequence) => {
      sequence?.flows?.map((flow, idx) => {
        flowOptions.push({
          id: flow?.id,
          text: flow?.name,
          displayName: ` ${flow?.name}`,
          type: "flow",
          sequenceId: sequence?.id,
        });
        flow?.steps?.map((step) => {
          stepOptions.push({
            id: step.id,
            text: step.name,
            displayName: `${flow?.name} - ${step.name}`,
            type: "step",
            sequenceId: sequence?.id,
            flowId: flow?.id,
            stepId: step.id,
          });
        });
      });
    });

    suggestionsOptions = [...sequenceOptions, ...flowOptions, ...stepOptions];
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    // Call the original onKeyDown handler for @ mentions
    onKeyDown(e);

    // Handle Enter and Shift+Enter
    if (e.key === "Enter") {
      if (!e.shiftKey) {
        e.preventDefault();
        if (!isLoading && !hasPendingToolCall && input.trim()) {
          formRef.current?.requestSubmit();
        }
      }
    }
  };

  return (
    <div className="p-2  ">
      {/*
       <form ref={formRef} onSubmit={onSubmit} className="relative">
        <EditableInput
          value={input}
          onChange={onInputChange}
          onKeyDown={handleKeyDown}
          disabled={isLoading || hasPendingToolCall}
          placeholder="Type a message or use @ to reference"
          selectedMentions={selectedMentions}
          ref={inputRef}
        />

        {mentionState.isOpen && (
          <MentionDropdown
            isOpen={mentionState.isOpen}
            sequences={sequences}
            onSelect={onMentionSelect}
            onClose={onMentionClose}
            position={dropdownPosition}
            searchTerm={mentionState.searchTerm}
          />
        )}

        <button
          type="submit"
          disabled={isLoading || hasPendingToolCall || !input.trim()}
          className={`absolute right-3 top-1/2 -translate-y-1/2 p-0 rounded-full  bg-[#875BF8] text-white 
                        ${
                          isLoading || hasPendingToolCall || !input.trim()
                            ? "text-gray-500 cursor-not-allowed"
                            : "text-white hover:bg-blue-500 hover:bg-opacity-10"
                        }`}
        >
          <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect width="32" height="32" rx="8" fill="#875BF8" />
            <g clip-path="url(#clip0_5372_30035)">
              <path
                d="M20.4238 18.4467L21.5681 15.0138C22.5677 12.0149 23.0675 10.5155 22.276 9.72395C21.4845 8.93244 19.985 9.43226 16.9861 10.4319L13.5532 11.5762C11.1328 12.383 9.92259 12.7864 9.57869 13.378C9.25152 13.9407 9.25152 14.6358 9.57868 15.1985C9.92259 15.7901 11.1328 16.1935 13.5532 17.0003C13.9419 17.1299 14.1362 17.1946 14.2986 17.3034C14.456 17.4087 14.5912 17.544 14.6966 17.7014C14.8053 17.8638 14.8701 18.0581 14.9997 18.4467C15.8065 20.8672 16.2099 22.0774 16.8014 22.4213C17.3642 22.7484 18.0592 22.7484 18.622 22.4213C19.2136 22.0774 19.617 20.8672 20.4238 18.4467Z"
                stroke="white"
                stroke-width="1.5"
              />
              <path
                d="M19.8596 12.1404L17.0526 14.9165"
                stroke="white"
                stroke-width="1.5"
                stroke-linecap="round"
              />
            </g>
            <defs>
              <clipPath id="clip0_5372_30035">
                <rect
                  width="16"
                  height="16"
                  fill="white"
                  transform="translate(8 8)"
                />
              </clipPath>
            </defs>
          </svg>
        </button>
      </form> */}

      <form ref={formRef} onSubmit={onSubmit} className="relative">
        <SuggestionsInput
          suggestionsOptions={suggestionsOptions}
          text={input}
          setText={onInputChange}
          setSuggestionsValues={() => {}}
          suggestionsValues={[]}
          setMentionsData={setSelectedMentions}
        />
        <button
          type="submit"
          disabled={isLoading || hasPendingToolCall || !input.trim()}
          className={`absolute right-3 top-1/2 -translate-y-1/2 p-0 rounded-full  bg-[#875BF8] text-white 
                        ${
                          isLoading || hasPendingToolCall || !input.trim()
                            ? "text-gray-500 cursor-not-allowed"
                            : "text-white hover:bg-blue-500 hover:bg-opacity-10"
                        }`}
        >
          <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect width="32" height="32" rx="8" fill="#875BF8" />
            <g clip-path="url(#clip0_5372_30035)">
              <path
                d="M20.4238 18.4467L21.5681 15.0138C22.5677 12.0149 23.0675 10.5155 22.276 9.72395C21.4845 8.93244 19.985 9.43226 16.9861 10.4319L13.5532 11.5762C11.1328 12.383 9.92259 12.7864 9.57869 13.378C9.25152 13.9407 9.25152 14.6358 9.57868 15.1985C9.92259 15.7901 11.1328 16.1935 13.5532 17.0003C13.9419 17.1299 14.1362 17.1946 14.2986 17.3034C14.456 17.4087 14.5912 17.544 14.6966 17.7014C14.8053 17.8638 14.8701 18.0581 14.9997 18.4467C15.8065 20.8672 16.2099 22.0774 16.8014 22.4213C17.3642 22.7484 18.0592 22.7484 18.622 22.4213C19.2136 22.0774 19.617 20.8672 20.4238 18.4467Z"
                stroke="white"
                stroke-width="1.5"
              />
              <path
                d="M19.8596 12.1404L17.0526 14.9165"
                stroke="white"
                stroke-width="1.5"
                stroke-linecap="round"
              />
            </g>
            <defs>
              <clipPath id="clip0_5372_30035">
                <rect
                  width="16"
                  height="16"
                  fill="white"
                  transform="translate(8 8)"
                />
              </clipPath>
            </defs>
          </svg>
        </button>
      </form>
    </div>
  );
};

export default ChatInput;
