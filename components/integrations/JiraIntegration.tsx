import useIntegrations from "@/app/integrations/hooks/useIntegrations";
import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import React, { useEffect, useState } from "react";
import Select from "react-select";
import PrimaryButton from "../common/buttons/PrimaryButton";

const JiraIntegration = () => {
  const { fetchJiraProjects, setJiraProject } = useIntegrations();
  const [loading, setLoading] = useState<boolean>(false);
  const [jiraProjects, setJiraProjects] = useState<any>([]);
  const [selectedProject, setSelectedProject] = useState<any>(null);
  // Check if JIRA project is selected in the db

  const getJiraProjects = async () => {
    try {
      setLoading(true);
      const response = await fetchJiraProjects();

      const projectOptions = response.map((item) => {
        return {
          label: item.name,
          value: item.id,
        };
      });
      console.log("log:: response", projectOptions);

      setJiraProjects(projectOptions);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleProjectChange = async () => {
    try {
      await setJiraProject(selectedProject);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getJiraProjects();
  }, []);
  return (
    <div>
      Please Configure your JIRA project
      <div className="">Please select the projects</div>
      {loading ? (
        <>LOADING...</>
      ) : (
        <>
          <select
            name=""
            id=""
            onChange={(e) => setSelectedProject(e.target.value)}
          >
            {jiraProjects.map((item: any) => (
              <option key={item.value} value={item.value}>
                {item.label}
              </option>
            ))}
          </select>

          <PrimaryButton onClick={handleProjectChange} label="Save" />
        </>
      )}
    </div>
  );
};

export default JiraIntegration;
