import React from "react";
import PrimaryButton from "../common/buttons/PrimaryButton";
import PrimaryButtonOutlined from "../common/buttons/PrimaryButtonOutlined";

interface Integration {
  name: string;
  description: string;
  available: boolean;
  id: number;
}

const IntegrationCard = ({
  integration,
  handleConnect,
  handleDisconnect,
}: {
  integration: Integration;
  handleConnect: (integrationId: number) => void;
  handleDisconnect: (integrationId: number) => void;
}) => {
  return (
    <div
      key={integration.id}
      className="relative w-[300px] h-[200px] p-4 rounded-2xl border border-drCodeDarkBlue-200 shadow-sm bg-drcodeBlue flex flex-col"
    >
      {/* Always visible title */}
      <div className="text-xl font-semibold text-white mb-2 z-20">
        {integration.name}
      </div>

      {/* Overlay for unavailable integration */}
      {!integration.available && (
        <div className="absolute inset-0 bg-drcodeBlue bg-opacity-70 backdrop-blur-sm rounded-2xl flex items-center justify-center z-10">
          <span className="text-lg font-semibold text-gray-400">
            Coming Soon
          </span>
        </div>
      )}

      {/* Content below the title */}
      <div
        className={`flex flex-col flex-1 justify-between ${
          !integration.available ? "pointer-events-none" : ""
        }`}
      >
        <p className="text-sm text-gray-400 mb-6">{integration.description}</p>

        <div className="flex gap-3 mt-auto">
          <PrimaryButton
            label="Connect"
            onClick={() => handleConnect(integration.id)}
          />
          <PrimaryButtonOutlined
            label="Disconnect"
            onClick={() => handleDisconnect(integration.id)}
          />
        </div>
      </div>
    </div>
  );
};

export default IntegrationCard;
