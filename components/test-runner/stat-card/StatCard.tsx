import React, { useEffect, useState } from "react";
import styles from "./StatCard.module.scss";
import Image from "next/image";

export interface StatCardProps {
  image: string;
  title: string;
  count: number; // Directly use a number
  className?: string;
  isAllTestCount?: boolean;
  isProjectView?: boolean;
}

export const StatCard = React.memo(
  ({
    image,
    title,
    count,
    className,
    isAllTestCount = false,
    isProjectView = false,
  }: StatCardProps) => {
    const [displayCount, setDisplayCount] = useState<number>(0);

    useEffect(() => {
      let start = displayCount;
      const end = count;
      const duration = 500; // Animation duration in ms
      const frameRate = 16; // Approximate frame rate (60fps)
      const totalFrames = duration / frameRate;
      const increment = (end - start) / totalFrames;

      if (start === end) return;

      const animate = () => {
        start += increment;
        if (
          (increment > 0 && start >= end) ||
          (increment < 0 && start <= end)
        ) {
          setDisplayCount(end);
        } else {
          setDisplayCount(Math.floor(start)); // Use Math.floor to avoid decimal values
          requestAnimationFrame(animate);
        }
      };

      requestAnimationFrame(animate);

      // Cleanup in case the component unmounts during animation
      return () => cancelAnimationFrame(requestAnimationFrame(animate));
    }, [count]);

    return (
      <div className={`${styles["stat"]} flex gap-4 ${className}`}>
        <Image src={image} alt={title} quality={100} width={45} height={45} />
        <div className={`${styles["stat-detail"]}`}>
          <p className={`${styles["stat-title"]}`}>{title}</p>
          <p className={`${styles["stat-count"]} text-white`}>
            {isAllTestCount && !isProjectView ? (
              <>{displayCount === 0 ? "-" : displayCount}</>
            ) : (
              <>{displayCount}</>
            )}
          </p>
        </div>
      </div>
    );
  }
);
