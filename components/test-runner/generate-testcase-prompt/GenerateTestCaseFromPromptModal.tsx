import useBusinessTestCase from "../tests-table/useBusinessTestCase";
import {
  Ri<PERSON><PERSON><PERSON><PERSON>ill,
  Ri<PERSON><PERSON>ck<PERSON>ine,
  RiDeleteBin6Line,
  RiPencilFill,
  RiPencilLine,
  RiRefreshLine,
} from "react-icons/ri";
import { IoMdAddCircleOutline } from "react-icons/io";
import Modal from "@/components/common/modal/Modal";
import { toast } from "react-toastify";
import PrimaryTextarea from "@/components/common/primary-textarea/PrimaryTextarea";
import { useEffect, useRef, useState } from "react";
import RightSideBottomSheet from "@/components/common/right-bottom-sheet/RightBottomSheet";
import LoaderGif from "@/components/common/loader-gif/LoaderGif";

const GenerateTestCaseFromPromptModal = ({
  fetchTestCases,
  handleClose,
  testSuiteId,
  open,
  setPromptTestCaseLoading,
  setSelectedTab,
}) => {
  const {
    loading,
    prompts,
    userPrompt,
    promptsLoading,
    showEnhancedPrompt,
    editPrompt,
    title,
    handleAddPrompt,
    handleEditPrompt,
    handleRemovePrompt,
    handlePromptChange,
    handleGenerateTestCasesUsingPrompts,
    setUserPrompt,
    setPrompts,
    setShowEnhancedPrompt,
    handleEnhancedPrompt,
  } = useBusinessTestCase();

  const handleSave = async () => {
    // Trigger the request and store the promise
    setPromptTestCaseLoading(true);
    const generationPromise = handleGenerateTestCasesUsingPrompts({
      prompts: prompts,
      testSuitId: testSuiteId,
    });

    // Start polling immediately after firing the request
    const intervalId = setInterval(() => {
      fetchTestCases(parseInt(testSuiteId as string, 10), true);
    }, 3000); // Every 3 seconds (tweak as needed)

    // Immediately close the modal or UI element
    handleClose();

    // When the response completes, stop polling and fetch one last time
    const response = await generationPromise;
    if (response) {
      clearInterval(intervalId);
      fetchTestCases(parseInt(testSuiteId as string, 10)); // Final fetch
      setPromptTestCaseLoading(false);
      setSelectedTab(1);
    }
  };

  const handleEnhancedPromptRequest = async () => {
    await handleEnhancedPrompt({
      testSuitId: testSuiteId,
      userPrompt: userPrompt,
    });
  };

  return (
    <RightSideBottomSheet onClose={handleClose} isOpen={open}>
      <div className="h-[92%] overflow-auto">
        {showEnhancedPrompt ? (
          <EnhancedPrompts
            handleAddPrompt={handleAddPrompt}
            handlePromptChange={handlePromptChange}
            handleRemovePrompt={handleRemovePrompt}
            handleEditPrompt={handleEditPrompt}
            handleEnhancedPrompt={handleEnhancedPromptRequest}
            prompts={prompts}
            loading={promptsLoading}
            editPrompt={editPrompt}
            userPrompt={title || userPrompt}
          />
        ) : (
          <UserPrompt
            prompt={userPrompt}
            setPrompt={setUserPrompt}
            handleClose={handleClose}
          />
        )}
      </div>

      <div className="flex justify-between gap-2 items-center  w-full">
        {showEnhancedPrompt && (
          <button
            onClick={() => setShowEnhancedPrompt(false)}
            className="button--outlined-primary py-2 px-8 text-[14px]"
            style={{
              fontSize: "14px",
            }}
          >
            Back
          </button>
        )}
        <div className="flex gap-2 items-center ml-auto">
          <button
            onClick={handleClose}
            className="button--outlined-primary py-2 px-8 text-[14px]"
            style={{
              fontSize: "14px",
            }}
          >
            Close
          </button>

          <button
            onClick={
              showEnhancedPrompt ? handleSave : handleEnhancedPromptRequest
            }
            className="button--primary py-2 px-8 text-[14px]"
            style={{
              fontSize: "14px",
            }}
            disabled={
              loading ||
              promptsLoading ||
              !userPrompt ||
              (showEnhancedPrompt && prompts.length === 0)
            }
          >
            {showEnhancedPrompt ? "Generate test case" : "Review and improve"}
          </button>
        </div>
      </div>
    </RightSideBottomSheet>
  );
};

export default GenerateTestCaseFromPromptModal;

const UserPrompt = ({ prompt, setPrompt, handleClose }) => {
  return (
    <>
      <div className="font-bold text-[16px] text-[#D1D1E3]">
        Add AI-Powered Test Cases{" "}
      </div>
      <p className="text-[#B2B2C1] text-[13px] mb-2">
        Extend your test coverage by adding more test scenario.{" "}
      </p>
      <div className="bg-drCodeDarkBlue h-[80%]  p-2 rounded-lg">
        <PrimaryTextarea
          className=" w-full"
          style={{ minHeight: "100px" }}
          placeholder="Enter your test case scenario"
          onChange={(e) => setPrompt(e.target.value)}
          value={prompt}
        />
      </div>
    </>
  );
};

const EnhancedPrompts = ({
  userPrompt,
  prompts,
  handlePromptChange,
  handleRemovePrompt,
  handleAddPrompt,
  handleEditPrompt,
  loading,
  editPrompt,
  handleEnhancedPrompt,
}) => {
  const textAreaRefs = useRef<(HTMLTextAreaElement | null)[]>([]);

  const handleEditClick = (index: number) => {
    handleEditPrompt(index);
    setTimeout(() => {
      textAreaRefs.current[index]?.focus();
    }, 0); // Slight delay to ensure the state is updated before focusing
  };

  return (
    <>
      <div className="font-bold text-[16px] text-[#D1D1E3]">
        Review & Enhance{" "}
      </div>
      <p className="text-[#B2B2C1] text-[13px] mb-2">
        AI checks if your input is a valid test scenario and suggests
        improvements. Select the best version to proceed.{" "}
      </p>

      <div className="bg-drCodeDarkBlue min-h-[80%]  p-2 rounded-lg mt-4 ">
        {loading ? (
          <div className="flex items-center justify-center mt-[30%] mb-[30%] flex-col">
            <LoaderGif />
            <p>Analyzing your prompt</p>
          </div>
        ) : (
          <div className="bg-drcodeBlue rounded-lg p-2">
            <div className="flex justify-between items-center mb-2">
              <div className="text-[#EBEBF3] text-[14px] font-semibold max-w-[80%]">
                {userPrompt}
              </div>

              <RiRefreshLine
                color="white"
                size={20}
                className="cursor-pointer"
                onClick={handleEnhancedPrompt}
              />
            </div>
            {prompts.map((prompt, index) => (
              <div
                key={index}
                className="flex gap-1 my-2 bg-drCodeDarkBlue rounded-md"
              >
                <div className="flex-1">
                  <PrimaryTextarea
                    className="h-full w-full"
                    style={{
                      minHeight: "100px",
                      height: "100%",
                      fontSize: "13px",
                      color: "#B2B2C1",
                      fontWeight: "400",
                    }}
                    placeholder="Enter your test case scenario"
                    onChange={(e) => handlePromptChange(index, e.target.value)}
                    value={prompt}
                    readOnly={editPrompt !== index}
                    ref={(el) => {
                      if (el) textAreaRefs.current[index] = el;
                    }}
                  />
                </div>
                <div className="w-[40px] flex gap-2 pr-1 items-center justify-center cursor-pointer">
                  {editPrompt === index ? (
                    <RiCheckLine
                      onClick={() => handleEditClick(null)}
                      size={25}
                      color="#875bf8"
                    />
                  ) : (
                    <RiPencilLine
                      onClick={() => handleEditClick(index)}
                      size={25}
                      color="#875bf8"
                    />
                  )}

                  <RiDeleteBin6Line
                    size={25}
                    onClick={() => handleRemovePrompt(index)}
                  />
                </div>
              </div>
            ))}

            <div
              onClick={handleAddPrompt}
              className="flex gap-1 my-2 p-2 items-center cursor-pointer"
              style={{
                width: "fit-content",
              }}
            >
              <span>
                <IoMdAddCircleOutline
                  color="#875bf8"
                  style={{
                    cursor: "pointer",
                  }}
                />
              </span>
              <p className="text-drcodePurple text-sm font-semibold">
                Add test case
              </p>
            </div>
          </div>
        )}
      </div>
    </>
  );
};
