import { title } from "process";
import React from "react";
import { FaRegCheckSquare } from "react-icons/fa";
import { IoFlaskOutline, IoSettingsOutline } from "react-icons/io5";
import { RiErrorWarningLine } from "react-icons/ri";

interface StatListTabViewProps {
  allTestCount: number;
  executedCount: number;
  passedCount: number;
  failedCount: number;
  onFilter?: (type: string, value: string) => void;
  selectedFilter: string;
  isProjectView?: boolean;
}

const StatListTabView = ({
  allTestCount,
  executedCount,
  passedCount,
  failedCount,
  onFilter,
  selectedFilter,
  isProjectView = false,
}: StatListTabViewProps) => {
  const tabs = [
    {
      title: "All Tests",
      image: "/codetest.png",
      count: allTestCount,
      icon: <IoFlaskOutline color="#9F7CF9" size={15} />,

      filterValue: "",
    },
    {
      title: "Executed",
      image: "/revote.png",
      icon: <IoSettingsOutline color="#9F7CF9" size={15} />,
      count: executedCount,
      filterValue: "Executed",
    },
    {
      title: "Passed",
      image: "/hourglass.png",
      icon: <FaRegCheckSquare color="#9F7CF9" size={15} />,
      count: passedCount,
      filterValue: "Passed",
    },
    {
      title: "Failed",
      image: "/danger.png",
      icon: <RiErrorWarningLine color="#9F7CF9" size={15} />,
      count: failedCount,
      filterValue: "Failed",
    },
  ];

  return (
    <>

      <div className="flex items-center gap-4 p-1">
        {tabs.map((tab, index) => (
          <>
            <div key={tab.title} className="flex items-center">
              <div
                className={`flex items-center gap-2 text-[#A8A3BE] p-2 px-0 rounded-lg`}
              >
                <span className="text-lg">{tab.icon}</span>
                <span
                  className={`text-sm font-normal ${
                    selectedFilter === tab.filterValue
                      ? "text-[#D9D9E8]"
                      : "text-[#9494A1]"
                  }`}
                >
                  {tab.title}
                </span>
                <span className="text-sm font-semibold text-white">
                  {tab.count}
                </span>
              </div>
            </div>
            {/* Vertical divider after each tab except the last */}
            {index !== tabs.length - 1 && (
              <div className="h-6 border-l border-[#3E3C4E] mx-0" />
            )}
          </>
        ))}
      </div>
    </>
  );
};

export default StatListTabView;
