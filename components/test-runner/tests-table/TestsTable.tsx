"use client";
import React, {
  useC<PERSON>back,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import DrCodeTable from "../../common/drcode-table/DrCodeTable";
import { PiPlugsConnectedFill } from "react-icons/pi";
import ApiTestDetailedView from "../api-test-detailed-view/ApiTestDetailedView";
import { ButtonOutlinedPrimary } from "@/components/common/button-outlined-primary/ButtonOutlinedPrimary";
import Image from "next/image";
import { TestAssertionEntity } from "../api-test-detailed-view/blocks/assertion-results/AssertionResults";
import styles from "./TestsTable.module.scss";
import axios, { AxiosError, AxiosResponse } from "axios";
import EnvList from "../env-list/EnvList";
import StatList from "../stat-list/StatList";
import ProgressBar from "@/components/common/progress-bar/ProgressBar";
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from "next/navigation";
import { TestsTableLoader } from "@/components/skeletons/tests-table-loader/TestsTableLoader";
import { expect, assert, use, should } from "chai";
import { toast } from "react-toastify";
import { Spinner } from "@nextui-org/spinner";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import { useGlobalStore } from "@/stores/globalstore";
import {
  RiArrowDownSFill,
  RiArrowDownSLine,
  RiArrowUpSFill,
  RiArrowUpSLine,
  RiDeleteBin6Line,
  RiEdit2Fill,
} from "react-icons/ri";
import {
  getDataFromLocalStorage,
  removeDataFromLocalStorage,
  setDataOnLocalStorage,
} from "@/temp-utils/globalUtilities";
import {
  LOCAL_STORAGE_DATA_KEYS,
  UserData,
} from "@/temp-utils/Constants/localStorageDataModels";
import TestCaseLoader from "@/components/common/test-case-loader/TestCaseLoader";
import SearchInput from "@/components/common/search-input/SearchInput";
import { FaPencilAlt, FaPlayCircle, FaStopCircle } from "react-icons/fa";
import { MdMotionPhotosPaused } from "react-icons/md";
import { getAssertions } from "@/temp-utils/assertionsUtilities";
import Modal from "@/components/common/modal/Modal";
import TagEditor from "../tag-editor/TagEditor";
import ApiRequestResponseOutputView from "@/components/common/api-request-response-output-view/ApiRequestResponseOutputView";
import { ButtonPrimary } from "@/components/common/button-primary/ButtonPrimary";
import { RxCheck, RxCross1, RxGear } from "react-icons/rx";
import FloatingDropdown from "@/components/common/floating-dropdown/FloatingDropdown";
import PrimaryInput from "@/components/common/primary-input/PrimaryInput";
import RuntimeSettings from "../runtime-settings/RuntimeSettings";
import useTestExecution from "./useTestExecution";
import useBusinessTestCase from "./useBusinessTestCase";
import GenerateTestCaseFromPromptModal from "../generate-testcase-prompt/GenerateTestCaseFromPromptModal";
import useToast from "@/hooks/useToast";
import TabView from "@/components/common/tabview/Tabview";
import RightNavigation, {
  NavigationItem,
} from "../right-navigation/RightNavigation";
import FileMappingModal from "@/app/project/[projectId]/group/[groupId]/testSuit/[testSuitId]/file-mapping-model/FileMappingModal";
import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import useFileMapping from "@/app/project/[projectId]/group/[groupId]/testSuit/[testSuitId]/file-mapping-model/useFileMapping";
import useDataSource from "@/components/data-source/useDataSource";
import categoriesMap from "@/temp-utils/Constants/categories";
import BottomSheet from "@/components/common/right-bottom-sheet/RightBottomSheet";
import StatListTabView from "../stat-list-tab-view/StatListTabView";
import ToggleSwitch from "@/components/common/toggle-switch/ToggleSwitch";
import { Tooltip } from "@/components/common/tooltip/Tooltip";
import { IoMdInformationCircleOutline } from "react-icons/io";
import DataSourcePopup from "@/components/data-source/DataSourcePopup";
export interface ApiTestDetails {
  id: number;
  description: string;
  status_code: string;
  request: any;
  response: any;
  request_type?: any;
  response_type?: any;
  assertion: any;
  assertions_results: TestAssertionEntity[];
}

interface TestRequest {
  headers: Record<string, string>;
  path_params: Record<string, string>;
  query_params: Record<string, string>;
  request_body: {
    email: string;
    password: string;
  };
}

interface EnvironmentVariables {
  [key: string]: string;
}

interface TestSuit {
  id: number;
  group_id: number;
  test_suite_name: string;
  url: string;
  method: string;
  request: TestRequest;
  test_case_generation: string;
  created_at: string;
  updated_at: string;
  environment_variables: EnvironmentVariables;
  is_datasource?: boolean;
  datasource_status?: string;
}

export interface TestCase {
  category: number;
  id: number;
  test_suite_id: number;
  name: string;
  description: string;
  method: string;
  url: string;
  request: string;
  // {
  //     headers: Record<string, string>;
  //     json_body: any;
  //     path_params: Record<string, string>;
  //     query_params: Record<string, string>;
  // };
  assertions: null | string;
  tags: string[];
  created_at: string;
  updated_at: string;
  test_suite_name: string;
  assertions_results: TestAssertionEntity[];
  response: any;
  status_code: string;
  running: boolean;
  expanded: boolean;
  status?: string;
  user_prompt?: string;
  analysis?: {
    fields?: any[];
  };
}

const TestsTable = () => {
  const { setEnvironment, environment, triggerRevalidateTestsExplorer } =
    useGlobalStore();

  const params = useSearchParams();

  const { showToast } = useToast();

  const router = useRouter();
  const pathname = usePathname();

  const expandedRows = useRef<number[]>([]);
  const abortControllerRef = useRef<AbortController | null>(null);

  const [selectedTab, setSelectedTab] = useState<number>(0);
  const [defaultTab, setDefaultTab] = useState<number>(0);
  const [expandToggle, setExpandToggle] = useState(false);

  const [envModal, setEnvModal] = useState<boolean>(false);

  const [variableModal, setVariableModal] = useState<boolean>(false);

  const [dataSourceModal, setDataSourceModal] = useState<boolean>(false);
  const [showAnalyzeData, setShowAnalyzeData] = useState<boolean>(false);

  const [isEditingName, setIsEditingName] = useState<boolean>(false);
  const [suiteName, setSuiteName] = useState<string>("");
  const [realTestData, setRealTestData] = useState<any>(null);
  const [mappingStep, setMappingStep] = useState<number>(0);
  const [uniqueHeaders, setUniqueHeaders] = useState<string[]>([]);

  const { setRealTestDataModal, realTestDataModal } = useFileMapping();

  const [apiVariables, setApiVariables] = useState<{
    headers: Record<string, string>;
    path_params: Record<string, string>;
    query_params: Record<string, string>;
    json_body: string;
  }>({
    headers: {},
    path_params: {},
    query_params: {},
    json_body: "{}",
  });

  // JSON errors state
  const [jsonErrors, setJsonErrors] = useState({
    headers: "",
    pathParams: "",
    queryParams: "",
    requestBody: "",
  });

  // Headers and parameters state
  const [headers, setHeaders] = useState<string>("{}");
  const [pathParams, setPathParams] = useState<string>("{}");
  const [queryParams, setQueryParams] = useState<string>("{}");
  const [requestBody, setRequestBody] = useState<string>("{}");

  const { testSuitId, groupId } = useParams();

  const [testSuitDetails, setTestSuitDetails] = React.useState<TestSuit>();

  const [testCases, setTestCases] = React.useState<TestCase[]>([]);

  const [loading, setLoading] = React.useState<boolean>(false);

  const [pollingStatus, setPollingStatus] = useState<string>(
    "Generating test cases..."
  );

  const intervalIdRef = useRef<NodeJS.Timeout | null>(null);

  const [executedCount, setExecutedCount] = useState<number>(0);
  const [passedCount, setPassedCount] = useState<number>(0);
  const [failedCount, setFailedCount] = useState<number>(0);

  const [selectedEnvOptions, setSelectedEnvOptions] = useState<
    { value: string; label: string }[]
  >([]);

  const [running, setRunning] = useState<boolean>(false);

  const [isAllRunning, setIsAllRunning] = useState(false);

  const [isPaused, setIsPaused] = useState(false);

  const [searchInput, setSearchInput] = useState<string>("");

  const [selectedEnvironment, setSelectedEnvironment] = useState<string | null>(
    null
  );

  const [filters, setFilters] = useState<
    { type: "Result" | "Tags"; options: { name: string; value: string }[] }[]
  >([
    {
      type: "Result",
      options: [
        {
          name: "Executed",
          value: "Executed",
        },
        {
          name: "Passed",
          value: "Passed",
        },
        {
          name: "Failed",
          value: "Failed",
        },
        {
          name: "Not Run",
          value: "Not Run",
        },
      ],
    },
  ]);

  const [selectedFilter, setSelectedFilter] = useState<{
    type: string;
    value: string;
  }>({
    type: "",
    value: "",
  });

  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  const [selectedTestCaseForEdit, setSelectedTestCaseForTagEdit] =
    useState<TestCase | null>(null);

  const [openTagEditorModal, setOpenTagEditorModal] = useState<boolean>(false);

  const [runTimeSettingsModal, setRunTimeSettingsModal] =
    useState<boolean>(false);

  const {
    concurrency,
    runDelay,
    setConcurrency,
    setRunDelay,
    setWaitTime,
    waitTime,
    minElapsedTime,
    setMinElapsedTime,
  } = useTestExecution();

  const {
    handleConnectWithDataSource,
    checkAnalyseStatus,
    checkDataSourceAvailability,
  } = useDataSource();

  const [promptTestCaseLoading, setPromptTestCaseLoading] = useState(false);

  const { businessTestCaseModal, setBusinessTestCaseModal } =
    useBusinessTestCase();
  const [isConnected, setIsConnected] = useState(false);
  const [showDataSourceWarningModal, setShowDataSourceWarningModal] =
    useState(false);

  const [chooseDataSourceModal, setChooseDataSourceModal] = useState(false);
  const [activePage, setActivePage] = useState(1);

  const userData: UserData = getDataFromLocalStorage(
    LOCAL_STORAGE_DATA_KEYS.USER_DATA
  ) as UserData;

  const isStoppedRef = useRef(false);

  const handleButtonClick = (
    row: TestCase,
    rowIndex: number,
    type: "manual" | "automatic"
  ) => {
    runApiTest(rowIndex, row, type);
  };

  const handleSearchAPI = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  const fetchTestDetails = async (id: number) => {
    try {
      // setLoading(true);
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testSuites/${id}`
      );
      setTestSuitDetails(response.data.data || []);
      setIsConnected(response.data.data.is_datasource || false);

      const request = response.data.data.request;
      setApiVariables({
        headers: request.headers || {},
        path_params: request.path_params || {},
        query_params: request.query_params || {},
        json_body: request.request_body || "{}",
      });

      setHeaders(JSON.stringify(request.headers, null, 10) ?? "{}");
      setPathParams(JSON.stringify(request.path_params, null, 10) ?? "{}");
      setQueryParams(JSON.stringify(request.query_params, null, 10) ?? "{}");
      setRequestBody(JSON.stringify(request.request_body, null, 10) ?? "{}");
      // setLoading(false);
    } catch (err) {
      // setLoading(false);
      console.log(err);
    }
  };

  const fetchTestCases = async (id: number, isFetchingBusinessCase = false) => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testCases/test-suite/${id}`
      );

      const securityResponse = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testCases/test-suite/${id}?type=SECURITY`
      );

      securityResponse.data.data = securityResponse.data.data.map((tc) => {
        return {
          ...tc,
          category: 4,
        };
      });

      response.data.data = [
        ...response.data.data,
        ...securityResponse.data.data,
      ];

      console.log({ securityResponse: response.data.data });

      response.data.data = response.data.data.map(
        (testCase: TestCase, idx: number) => {
          testCase.request = JSON.stringify(testCase.request, null, 10);
          testCase.expanded = expandedRows.current.includes(idx); // Retain expanded state
          return testCase;
        }
      );

      setTestCases(response.data.data || []);
      if (response.data?.data?.length > 0 && isFetchingBusinessCase) {
        setSelectedTab(1);
      }

      const tags = response.data.data.flatMap(
        (testCase: TestCase) => testCase.tags
      );
      const uniqueTags = [...new Set(tags)];

      // Check if the "Tags" filter already exists
      const existingTagsFilter = filters.find(
        (filter) => filter.type === "Tags"
      );

      const newTagsOptions = uniqueTags.map((tag: string) => ({
        name: tag,
        value: tag,
      }));

      let mergedFilters;
      if (existingTagsFilter) {
        // Update the existing "Tags" filter with unique options
        existingTagsFilter.options = [
          ...new Set(
            [...existingTagsFilter.options, ...newTagsOptions].map((opt) =>
              JSON.stringify(opt)
            )
          ),
        ].map((opt) => JSON.parse(opt));

        mergedFilters = filters.map((filter) =>
          filter.type === "Tags" ? existingTagsFilter : filter
        );
      } else {
        // Add a new "Tags" filter if it doesn't exist
        mergedFilters = [
          ...filters,
          {
            type: "Tags",
            options: newTagsOptions,
          },
        ];
      }

      setFilters(mergedFilters);

      await calculateStats(response.data.data);

      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.log(err);
    }
  };

  const fetchGroupDetails = async () => {
    if (groupId) {
      try {
        const response = await axios.get(
          `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/groups/${groupId}`
        );
        setEnvironment(response.data.data.env_id);
      } catch (err) {
        console.log(err);
      }
    }
  };

  const generateTestCases = async (id: number) => {
    try {
      const data = {
        apiList: [
          {
            test_suite_id: id,
          },
        ],
        userId: userData.user_id,
      };
      // const data = [
      //   {
      //     test_suite_id: id,
      //   },
      // ];
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testCases/bulkGenerateTestCases`,
        data
      );

      if (response?.status === 202) {
        setTimeout(() => {
          fetchTestCases(id);
          fetchTestDetails(id);
        }, 100);
      }
    } catch (error) {
      // toast.error("Something went wrong, please try again later.");
      showToast("Something went wrong, please try again later.", "error");
    }
  };

  const currentTestCases = useMemo(() => {
    const tc = testCases
      .filter((item) =>
        item.description.toLowerCase().includes(searchInput.toLowerCase())
      )
      .filter((testCase) => {
        if (selectedFilter?.type !== "Result" || !selectedFilter) {
          return true;
        }
        const assertions = getAssertions(testCase);
        if (selectedFilter.value === "Passed") {
          return (
            assertions.every(({ passed, success }) => passed || success) &&
            assertions.length > 0
          );
        } else if (selectedFilter.value === "Failed") {
          return assertions.some(({ passed }) => !passed);
        } else if (selectedFilter.value === "Not Run") {
          return assertions.length === 0;
        } else if (selectedFilter.value === "Executed") {
          return assertions.length > 0;
        } else {
          return true;
        }
      })
      .filter((testCase) => {
        return (
          selectedTags.length === 0 ||
          selectedTags.some((tag) => testCase.tags.includes(tag))
        );
      })
      .filter((testCase) => {
        return testCase.status !== "INACTIVE";
      })
      .sort((a, b) => {
        if (a.status === "PENDING" && b.status !== "PENDING") return -1;
        if (a.status !== "PENDING" && b.status === "PENDING") return 1;
        return 0; // Maintain existing order for non-pending items
      });

    return tc;
  }, [
    testCases,
    testCases.length,
    searchInput,
    selectedFilter,
    expandToggle,
    selectedTags,
  ]);

  const modifyRequestBodyRow = (
    rowIndex: number,
    body: string,
    testCase: TestCase
  ) => {
    setTestCases((prev) => {
      const previous = structuredClone(prev);
      const index = previous.findIndex((test) => test.id === testCase.id);
      previous[index].request = body;
      return previous;
    });
  };

  const modifyResponseBodyRow = (
    rowIndex: number,
    body: string,
    testCase: TestCase
  ) => {
    let index = testCases.findIndex((test) => test.id === testCase.id);
    setTestCases((prev) => {
      const previous = structuredClone(prev);
      previous[index].response = body;
      return previous;
    });
  };

  const modifyAssertionRow = (
    rowIndex: number,
    body: string,
    row: TestCase
  ) => {
    setTestCases((prev) => {
      const previous = structuredClone(prev);
      const index = previous.findIndex((test) => test.id === row.id);

      if (index === -1) {
        console.error(`Test case not found for rowId: ${row.id}`);
        return previous; // Return unchanged state if row is not found
      }

      previous[index].assertions = body;
      previous[index].assertions_results = row.assertions_results || [];
      return previous;
    });
  };

  const modifyAssertionResultsRow = async (
    rowIndex: number,
    result: TestAssertionEntity[],
    response?: any
  ) => {
    let updatedTestCase = testCases[rowIndex];
    setTestCases((prev) => {
      const previous = structuredClone(prev);
      if (!previous[rowIndex].assertions_results) {
        previous[rowIndex].assertions_results = [];
      }
      previous[rowIndex].assertions_results = structuredClone(result);
      previous[rowIndex].running = false;
      // previous[rowIndex].expanded = true;
      // previous[rowIndex].

      return previous;
    });

    updatedTestCase.assertions_results = result;

    console.log({ updatedTestCase });
    // await handleSaveRequest(updatedTestCase, "");
    try {
      const updateUrl = `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testCases/${updatedTestCase.id}`;
      await axios.put(updateUrl, {
        assertions: { assertions: updatedTestCase.assertions },
        assertions_results: updatedTestCase.assertions_results.map((item) =>
          JSON.stringify(item)
        ),
      });
    } catch (error) {}
  };

  const runApiTest = async (
    rowIndex: number,
    row?: TestCase,
    type: "automatic" | "manual" = "automatic"
  ) => {
    if (row?.running) {
      return;
    }

    // Find the row index using row.id if available and check type too
    const index = row?.id
      ? type === "automatic"
        ? testCases
            .filter((tc) => !tc.user_prompt)
            .findIndex((test) => test.id === row.id)
        : testCases
            .filter((tc) => !!tc.user_prompt)
            .findIndex((test) => test.id === row.id)
      : rowIndex;

    let testCase: TestCase;
    if (type === "automatic") {
      testCase = testCases.filter((tc) => !tc.user_prompt)[index];
    } else {
      testCase = testCases.filter((tc) => !!tc.user_prompt)[index];
    }

    if (index === -1) return; // If row is not found, exit early

    const variablesParsedUrl = injectDynamicVariables(testCases[index].url);

    console.log("log:: parsed url is ",variablesParsedUrl)
    if (variablesParsedUrl.includes("{{")) {
      // toast.error(
      //   "Please select required environment in the dropdown to run the test case, if not present in the dropdown, please add it in the environment variables section of the test suite."
      // );
      showToast("Please fix the environment variables in the URL.", "error");
      isStoppedRef.current = true;
      return;
    }

    let testResponse: any = {};
    let response: AxiosResponse;
    let elapsedTime: string;
    let errResponse: any;

    setRunning(true);
    let refrenceofLatestTestCase: any;
    const startTime = Date.now();

    try {
      setTestCases((prev) => {
        const previous = structuredClone(prev);
        previous[index].running = true;
        return previous;
      });

      response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/run/testcase`,
        {
          testCaseId: testCase.id,
          waitTime: waitTime,
        },
        {
          signal: abortControllerRef.current?.signal,
        }
      );

      const data = response.data?.data;

      const endTime = Date.now();
      elapsedTime =
        startTime !== undefined
          ? `${Math.round(endTime - startTime)}ms`
          : "N/A";

      // response: {
      //   response: responseBody,
      //   headers: responseHeaders,
      //   statusCode: status,
      //   ...(error !== null ? { error: error } : {}),
      //   elapsedTime: elapsedTime,
      // },

      console.log("log:: run test datav", data);

      // testResponse.response = data?.response?.response;

      testResponse = {
        ...data?.response,
      };
      testResponse.headers = data?.response?.headers;
      testResponse.statusCode = data?.response.statusCode || data?.statusCode;
      testResponse.elapsedTime = elapsedTime;

      setTestCases((prev) => {
        const previous = structuredClone(prev);
        // previous[index].running = false;
        // previous[index].expanded = true;
        previous[index].status_code = data?.statusCode?.toString();
        previous[index].response = testResponse;

        refrenceofLatestTestCase = previous;

        return previous;
      });

      console.log("log:: response body", testResponse);

      modifyResponseBodyRow(index, testResponse, testCase);
    } catch (err: any) {
      const endTime = Date.now();
      elapsedTime =
        startTime !== undefined
          ? `${Math.round(endTime - startTime)}ms`
          : "N/A";
      errResponse = err.response;
      testResponse.error = err.response?.data;
      testResponse.response = err.response?.data;
      testResponse.headers = err.response?.headers;
      testResponse.statusCode = err?.statusCode;
      testResponse.elapsedTime = elapsedTime;

      setTestCases((prev) => {
        const previous = structuredClone(prev);
        // previous[index].running = false;
        // previous[index].expanded = true;
        previous[index].status_code = err?.status?.toString() || ""; // Handle undefined status
        previous[index].response = testResponse;
        previous[rowIndex].assertions_results = [];

        refrenceofLatestTestCase = previous;

        return previous;
      });

      modifyResponseBodyRow(index, testResponse, testCase);
    }

    setTimeout(async () => {
      await generateAssertions(index, testResponse, row);
    }, 100);
  };

  const calculateStats = async (refrenceofLatestTestCase: any[]) => {
    let passed = 0,
      failed = 0,
      executed = 0;

    refrenceofLatestTestCase.forEach((testCase) => {
      const assertions = getAssertions(testCase);
      // console.log("log:: stats assertions ",assertions)
      if (!Array.isArray(assertions) || assertions.length === 0) return;

      executed++;
      if (assertions.some(({ message }) => message === "Processing...")) return;

      if (assertions.every(({ passed, success }) => passed || success)) {
        passed++;
      } else {
        failed++;
      }
    });

    setExecutedCount(executed);
    setPassedCount(passed);
    setFailedCount(failed);
  };

  const isAllRunningRef = useRef(false);

  const checkForDataAndRun = async () => {
    if (!testSuitDetails.is_datasource) {
      setChooseDataSourceModal(true);
      return;
    }
    if (isConnected) {
      const response = await connectWithDataSource();
      console.log("log:: response is ", response);
      if (!response.success) {
        setShowDataSourceWarningModal(true);
        return;
      }
    }
    await runAllTests();
  };

  const runAllTests = async () => {
    setIsAllRunning(true);
    isAllRunningRef.current = true;
    isStoppedRef.current = false;
    abortControllerRef.current = new AbortController();

    const apiTestCases = [...testCases].filter((tc) => !tc.user_prompt); // No prompt
    const businessTestCases = [...testCases].filter((tc) => !!tc.user_prompt); // Has prompt

    await Promise.all([
      runTestCasesInBatches(apiTestCases, "automatic"),
      runTestCasesInBatches(businessTestCases, "manual"),
    ]);

    setIsAllRunning(false);
    isAllRunningRef.current = false;
  };

  const runTestCasesInBatches = async (
    casesToRun: TestCase[],
    type: "automatic" | "manual"
  ) => {
    setIsAllRunning(true);
    isStoppedRef.current = false;
    abortControllerRef.current = new AbortController();

    let index = 0;

    const runNextBatch = async () => {
      if (isStoppedRef.current || abortControllerRef.current.signal.aborted)
        return;

      while (isPausedRef.current) {
        await delay(100);
      }

      const promises = [];
      for (let i = 0; i < concurrency; i++) {
        if (index >= casesToRun.length) break;

        const currentIndex = index;
        promises.push(runApiTest(currentIndex, casesToRun[currentIndex]), type);
        index++;
      }

      await Promise.all(promises);
      await delay(runDelay);

      if (index < casesToRun.length) {
        await runNextBatch();
      }
    };

    await runNextBatch();

    // setIsAllRunning(false);
  };

  const delay = (ms: number) =>
    new Promise((resolve) => setTimeout(resolve, ms));

  function injectDynamicVariables(template: string) {
    const variables: any = {};
    selectedEnvOptions.forEach((acc: any) => {
      variables[acc.label] = acc.value;
    });

    const parsedUrl = template.replace(/{{(.*?)}}/g, (match, variableName) => {
      const value = variables[variableName.trim()];
      return value !== undefined ? value : match;
    });

    return parsedUrl;
  }

  const generateAssertions = async (
    rowIndex: number,
    testResponse?: any,
    row?: TestCase
  ) => {
    setRunning(true);
    try {
      // Find the row index using row.id if available
      const index = row?.id
        ? testCases.findIndex((test) => test.id === row.id)
        : rowIndex;

      if (index === -1) return; // If row is not found, exit early

      const variablesParsedUrl = injectDynamicVariables(testCases[index].url);

      setTestCases((prev) => {
        const previous = structuredClone(prev);
        previous[index].assertions_results = [
          { passed: false, message: "Processing..." },
        ];
        return previous;
      });

      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/run/assertions`,
        {
          testCaseId: testCases[index].id,
          minElapsedTime: minElapsedTime,
        }
      );

      const data = response.data.data;

      const assertions = data.assertions;
      const assertionsResults = data.assertionResults;

      let updatedTestCase = testCases[index];
      updatedTestCase.assertions = assertions;
      updatedTestCase.assertions_results = assertionsResults;

      setTestCases((prev) => {
        const previous = structuredClone(prev);
        previous[index].assertions = assertions;
        previous[index].assertions_results = assertionsResults;
        previous[index].running = false;
        if (!isAllRunningRef.current) {
          previous[index].expanded = true;
        } else {
          previous[index].expanded = false;
        }
        calculateStats(previous);

        return previous;
      });

      if (!isAllRunningRef.current) {
        setDefaultTab(3);
      }
    } catch (error) {
      return "";
    } finally {
      setTestCases((prev) => {
        const previous = structuredClone(prev);
        previous[rowIndex].running = false;
        return previous;
      });
      setRunning(false);
    }
  };

  const onRowExpandToggle = (rowIndex: number, row: TestCase) => {
    setDefaultTab(0);
    setTestCases((prev) => {
      const newTestCases = structuredClone(prev);

      const rowIndex = newTestCases.findIndex((test) => test.id === row.id);
      if (rowIndex === -1) return prev; // If row is not found, return previous state

      newTestCases[rowIndex].expanded = !newTestCases[rowIndex].expanded;

      if (expandedRows.current.includes(row.id)) {
        expandedRows.current = expandedRows.current.filter(
          (id) => id !== row.id
        );
      } else {
        expandedRows.current = [...expandedRows.current, row.id];
      }

      return newTestCases;
    });

    setExpandToggle((prev) => !prev);
  };

  const handleDelete = async (row: TestCase) => {
    try {
      const deleteUrl = `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testCases/${row.id}`;
      const response = await axios.delete(deleteUrl);

      if (response.data.success) {
        // toast.success("Test case deleted successfully.");
        showToast("Test case deleted successfully.", "success");
        fetchTestCases(parseInt(testSuitId as string, 10));
      } else {
        // toast.error("Failed to delete test case.");
        showToast("Failed to delete test case.", "error");
      }
    } catch (error) {
      // toast.error("Failed to delete test case.");
      showToast("Failed to delete test case.", "error");
    }
  };

  const handleStop = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setIsAllRunning(false);
    setIsPaused(false);
    // setCurrentIndex(0)
    // setTestQueue([])
    // setFreshRun(true)
  };

  const handleSaveRequest = async (testSuite: TestCase, status?: string) => {
    try {
      // Reset statusCode, response, and assertions before saving
      const updatedTestSuite = {
        ...testSuite,
        statusCode: undefined,
        response: undefined,
        assertions: undefined,
        assertionResults: undefined,
        assertions_results: [],
        status: status || testSuite.status || "ACTIVE",
      };

      const updateUrl = `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testCases/${testSuite.id}`;
      await axios.put(updateUrl, {
        request: updatedTestSuite.request,
        response: {},
        assertions: null,
        assertions_results: [],
        status: status || testSuite.status || "ACTIVE",
        sync: true,
      });

      // Update the state with the reset values
      setTestCases((prevTestSuites) =>
        prevTestSuites
          .map((suite) =>
            suite.id === testSuite.id ? updatedTestSuite : suite
          )
          .filter((testCase) => testCase.status !== "INACTIVE")
      );

      if (!status) {
        // toast.success("Request saved successfully.");
        showToast("Request saved successfully.", "success");
      }
    } catch (error) {
      console.log("error", error);
      // toast.error("Failed to save request.");
      showToast("Failed to save request.", "error");
    }
  };

  // Update API variables
  const updateApiVariables = async () => {
    const errors = {
      headers: "",
      pathParams: "",
      queryParams: "",
      requestBody: "",
    };
    let isValid = true;

    // Validate JSON inputs
    try {
      JSON.parse(headers);
    } catch (error) {
      errors.headers = "Invalid JSON format.";
      isValid = false;
    }

    try {
      JSON.parse(pathParams);
    } catch (error) {
      errors.pathParams = "Invalid JSON format.";
      isValid = false;
    }

    try {
      JSON.parse(queryParams);
    } catch (error) {
      errors.queryParams = "Invalid JSON format.";
      isValid = false;
    }

    try {
      JSON.parse(requestBody);
    } catch (error) {
      errors.requestBody = "Invalid JSON format.";
      isValid = false;
    }

    // Set errors to state
    setJsonErrors(errors);

    if (!isValid) return;

    const data = {
      request: {
        headers: JSON.parse(headers),
        path_params: JSON.parse(pathParams),
        query_params: JSON.parse(queryParams),
        request_body: JSON.parse(requestBody),
      },
    };

    try {
      const updateUrl = `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/apiVariables/updateApiVariables/${testSuitId}`;
      await axios.put(updateUrl, data);

      await fetchTestCases(parseInt(testSuitId as string, 10));
      await fetchTestDetails(parseInt(testSuitId as string, 10));

      // toast.success("Test Suite variables updated successfully.");
      showToast("Test Suite variables updated successfully.", "success");
      setVariableModal(false);
    } catch (error) {
      // toast.error("Failed to update Test Suite variables.");
      showToast("Failed to update Test Suite variables.", "error");
    }
  };

  const handleFilter = (type: "Result" | "Tags" | "", value: string) => {
    if (type === "Result" || type === "") {
      if (selectedFilter.value === value) {
        setSelectedFilter({
          type: "",
          value: "",
        });
      } else {
        setSelectedFilter({
          type,
          value,
        });
      }
    }

    if (type === "Tags") {
      setSelectedTags((prev) => {
        if (prev.includes(value)) {
          return prev.filter((tag) => tag !== value);
        } else {
          return [...prev, value];
        }
      });
      console.log("filters sett selectedTags ", selectedTags);
    }
  };

  const handleSaveName = async () => {
    try {
      const response = await axios.put(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testSuites/${testSuitDetails?.id}`,
        {
          test_suite_name: suiteName,
        }
      );

      if (response.data.success) {
        setTestSuitDetails((prev) => {
          return {
            ...prev,
            test_suite_name: suiteName,
          };
        });
        // toast.success("Test suite name updated successfully.");
        showToast("Test suite name updated successfully.", "success");
        setIsEditingName(false);
        triggerRevalidateTestsExplorer();
      } else {
        // toast.error("Failed to update test suite name.");
        showToast("Failed to update test suite name.", "error");
      }
    } catch (error) {
      // toast.error("Failed to update test suite name.");
      showToast("Failed to update test suite name.", "error");
    }
  };

  const handlePause = () => {
    setIsPaused(true);
  };

  const handleResume = () => {
    setIsPaused(false);
  };

  const concurrencyFunction = (value: number) => {
    if (value > 5) {
      setConcurrency(5);
    } else if (value < 1) {
      setConcurrency(1);
    } else {
      setConcurrency(value);
    }
  };

  const getEnvironmentList = async () => {
    try {
      //showLoader(true);
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables/user/${userData.user_id}`
      );
      const responseData = response.data;
      // Find the selected environment based on matching id
      const selectedEnv = responseData?.find(
        (item: any) => item.id === environment
      );

      if (selectedEnv) {
        setSelectedEnvironment(selectedEnv.name);

        const envObj = selectedEnv.env;
        Object.keys(envObj).forEach((key) => {
          setSelectedEnvOptions &&
            setSelectedEnvOptions((prev) => {
              return [...prev, { value: envObj[key], label: key }];
            });
        });
      }
    } catch (err) {
      console.error("Error fetching environments:", err);
    } finally {
      //showLoader(false);
    }
  };

  const settingsOptionsList = [
    {
      name: "Edit Environment",
      id: 1,
      onClick: () => {
        // router.push(`/environment/${environment}`);
        setEnvModal(true);
      },
    },
    {
      name: "Edit Run time Settings",
      id: 2,
      onClick: () => setRunTimeSettingsModal(true),
    },
    {
      name: "Edit Test Suite Variables",
      id: 3,
      onClick: () => setVariableModal(true),
    },

    {
      name: "Edit Data source settings",
      id: 4,
      onClick: () => setDataSourceModal(true),
    },
  ];

  const runAssertionsLocally = (
    rowIndex: number,
    response: any,
    row?: TestCase
  ) => {
    console.log({ response });
    const index = row?.id
      ? testCases.findIndex((test) => test.id === row.id)
      : rowIndex;

    if (index === -1 || !testCases[index]) {
      console.error(
        `Test case not found for rowIndex: ${rowIndex} or row.id: ${row?.id}`
      );
      return;
    }

    const testCaseAssertion = (
      (testCases[index].assertions as any)?.assertions?.assertions ??
      (testCases[index].assertions as any)?.assertions ??
      (testCases[index].assertions as any)
    )?.replace("```", "");

    if (!testCaseAssertion?.trim()) {
      console.log("No assertions to run");
      return;
    }

    const assertionResults: TestAssertionEntity[] = [];

    try {
      // Define test helpers
      const it = (description: string, testFn: () => void) => {
        try {
          testFn();
          assertionResults.push({ message: description, passed: true });
          console.log("Test passed", { description });
        } catch (err: any) {
          console.log("Test failed", { description, error: err.message });
          assertionResults.push({
            message: description,
            passed: false,
          });
        }
      };

      const describe = (description: string, suiteFn: () => void) => {
        try {
          console.log(`Running test suite: ${description}`);
          suiteFn();
        } catch (err) {
          console.error(`Error in describe block "${description}":`, err);
        }
      };

      const before = (fn: () => void) => {};
      const after = (fn: () => void) => {};
      const beforeEach = (fn: () => void) => {};
      const afterEach = (fn: () => void) => {};

      // Execute the test script in an isolated environment
      const testRunner = new Function(
        "it",
        "expect",
        "assert",
        "response",
        "describe",
        "before",
        "after",
        "beforeEach",
        "afterEach",
        testCaseAssertion
      );

      testRunner(
        it,
        expect,
        assert,
        response,
        describe,
        before,
        after,
        beforeEach,
        afterEach
      );
    } catch (err) {
      console.error("Test execution failed:", err);
    }

    modifyAssertionResultsRow(index, assertionResults, response);
  };

  const connectWithDataSource = async () => {
    try {
      const analyseData = await checkAnalyseStatus();

      if (analyseData.data?.status === "READY") {
        const response = await handleConnectWithDataSource();
        if (response.success) {
          await fetchTestCases(parseInt(testSuitId as string, 10), true);
          showToast("Connected with data source successfully.", "success");
        }
        return {
          success: response.success,
          message: response.message,
        };
      } else {
        showToast(
          `${
            analyseData.data?.totalTestCases -
            analyseData.data?.analyzedTestCases
          } test cases are still being analyzed. Please wait.`,
          "error"
        );
        return {
          success: false,
          message: `${
            analyseData.data?.totalTestCases -
            analyseData.data?.analyzedTestCases
          } test cases are still being analyzed. Please wait.`,
        };
      }
    } catch (error) {
      console.error(
        "Error connecting with data source:",
        error?.response?.data?.message
      );
      return {
        success: false,
        message:
          error?.response?.data?.message ||
          "Failed to connect with data source.",
      };
      // showToast(
      //   error?.response?.data?.message || "Failed to connect with data source.",
      //   "error"
      // );
    }
  };

  const handleResetAnalysisData = async () => {
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.RESET_ANALYZE_DATA_SOURCE_USAGE}/${testSuitId}/reset-analysis`
      );

      if (response.data.success) {
        showToast("Reset analysis data successfully.", "success");
        await fetchTestCases(parseInt(testSuitId as string, 10), true);
      }
    } catch (error) {}
  };

  const handleAnalyseData = async () => {
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.ANALYZE_CSV_DATA}/${testSuitId}`
      );

      if (response.data.success) {
        showToast("data successfully analysed", "success");
        await fetchTestCases(parseInt(testSuitId as string, 10), true);
      }
    } catch (error) {}
  };

  const handleDataSourceFeature = () => {
    if (testSuitDetails?.is_datasource) {
      router.push(`${pathname}/dataSource?isEdit=true`);
    } else {
      setRealTestDataModal(true);
      localStorage.removeItem("csvData");
      localStorage.removeItem("csvUniqueHeaders");
      localStorage.removeItem(`${testSuitId}_stepsArray`);
      localStorage.removeItem(`${testSuitId}_typesArray`);
      localStorage.removeItem(`${testSuitId}_csvKeyTypes`);
      setUniqueHeaders([]);
      setRealTestData(null);
    }

    localStorage.setItem("tooltipClicked", "true");
  };

  const columns = useMemo(() => {
    return [
      {
        displayName: "Description",
        accessor: "description",
        dataRender: (row: TestCase) => (
          <div className="col-span-2">
            <p className="text-[12px] leading-none mb-2">{row.description}</p>

            <div className="text-[10px] flex gap-1.5 items-center  ">
              {row.tags &&
                row.tags.map((tag: string) => {
                  const displayTag =
                    tag.length > 20 ? `${tag.substring(0, 20)}...` : tag;
                  return (
                    <span
                      key={tag}
                      className="bg-drcodePurple/20  whitespace-nowrap   text-[10px] p-[1px] px-[3px] rounded-md"
                    >
                      {displayTag.toUpperCase()}
                    </span>
                  );
                })}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedTestCaseForTagEdit(row);
                  setOpenTagEditorModal(true);
                }}
                title="Edit Tags"
              >
                <RiEdit2Fill className="text-[#B2B2C1] text-base" />
              </button>
            </div>
          </div>
        ),
        colSpan: 4,
      },

      {
        displayName: "",
        accessor: "actions",
        dataRender: (row: TestCase, rowIndex: number) => (
          <div className="pl-14">
            {row.status === "PENDING" ? null : (
              <>
                {row.running ? (
                  <button className="button--outlined-primary px-2 py-2 flex items-center gap-2">
                    Running... <Spinner size="sm" />
                  </button>
                ) : (
                  <ButtonOutlinedPrimary
                    disabled={isAllRunning}
                    text="Run"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleButtonClick(
                        row,
                        rowIndex,
                        row.user_prompt ? "manual" : "automatic"
                      );
                    }}
                  />
                )}
              </>
            )}
          </div>
        ),
        colSpan: 3,
      },
      {
        displayName: "Status code",
        accessor: "status_code",
        colSpan: 3,
        dataRender: (row) => {
          if (row.status !== "PENDING")
            return (
              <div className="flex items-center">
                {/* <span className={`${styles["status-code"]}`}>
                {row?.status_code ?? row?.response?.statusCode}
              </span> */}

                <div
                  className={`border ${
                    !row.status_code ? "text-error-100" : "text-[#C1C1CB]"
                  } border-[#3F3F47] font-semibold font-sans text-[10px] px-2 rounded-md py-1 min-w-[40px] text-center`}
                >
                  {row?.response?.statusCode == null
                    ? row?.response && Object.keys(row.response).length > 0
                      ? "ERROR"
                      : "—"
                    : row.response.statusCode == 0
                    ? "ERROR"
                    : row.response.statusCode}
                </div>

                {/* Passed/Failed/Not Run Status */}
                {(() => {
                  const assertions = getAssertions(row);

                  return assertions.length > 0 ? (
                    assertions.some(
                      (assertion: any) => assertion.message === "Processing..."
                    ) ? (
                      <div className="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 rounded-md text-sm">
                        Processing
                      </div>
                    ) : assertions.every(
                        (assertion: any) =>
                          assertion.passed || assertion.success
                      ) ? (
                      <div className="ml-2 px-2 py-1 bg-green-100 text-green-800 rounded-md text-sm">
                        Passed
                      </div>
                    ) : (
                      <div className="ml-2 px-2 py-1 bg-red-100 text-red-800 rounded-md text-sm">
                        Failed
                      </div>
                    )
                  ) : (
                    <div className="ml-2 px-2 py-1 bg-vscode-editor-background text-gray-500 rounded-md text-sm">
                      Not Run
                    </div>
                  );
                })()}
              </div>
            );
        },
      },
      {
        displayName: "Actions",
        accessor: "actions",
        colSpan: 3,
        dataRender: (row: TestCase, rowIndex: number) => {
          if (row.status === "PENDING")
            return (
              <div className="flex gap-4 ml-[15px] relative z-10">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSaveRequest(row, "ACTIVE");
                  }}
                  title="Accept test case"
                  className="bg-drcodePurple p-1 rounded-md cursor-pointer"
                >
                  <RxCheck size={22} color="white" />
                </button>
                <button
                  title="Reject test case"
                  className="border-1 border-drcodePurple p-1 rounded-md cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSaveRequest(row, "INACTIVE");
                  }}
                >
                  <RxCross1 size={22} color="white" />
                </button>
              </div>
            );
          return (
            <>
              <RiDeleteBin6Line
                onClick={(e) => {
                  e.stopPropagation();
                  handleDelete(row);
                }}
                size={16}
              />

              {showAnalyzeData && (
                <>
                  {row?.analysis?.fields?.map((field, index) => (
                    <div
                      key={index}
                      className="p-1 border-b border-gray-300 text-[10px]"
                    >
                      <p>
                        <strong>Path:</strong> {field.path}
                      </p>
                      <p>
                        <strong>Type:</strong> {field.type}
                      </p>
                      <p>
                        <strong>Expect:</strong> {field.expect}
                      </p>
                    </div>
                  ))}
                </>
              )}
            </>
          );
        },
      },
      {
        displayName: "",
        accessor: "expand",
        colSpan: 3,
        dataRender: (row: TestCase, rowIndex: number) => {
          // if(row.status === "PENDING") return null;
          return (
            <div className="flex justify-center items-center ml-10">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onRowExpandToggle(rowIndex, row);
                }}
              >
                {row.expanded ? (
                  <RiArrowUpSLine size={16} />
                ) : (
                  <RiArrowDownSLine size={16} />
                )}
              </button>
            </div>
          );
        },
      },
    ];
  }, [handleButtonClick]);

  const [activeTab, setActiveTab] = useState<"api" | "business">("api");
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(
    null
  );

  const filteredTestCases = currentTestCases
    .filter((tc) => (activeTab === "api" ? !tc.user_prompt : !!tc.user_prompt))
    .filter((tc) => {
      if (activeTab === "api") {
        return (
          selectedCategoryId === null || tc.category === selectedCategoryId
        );
      }
      return true; // for business tab, ignore category filtering
    });

  const navigationItems: NavigationItem[] = [
    {
      icon: "custom_test_case.svg",
      title: "Custom Test Case",
      onClick: () => {
        testSuitDetails.test_case_generation !== "running" &&
          testCases.length >= 0 &&
          setBusinessTestCaseModal(true);
      },
    },
    {
      icon: testSuitDetails?.is_datasource ? "edit_csv.svg" : "csv_upload.svg",
      title: testSuitDetails?.is_datasource ? "Edit Data" : "Live Data Test",
      onClick: handleDataSourceFeature,
      tooltip:
        localStorage.getItem("tooltipClicked") === "true" ? (
          <></>
        ) : (
          <>
            <div className="w-72 rounded-lg bg-[linear-gradient(115.34deg,_#5635AA_1.26%,_#41297F_66.07%)] p-4 text-white shadow-lg relative">
              {/* Triangle arrow on the right side */}
              <div
                className="absolute top-1/2 -right-3 -translate-y-1/2 w-0 h-0 
                      border-y-[10px] border-y-transparent
                      border-l-[12px] border-l-purple-600"
              ></div>

              <div className="flex items-center justify-between">
                <h2 className="text-lg font-medium">Live Data Test</h2>
                <button className="text-white/80 hover:text-white">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>

              <p className="mt-2 text-sm text-white/80">
                Test your API with real-world data. Upload a CSV, map fields,
                and simulate real user inputs.
              </p>

              <button
                onClick={handleDataSourceFeature}
                className="mt-4  rounded-lg bg-white py-2 px-3 font-medium text-[#6041B0] "
              >
                Live Data Test
              </button>
            </div>
          </>
        ),
      tooltipWidth: "288px",
    },
  ];

  useEffect(() => {
    if (testSuitId) {
      fetchTestDetails(parseInt(testSuitId as string, 10));
      fetchTestCases(parseInt(testSuitId as string, 10));
      fetchGroupDetails();
    }
  }, [testSuitId]);

  useEffect(() => {
    const interval = setInterval(() => {
      if (
        testSuitDetails?.test_case_generation === "running" ||
        testSuitDetails?.test_case_generation === "inqueue"
      ) {
        fetchTestCases(parseInt(testSuitId as string, 10));
        fetchTestDetails(parseInt(testSuitId as string, 10));
      }
    }, 20000);

    return () => clearInterval(interval);
  }, [testSuitDetails, isAllRunning]);

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const isPausedRef = useRef(isPaused);
  useEffect(() => {
    isPausedRef.current = isPaused;
  }, [isPaused]);

  useEffect(() => {
    getEnvironmentList();
  }, [environment]);

  const hasGenerated = useRef(false);

  useEffect(() => {
    if (
      !hasGenerated.current &&
      params.get("isPlayground") &&
      testSuitDetails?.test_case_generation === "NOT STARTED"
    ) {
      hasGenerated.current = true;
      generateTestCases(parseInt(testSuitId as string, 10));
    }
  }, [params, testSuitDetails]);

  if (!testSuitDetails) {
    return <></>;
  }

  if (
    testCases.length === 0 &&
    testSuitDetails?.test_case_generation === "inqueue"
  ) {
    return <TestCaseLoader isQueue={true} />;
  }

  if (
    testCases.length === 0 &&
    testSuitDetails?.test_case_generation === "running"
  ) {
    return <TestCaseLoader />;
  }

  if (
    testSuitDetails &&
    (testSuitDetails.test_case_generation === "NOT STARTED" ||
      (testSuitDetails.test_case_generation === "completed" &&
        testCases.length === 0))
  ) {
    return (
      <div className=" flex flex-col mt-[12rem] ml-10 gap-5 items-center justify-center w-full">
        <GenerateTestCasesSvg />
        <button
          className="border border-[#875bf8] bg-[#875bf8]  text-xs font-bold text-white rounded-md py-2 px-3 hover:bg-[#875bf8]/50"
          onClick={(e) => {
            e.stopPropagation();
            generateTestCases(parseInt(testSuitId as string, 10));
          }}
        >
          Generate Test Cases
        </button>
      </div>
    );
  }

  return (
    <>
      <div className="flex w-full">
        <div className="h-[100vh] pr-2 pb-20 overflow-auto w-full">
          <div className="flex justify-between items-top">
            <div className="my-2">
              {isEditingName ? (
                <div className="flex gap-4 items-center min-w-[400px] ">
                  <PrimaryInput
                    className=" border-white border-1 text-white px-2 py-1 text-[24px] max-w-[350px] w-[100%]"
                    value={suiteName}
                    onChange={(e) => setSuiteName(e.target.value)}
                    placeholder="Enter test suite name"
                  />
                  <span
                    onClick={handleSaveName}
                    className="cursor-pointer text-[14px] text-drcodePurple hover:border-b-1 hover:border-drcodePurple"
                  >
                    Save
                  </span>
                  <span
                    onClick={() => {
                      setIsEditingName(false);
                      setSuiteName("");
                    }}
                    className="cursor-pointer text-[14px] hover:border-b-1"
                  >
                    Cancel
                  </span>
                </div>
              ) : (
                <h2 className="text-white text-[22px] flex items-center gap-4 pl-4">
                  {testSuitDetails?.test_suite_name}{" "}
                  <span
                    className="cursor-pointer"
                    onClick={() => {
                      setIsEditingName(true);
                      setSuiteName(testSuitDetails?.test_suite_name);
                    }}
                  >
                    <FaPencilAlt size={16} />
                  </span>
                </h2>
              )}
              {/* <p className="text-[14px] mt-2">
              These are the tests generated for your API.
            </p> */}
            </div>
            <StatListTabView
              allTestCount={
                testSuitDetails.test_case_generation === "running"
                  ? 0
                  : testCases.length
              }
              executedCount={executedCount}
              failedCount={failedCount}
              passedCount={passedCount}
              onFilter={handleFilter}
              selectedFilter={selectedFilter.value}
            />
          </div>

          {/* <div className="my-4">
            <StatList
              allTestCount={
                testSuitDetails.test_case_generation === "running"
                  ? 0
                  : testCases.length
              }
              executedCount={executedCount}
              failedCount={failedCount}
              passedCount={passedCount}
              onFilter={handleFilter}
              selectedFilter={selectedFilter.value}
            />
          </div> */}

          <div className="my-4 flex justify-between">
            <div className="flex flex-1 gap-3 items-center">
              <SearchInput
                value={searchInput}
                onChangeHandler={handleSearchAPI}
              />

              <FloatingDropdown
                buttonContent={
                  <>
                    <button className="border w-30 items-center justify-center border-drcodePurple rounded-lg flex gap-2 px-2  p-1 text-sm text-drcodePurple font-semibold hover:bg-drcodePurple/30 py-1.5">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12.6667 2H3.33334C2.39053 2 1.91913 2 1.62624 2.2748C1.33334 2.5496 1.33334 2.99188 1.33334 3.87644V4.33632C1.33334 5.02821 1.33334 5.37416 1.50641 5.66095C1.67948 5.94773 1.99566 6.12572 2.62802 6.4817L4.57004 7.57492C4.99432 7.81376 5.20646 7.93318 5.35835 8.06505C5.67467 8.33966 5.8694 8.66234 5.95764 9.05811C6.00001 9.24816 6.00001 9.47054 6.00001 9.91529L6.00001 11.6949C6.00001 12.3013 6.00001 12.6045 6.16796 12.8409C6.33591 13.0772 6.6342 13.1938 7.23078 13.427C8.4832 13.9166 9.10941 14.1614 9.55471 13.8829C10 13.6044 10 12.9679 10 11.6949V9.91529C10 9.47054 10 9.24816 10.0424 9.05811C10.1306 8.66234 10.3254 8.33966 10.6417 8.06505C10.7936 7.93318 11.0057 7.81376 11.43 7.57492L13.372 6.4817C14.0044 6.12572 14.3205 5.94773 14.4936 5.66095C14.6667 5.37416 14.6667 5.02821 14.6667 4.33632V3.87644C14.6667 2.99188 14.6667 2.5496 14.3738 2.2748C14.0809 2 13.6095 2 12.6667 2Z"
                          stroke="#875BF8"
                          strokeWidth="1.5"
                        />
                      </svg>
                    </button>
                  </>
                }
              >
                <div className="max-h-[200px] overflow-y-auto">
                  {filters.map((filter) => {
                    return (
                      <>
                        <p
                          className={`${
                            filter.type === "Tags" ? "mt-[10px]" : ""
                          }`}
                        >
                          {filter.type}
                        </p>
                        <div className="my-1 ">
                          {filter.options.map((option) => {
                            return (
                              <p
                                className={`text-[12px] py-1 pl-4 text-white hover:bg-drcodePurple/60 cursor-pointer ${
                                  (selectedFilter.value === option.value ||
                                    (selectedTags.length > 0 &&
                                      selectedTags.includes(option.value))) &&
                                  "bg-drcodePurple/20 "
                                }`}
                                onClick={() => {
                                  handleFilter(filter.type, option.value);
                                }}
                              >
                                {option.name}
                              </p>
                            );
                          })}
                        </div>
                        <hr />
                      </>
                    );
                  })}
                </div>
              </FloatingDropdown>
            </div>

            <div className="flex gap-3 items-center">
              {/* {testSuitDetails.is_datasource && (
                <button
                  onClick={connectWithDataSource}
                  className="button--outlined-primary p-2 text-[14px] flex gap-2"
                >
                  <PiPlugsConnectedFill />
                  Connect with data source
                </button>
              )} */}

              <div className="flex gap-1 items-center">
                <ToggleSwitch
                  disabled={!testSuitDetails.is_datasource}
                  value={isConnected}
                  onToggle={(value) => setIsConnected(value)}
                  label="Connect Data"
                />

                <Tooltip
                  content="Link your data source to run tests with real values."
                  position="bottom"
                >
                  <IoMdInformationCircleOutline />
                </Tooltip>
              </div>
              {isAllRunning ? (
                <>
                  {!isPaused ? (
                    <button
                      onClick={handlePause}
                      className="inline-flex items-center gap-2 text-drcodePurple/80 font-semibold text-sm px-1"
                    >
                      <MdMotionPhotosPaused
                        title="Pause Run All"
                        className="text-2xl"
                      />
                    </button>
                  ) : (
                    <button
                      disabled={running}
                      onClick={handleResume}
                      className="inline-flex items-center gap-2 text-drcodePurple font-semibold text-sm px-1"
                    >
                      <FaPlayCircle
                        title="Resume Run All"
                        className="text-2xl"
                      />
                    </button>
                  )}

                  <button
                    onClick={handleStop}
                    className="inline-flex items-center gap-2  text-red-600 font-semibold text-sm px-1"
                  >
                    <FaStopCircle title="Stop Run All" className="text-xl" />
                  </button>
                </>
              ) : (
                <button
                  onClick={() => checkForDataAndRun()}
                  className="inline-flex items-center gap-2 px-5 p-1 rounded-lg border border-drcodePurple bg-[#875BF8] font-semibold text-sm hover:bg-drcodePurple/80 text-white disabled:cursor-not-allowed disabled:bg-[#2E2E60] disabled:opacity-[0.5] h-[36px]"
                  disabled={testSuitDetails?.test_case_generation === "running"}
                >
                  <svg
                    width="17"
                    height="16"
                    viewBox="0 0 17 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M11.1085 4.5581C13.2583 6.01956 14.3333 6.75029 14.3333 8.0001C14.3333 9.2499 13.2583 9.98063 11.1085 11.4421C10.515 11.8455 9.92637 12.2254 9.38547 12.5419C8.91094 12.8196 8.37353 13.1068 7.81712 13.3888C5.67231 14.4757 4.59991 15.0191 3.63808 14.4174C2.67625 13.8157 2.58884 12.5562 2.41401 10.037C2.36457 9.3246 2.33325 8.6262 2.33325 8.0001C2.33325 7.374 2.36457 6.6756 2.41401 5.96318C2.58884 3.44402 2.67625 2.18444 3.63808 1.58276C4.59991 0.981076 5.67231 1.52452 7.81712 2.61141C8.37352 2.89337 8.91094 3.18061 9.38547 3.45829C9.92637 3.77481 10.515 4.15466 11.1085 4.5581Z"
                      stroke="#FFFFFF"
                      stroke-width="1.5"
                    />
                  </svg>
                  Run all test cases
                </button>
              )}
              <FloatingDropdown
                buttonContent={
                  <button className="button--outlined-primary py-2 px-2 text-[12px] flex gap-2">
                    <RxGear color="#875bf8" size={16} />
                  </button>
                }
              >
                <ul>
                  {settingsOptionsList.map((settingsOption) => {
                    return (
                      <p
                        onClick={settingsOption.onClick}
                        className="font-semibold text-[11px] rounded-md p-1 cursor-pointer hover:bg-drcodePurple/20 text-white"
                      >
                        {settingsOption.name}
                      </p>
                    );
                  })}
                </ul>
              </FloatingDropdown>
            </div>
          </div>

          {(testSuitDetails.test_case_generation === "running" ||
            promptTestCaseLoading) && (
            <div>
              <p>
                {" "}
                {promptTestCaseLoading
                  ? "Generating Business Test cases"
                  : pollingStatus}
              </p>

              <ProgressBar />
            </div>
          )}

          {testSuitDetails.test_case_generation !== "running" &&
            testCases.length >= 0 && (
              <>
                {/* <div className="bg-drCodeDarkBlue px-4 py-2 mb-4 rounded-md text-[13px] font-normal flex gap-2 items-center">
                  <p className="">Want to add more test cases?</p>

                  <p
                    onClick={() => setBusinessTestCaseModal(true)}
                    className="text-[#9F7CF9] cursor-pointer border-b-1 border-[#9F7CF9] leading-3 pb-[2px] pt-[2px]"
                  >
                    Click here to enter new scenarios.
                  </p>
                </div> */}
              </>
            )}

          {/* {selectedFilter} */}

          {(selectedTags.length > 0 || selectedFilter.value) && (
            <div className="my-2 mb-4">
              <div className="flex gap-2">
                {selectedFilter.value && (
                  <div className="flex items-center bg-[#2E2E60] text-[#D1D1E3] min-w-[100px] h-[28px] px-3 py-0.5 rounded-[12px]">
                    <span className="text-[14px]">{selectedFilter.value}</span>
                    <button
                      onClick={() => setSelectedFilter({ type: "", value: "" })}
                      className="ml-2 text-drcodePurple hover:text-drcodePurple/80 text-[16px]"
                    >
                      {/* &times; */}
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 16 16"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M4.26665 12.6668L3.33331 11.7335L7.06665 8.00016L3.33331 4.26683L4.26665 3.3335L7.99998 7.06683L11.7333 3.3335L12.6666 4.26683L8.93331 8.00016L12.6666 11.7335L11.7333 12.6668L7.99998 8.9335L4.26665 12.6668Z"
                          fill="#D1D1E3"
                        />
                      </svg>
                    </button>
                  </div>
                )}
                {selectedTags.map((tag) => {
                  return (
                    <>
                      <div
                        key={tag}
                        className="flex items-center bg-[#2E2E60] min-w-[100px] text-[#D1D1E3] px-3 py-0.5 rounded-[12px]"
                      >
                        <span className="text-[14px] ">{tag}</span>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedTags((prevTags) =>
                              prevTags.filter((t) => t !== tag)
                            );
                          }}
                          className="ml-2 text-[16px]"
                        >
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M4.26665 12.6668L3.33331 11.7335L7.06665 8.00016L3.33331 4.26683L4.26665 3.3335L7.99998 7.06683L11.7333 3.3335L12.6666 4.26683L8.93331 8.00016L12.6666 11.7335L11.7333 12.6668L7.99998 8.9335L4.26665 12.6668Z"
                              fill="#D1D1E3"
                            />
                          </svg>
                        </button>
                      </div>
                    </>
                  );
                })}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedTags([]);
                    setSelectedFilter({ type: "", value: "" });
                  }}
                  className="text-drcodePurple hover:underline text-[12px]"
                >
                  Clear All
                </button>
              </div>
            </div>
          )}
          <div className="flex item-center w-full">
            <div className="w-[18%] pr-4">
              <p className="text-[12px] mb-2">TEST CASE CATEGORIES</p>

              {/* API Test Cases Tab */}
              <div>
                <div
                  className={`mb-2 cursor-pointer flex items-center gap-2 p-2 rounded-[8px] ${
                    activeTab === "api" ? "bg-[#1B1B41] text-[#E2E2ED]" : ""
                  }`}
                  onClick={() => {
                    setActiveTab("api");
                    setSelectedCategoryId(null);
                  }}
                >
                  <RxGear size={16} color="#E2E2ED" />
                  <span className="text-sm text-[#E2E2ED]">
                    API Test Cases (
                    {
                      testCases.filter(
                        (tc) => !tc.user_prompt && tc.category !== null
                      ).length
                    }
                    )
                  </span>
                </div>
                {/* Vertical line container */}
                <div className="relative flex flex-col">
                  {activeTab === "api" &&
                    Object.entries(categoriesMap).map(
                      ([key, category], index) => {
                        const isSelected = selectedCategoryId === Number(key);
                        return (
                          <div
                            key={index}
                            className={`flex items-anchor-center cursor-pointer relative mb-9`}
                            onClick={() => {
                              setSelectedCategoryId(Number(key));

                              setActivePage(1);
                            }}
                          >
                            <img
                              src={
                                index ==
                                Object.entries(categoriesMap).length - 1
                                  ? "/category_directory_last.svg"
                                  : "/category_directory_new.svg"
                              }
                              alt={`category_directory_${category}`}
                              className={`${
                                index ==
                                Object.entries(categoriesMap).length - 1
                                  ? "h-5 left-0"
                                  : "h-10 left-0"
                              } pl-3 top-0 absolute`}
                            />
                            <div
                              className={`top-0 left-8 text-[13px] p-1 rounded-md absolute ${
                                isSelected
                                  ? "bg-[#2C2C5A] text-white"
                                  : "hover:bg-[#1B1B41] hover:text-white"
                              }`}
                            >
                              {category}
                            </div>
                          </div>
                        );
                      }
                    )}
                </div>
              </div>

              {/* Business Test Cases Tab */}
              <div className="pt-4">
                <div
                  className={`cursor-pointer flex items-center gap-2 mb-2 p-2 rounded-[8px] ${
                    activeTab === "business"
                      ? "bg-[#1B1B41] text-[#E2E2ED]"
                      : ""
                  }`}
                  onClick={() => setActiveTab("business")}
                >
                  <img src="/business.svg" alt="Business" className="w-4 h-4" />
                  <span className="text-sm">
                    Business Test Cases (
                    {testCases.filter((tc) => tc.user_prompt).length})
                  </span>
                </div>

                {/* {activeTab === "business" &&
                  Array.from({ length: 5 }, (_, i) => (
                    <div key={`scenario-${i + 1}`} className="flex items-baseline gap-2">
                      <img
                        src="/category_directory.svg"
                        alt={`category_directory_scenario_${i + 1}`}
                        className="pl-3 w-8 h-8"
                      />
                      <span className="text-sm text-[#B2B2C1]">{`Scenario ${i + 1}`}</span>
                    </div>
                  ))} */}
              </div>
            </div>

            {/* Tab Content */}
            <div className="w-[84%]">
              <DrCodeTable
                activePage={activePage}
                setActivePage={setActivePage}
                className="w-full"
                data={filteredTestCases}
                columns={columns}
                onRowExpanded={onRowExpandToggle}
                rowExpandedRender={(row: TestCase, rowIndex) => (
                  <ApiTestDetailedView
                    key={row.id}
                    generateAssertions={generateAssertions}
                    modifyResponseBodyRow={modifyResponseBodyRow}
                    modifyAssertionRow={modifyAssertionRow}
                    modifyRequestBodyRow={modifyRequestBodyRow}
                    idx={rowIndex}
                    apiTestDetails={row}
                    runAssertionsLocally={runAssertionsLocally}
                    handleSaveRequest={handleSaveRequest}
                    running={running}
                    defaultTab={defaultTab}
                  />
                )}
              />
            </div>
          </div>
        </div>
        <div className="min-w-[6%]">
          <RightNavigation navigationItems={navigationItems} />
        </div>
      </div>

      {/* MODALS */}

      {openTagEditorModal && (
        <Modal
          isOpen={openTagEditorModal}
          title="Edit Tags"
          onClose={() => setOpenTagEditorModal(false)}
        >
          <TagEditor
            handleClose={() => setOpenTagEditorModal(false)}
            testCase={selectedTestCaseForEdit}
            onTagsUpdated={(updatedTags: any) => {
              // Update the test case's tags in state.
              setTestCases((prevTestSuites: TestCase[]) =>
                prevTestSuites.map((ts) =>
                  ts.id === selectedTestCaseForEdit.id
                    ? { ...ts, tags: updatedTags }
                    : ts
                )
              );
            }}
          />
        </Modal>
      )}

      {variableModal && (
        <Modal
          isOpen={variableModal}
          title="Edit API Variables"
          size="medium"
          footer={
            <div className="flex gap-2 my-2 items-end justify-end">
              <button
                onClick={() => setVariableModal(false)}
                className="button--outlined-primary mt-4 py-2 px-8 text-[14px]"
                style={{
                  fontSize: "14px",
                }}
              >
                Cancel
              </button>
              <button
                onClick={updateApiVariables}
                className="button--primary mt-4 py-2 px-8 text-[14px]"
                style={{
                  fontSize: "14px",
                }}
              >
                Update
              </button>
            </div>
          }
        >
          {/* <p>Update the API variables</p> */}

          {apiVariables && (
            <>
              <div className="flex flex-col space-y-4">
                {/* Headers */}
                <div className="space-y-2">
                  <label className="text-[14px]">Headers</label>
                  <ApiRequestResponseOutputView
                    background="#1B1B41"
                    value={headers}
                    onChange={(value) => {
                      setHeaders(value);
                    }}
                    responseType="json"
                    readonly={false}
                    height="15vh"
                  />
                  {jsonErrors.headers && (
                    <p className="text-red-500 text-sm">{jsonErrors.headers}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <label className="text-[14px]">Path Params</label>
                  <ApiRequestResponseOutputView
                    background="#1B1B41"
                    value={pathParams}
                    onChange={(value) => {
                      setPathParams(value);
                    }}
                    responseType="json"
                    readonly={false}
                    height="15vh"
                  />
                  {jsonErrors.pathParams && (
                    <p className="text-red-500 text-sm">
                      {jsonErrors.pathParams}
                    </p>
                  )}
                </div>
                {/* Query Params */}
                <div className="space-y-2">
                  <label className="text-[14px]">Query Params</label>
                  <ApiRequestResponseOutputView
                    background="#1B1B41"
                    value={queryParams}
                    onChange={(value) => {
                      setQueryParams(value);
                    }}
                    responseType="json"
                    readonly={false}
                    height="15vh"
                  />
                  {jsonErrors.queryParams && (
                    <p className="text-red-500 text-sm">
                      {jsonErrors.queryParams}
                    </p>
                  )}
                </div>
                {/* JSON Body */}
                <div className="space-y-2">
                  <label className="text-[14px]">Request Body</label>

                  <ApiRequestResponseOutputView
                    background="#1B1B41"
                    value={requestBody}
                    onChange={(value) => {
                      setRequestBody(value);
                    }}
                    responseType="json"
                    readonly={false}
                    height="15vh"
                  />
                  {jsonErrors.requestBody && (
                    <p className="text-red-500 text-sm">
                      {jsonErrors.requestBody}
                    </p>
                  )}
                </div>
              </div>
            </>
          )}
        </Modal>
      )}

      {envModal && (
        <Modal
          isOpen={envModal}
          title={
            <>
              <div className="font-bold text-xl text-vscode-editor-foreground">
                Edit Environment Variables
              </div>
              <p className="text-gray-500 text-sm">
                It's your environment, update the keys like a pro!
              </p>
            </>
          }
        >
          <EnvList
            environmentId={environment?.toString()}
            handleClose={() => setEnvModal(false)}
            setSelectedEnvOptions={setSelectedEnvOptions}
          />
        </Modal>
      )}

      {runTimeSettingsModal && (
        <Modal
          isOpen={runTimeSettingsModal}
          title={
            <>
              <div className="font-bold text-xl text-vscode-editor-foreground">
                Advanced Settings
              </div>
              <p className="text-gray-500 text-sm">
                Configure the test execution parameters.
              </p>
            </>
          }
        >
          <RuntimeSettings
            runDelay={runDelay}
            concurrency={concurrency}
            minElapsedTime={minElapsedTime}
            waitTime={waitTime}
            setWaitTime={setWaitTime}
            setMinElapsedTime={setMinElapsedTime}
            setRunDelay={setRunDelay}
            concurrencyFunction={concurrencyFunction}
            handleModalClose={() => setRunTimeSettingsModal(false)}
          />
        </Modal>
      )}

      {businessTestCaseModal && (
        <GenerateTestCaseFromPromptModal
          handleClose={() => setBusinessTestCaseModal(false)}
          fetchTestCases={fetchTestCases}
          testSuiteId={testSuitId as string}
          open={businessTestCaseModal}
          setPromptTestCaseLoading={setPromptTestCaseLoading}
          setSelectedTab={setSelectedTab}
        />
      )}

      {/* {realTestDataModal && (
        <FileMappingModal
          isOpen={realTestDataModal}
          handleClose={() => setRealTestDataModal(false)}
        />
      )} */}

      {realTestDataModal && (
        <BottomSheet
          isOpen={realTestDataModal}
          onClose={() => {
            setRealTestDataModal(false);
          }}
        >
          <FileMappingModal
            isOpen={realTestDataModal}
            handleClose={() => setRealTestDataModal(false)}
          />
        </BottomSheet>
      )}

      {dataSourceModal && (
        <Modal
          isOpen={dataSourceModal}
          title="Data Source Settings"
          onClose={() => setDataSourceModal(false)}
          showFooterButtons={true}
          onSave={() => setDataSourceModal(false)}
          onCancel={() => setDataSourceModal(false)}
        >
          <div className="space-y-4">
            {/* Switch + Label */}
            <div className="flex items-center space-x-4">
              <input
                type="checkbox"
                checked={showAnalyzeData}
                onChange={(e) => setShowAnalyzeData(e.target.checked)}
                className="h-6 w-6 rounded-full appearance-none bg-gray-300 checked:bg-drcodePurple relative transition-colors duration-300 cursor-pointer"
              />
              <p className="text-sm text-gray-600">
                {showAnalyzeData
                  ? "Analyze data is visible."
                  : "Analyze data is not visible."}
              </p>
            </div>

            {/* Reset Button */}
            <div>
              {testSuitDetails.datasource_status === "NOT_READY" ? (
                <button
                  className="button--outlined-primary py-2 px-8 text-[14px]"
                  onClick={handleAnalyseData}
                >
                  Analyze data
                </button>
              ) : (
                <button
                  className="button--outlined-primary py-2 px-8 text-[14px]"
                  onClick={handleResetAnalysisData}
                >
                  Reset Analysis Data
                </button>
              )}
            </div>
          </div>
        </Modal>
      )}

      {chooseDataSourceModal && (
        <DataSourcePopup
          onClose={() => setChooseDataSourceModal(false)}
          onCsvClick={() => {
            setChooseDataSourceModal(false);
            setRealTestDataModal(true);
          }}
          onAiClick={() => {
            setChooseDataSourceModal(false);
            runAllTests();
          }}
        />
      )}

      {showDataSourceWarningModal && (
        <Modal
          isOpen={showDataSourceWarningModal}
          showFooterButtons={true}
          saveLabel="Add Data"
          cancelLabel="Continue without data"
          onCancel={() => {
            setShowDataSourceWarningModal(false);
            runAllTests();
          }}
          onSave={() => {
            router.push(`${pathname}/dataSource?isEdit=true`);
            setShowDataSourceWarningModal(false);
          }}
        >
          <p>
            You don't have sufficient data to run the test. Please add data to
            the test case, or continue with the existing data.
          </p>
        </Modal>
      )}
    </>
  );
};

export default TestsTable;

const GenerateTestCasesSvg = () => {
  return (
    <svg
      width="100"
      height="100"
      viewBox="0 0 141 141"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M59.4372 1.15512C59.7113 1.10523 59.9247 1.27905 60.164 1.35303C60.1628 1.35331 60.1923 1.35129 60.1923 1.35129L60.2309 1.36787C60.3676 1.29871 60.4356 1.40024 60.5119 1.48104C60.8341 1.50602 61.1371 1.61197 61.4439 1.70202C61.5826 1.63952 61.6662 1.72151 61.7449 1.8166C62.0451 1.85266 62.3388 1.91426 62.6148 2.04345C62.8037 1.96942 62.9831 2.03332 63.1603 2.08713C65.4434 2.78075 67.6688 3.61761 69.8362 4.62505C71.6994 5.49109 73.4941 6.47395 75.2206 7.57637C77.4526 9.00151 79.5728 10.5805 81.5536 12.3466C83.1564 13.7757 84.6638 15.2972 86.0815 16.9053C87.2774 18.2619 88.3325 19.7307 89.3846 21.2019C90.5256 22.7975 91.514 24.4864 92.4291 26.2114C93.8624 28.9129 95.0123 31.7411 95.906 34.67C96.5539 36.7933 97.0686 38.9468 97.3984 41.1432C97.4091 41.2144 97.4448 41.2817 97.469 41.3508C97.4453 41.4323 97.4639 41.5066 97.5073 41.5768C97.6346 42.668 97.7837 43.757 97.8846 44.8507C98.1058 47.2491 98.1534 49.6517 97.9833 52.0565C97.932 52.7807 97.8851 53.5051 97.8361 54.2294C97.7958 54.301 97.7878 54.3756 97.8155 54.4534C97.6833 55.2793 97.5486 56.1048 97.4196 56.9311C96.8606 60.5144 95.8724 63.9758 94.524 67.3417C93.9766 68.7083 93.3101 70.0169 92.7047 71.355C92.6445 71.488 92.5931 71.6326 92.4636 71.7228C92.4441 71.6343 92.478 71.5342 92.3927 71.4411C91.9705 72.0161 91.7252 72.6824 91.3852 73.2953C90.8521 74.2565 90.233 75.1634 89.6545 76.095C89.2751 76.706 88.7994 77.2571 88.5286 77.9358C88.5015 78.0039 88.4745 78.081 88.424 78.1294C87.7878 78.7391 87.4262 79.5551 86.8341 80.2047C86.0089 81.1101 85.2399 82.0677 84.3901 82.9518C83.7694 83.5975 83.1249 84.2201 82.4928 84.8548C81.8096 85.5409 81.0051 86.0867 80.3007 86.7467C79.7422 87.2699 79.0312 87.5873 78.4976 88.1502C78.2785 88.3814 78.0099 88.6276 77.6729 88.7053C77.1103 88.8348 76.6477 89.1657 76.2078 89.4907C74.8753 90.4752 73.4232 91.2543 71.9925 92.0742C71.0855 92.5941 70.1084 92.9661 69.1925 93.465C68.3109 93.9454 67.3434 94.2265 66.4272 94.6298C65.5818 95.002 64.6681 95.2197 63.7828 95.5004C62.8636 95.7917 61.9492 96.0959 61.0094 96.3222C59.6203 96.6568 58.2124 96.8849 56.8096 97.1457C56.2276 97.2539 55.6378 97.32 55.0596 97.4518C54.9059 97.4868 54.8222 97.5397 54.8253 97.6998C54.377 97.8469 53.9066 97.8416 53.4454 97.8867C48.8253 98.3388 44.2433 98.057 39.6885 97.1852C35.9342 96.4665 32.3261 95.2913 28.8399 93.7413C26.8636 92.8626 24.9689 91.8262 23.1246 90.6894C21.0069 89.3841 19.0205 87.9046 17.1198 86.3104C15.9351 85.3167 14.8246 84.238 13.744 83.1269C12.0882 81.4241 10.5922 79.5924 9.2008 77.6736C8.13871 76.2088 7.17786 74.6772 6.29502 73.097C5.77181 72.1605 5.24821 71.2247 4.79942 70.2491C3.67897 67.8133 2.67562 65.3337 1.95272 62.7465C1.61649 61.5433 1.28044 60.3406 1.03034 59.1158C0.989404 58.9153 0.964988 58.7114 0.932732 58.512C0.932508 58.5149 0.950876 58.4849 0.950876 58.4849C0.898012 58.4036 0.850469 58.3218 0.930213 58.2295C0.978037 58.0121 0.804437 57.8388 0.811436 57.6295C0.743508 57.5479 0.731412 57.4546 0.75398 57.3541C0.872701 57.2363 1.13618 57.3121 1.16127 57.0348C1.03348 56.8619 0.839548 56.935 0.669868 56.9215C0.642651 56.7799 0.61538 56.6383 0.588388 56.5019C0.57954 56.2705 0.696691 56.0205 0.514131 55.8023C0.472355 55.744 0.463731 55.6823 0.495372 55.6164C0.487027 55.5742 0.478684 55.5322 0.47034 55.4902C0.370884 55.0728 0.301668 54.6516 0.311972 54.2205C0.3211 54.2103 0.339635 54.1976 0.337675 54.1905C0.331403 54.1675 0.317515 54.1467 0.306595 54.1249C0.301275 54.0577 0.295955 53.9905 0.290691 53.9237C0.213187 53.7405 0.325131 53.5341 0.222259 53.3534C0.165867 53.2274 0.161444 53.0958 0.18054 52.9627C0.0865715 52.6544 0.196723 52.3361 0.138931 52.0252C0.116475 51.917 0.0795152 51.8096 0.0736912 51.7005C-0.079357 48.825 -0.00106822 45.9559 0.375028 43.1002C0.667236 40.8816 1.04927 38.6776 1.63744 36.5149C2.2778 34.1604 3.05625 31.8513 4.03133 29.6143C4.88757 27.65 5.85699 25.7418 6.96203 23.9015C7.5861 22.8622 8.23038 21.8355 8.93447 20.8528C9.96543 19.4137 11.0484 18.0115 12.2227 16.6825C13.2849 15.4806 14.3914 14.32 15.5666 13.2308C16.7149 12.1666 17.9103 11.1543 19.1548 10.1997C20.8724 8.88229 22.665 7.67683 24.5322 6.58713C26.0611 5.69488 27.6453 4.90097 29.2699 4.19587C31.3562 3.29052 33.4982 2.546 35.6805 1.89567C38.465 1.06592 41.3176 0.645523 44.1826 0.264835C44.3106 0.247811 44.4424 0.259011 44.5722 0.257051C44.8819 0.194331 45.1941 0.258115 45.5044 0.238739C45.6991 0.226587 45.911 0.310195 46.082 0.146507C46.1835 0.0980109 46.2871 0.0868106 46.3939 0.128363C46.394 0.128363 46.444 0.117891 46.444 0.117891L46.4949 0.119571C46.8453 0.0170907 47.2015 0.0198908 47.5598 0.0602108C47.9275 0.162299 48.294 0.200323 48.6568 0.0390425C49.8982 -0.0430536 51.1353 0.0081303 52.3693 0.15681C52.5562 0.314619 52.7813 0.199483 52.9842 0.242883C53.0187 0.184251 53.052 0.190131 53.0851 0.244395C53.2485 0.235155 53.4128 0.188563 53.573 0.261251C53.7057 0.243387 53.8343 0.253243 53.9551 0.316579C54.0633 0.329459 54.1716 0.342339 54.2799 0.355219C54.3531 0.324531 54.4237 0.330299 54.4917 0.370843C54.5893 0.495331 54.6963 0.492027 54.8101 0.394083C55.0153 0.354771 55.2031 0.420795 55.3903 0.490347C55.6759 0.691051 56.0087 0.48581 56.3047 0.59669C56.4278 0.579666 56.5453 0.591035 56.6509 0.663611C56.728 0.677499 56.8053 0.691443 56.8825 0.705387C57.4453 0.736691 57.9965 0.838555 58.5413 0.978611C58.6334 0.998603 58.7255 1.01859 58.8176 1.03864C58.9167 1.02061 59.0112 1.03024 59.0986 1.08406C59.2115 1.10764 59.3244 1.13138 59.4372 1.15512ZM44.0735 86.0575C44.1287 86.3127 44.3363 86.3457 44.5419 86.3693C45.056 86.4283 45.5737 86.4586 46.0801 86.5751C46.3197 86.6301 46.4379 86.5377 46.4215 86.2873C46.8084 86.2641 47.1935 86.2718 47.574 86.3559C47.3723 86.4323 47.113 86.293 46.8414 86.5594C47.5582 86.5594 48.1647 86.5594 48.7915 86.5594C48.6944 86.4062 48.5581 86.4055 48.4377 86.3716C48.9215 86.3603 49.4054 86.345 49.8893 86.3381C52.5671 86.3003 55.1969 85.9228 57.8019 85.3036C59.8758 84.8106 61.8796 84.1243 63.8328 83.2868C66.3069 82.2258 68.6268 80.8832 70.8216 79.3279C71.4886 78.8552 72.1402 78.3544 72.7563 77.8177C73.9069 76.8156 75.035 75.7911 76.0904 74.6814C77.1719 73.5444 78.1845 72.355 79.1162 71.0994C80.3214 69.4752 81.3478 67.7364 82.2647 65.9327C82.4472 65.5737 82.5794 65.1848 82.8439 64.8697C82.8728 64.8103 82.9016 64.7509 82.9304 64.6915C82.8899 64.5882 82.9633 64.5174 83.0017 64.4356C83.5716 63.2222 84.0406 61.9665 84.4688 60.6999C84.928 59.3415 85.2398 57.9399 85.5457 56.5372C85.9688 54.5971 86.1978 52.6369 86.2618 50.6577C86.3531 47.8341 86.2013 45.0263 85.6601 42.2446C85.2604 40.1905 84.7493 38.1699 84.0238 36.2047C83.1294 33.7824 82.0053 31.4713 80.6452 29.2788C79.5029 27.4375 78.1773 25.7281 76.7316 24.1164C75.3972 22.6287 73.9581 21.2402 72.4004 19.9908C70.0963 18.1427 67.6131 16.57 64.9273 15.3209C62.7072 14.2884 60.4161 13.4764 58.04 12.8824C55.2006 12.1727 52.3246 11.8267 49.3994 11.7801C45.5574 11.7188 41.8062 12.27 38.1434 13.3902C33.9335 14.6777 30.0628 16.6499 26.5622 19.333C26.2831 19.5469 26.0214 19.7893 25.6966 19.9395C25.5789 19.8155 25.4857 19.8598 25.3644 19.9561C24.3803 20.7369 23.502 21.6346 22.5668 22.4689C21.8166 23.1382 21.217 23.9428 20.5142 24.6534C20.3705 24.7987 20.1601 24.9483 20.3062 25.214C20.1568 25.4243 20.0135 25.6392 19.857 25.8441C18.1797 28.0402 16.6655 30.3366 15.4687 32.8362C14.372 35.1267 13.5028 37.4934 12.8755 39.9565C11.8901 43.8254 11.5733 47.7527 11.8381 51.7224C12.0764 55.2953 12.8515 58.7614 14.1103 62.1205C15.5293 65.907 17.5261 69.3617 20.0481 72.5104C21.1202 73.8489 22.3264 75.07 23.5732 76.2531C24.8072 77.4241 26.117 78.4952 27.4997 79.4796C29.3104 80.7686 31.2314 81.8709 33.2413 82.8193C35.2001 83.7435 37.2154 84.5095 39.3152 85.054C40.8867 85.4615 42.4733 85.7895 44.0735 86.0575ZM49.0336 97.6633C49.0481 97.6633 49.0626 97.6634 49.0771 97.6634C49.0771 94.0561 49.0771 90.4489 49.0771 86.8417C49.0626 86.8417 49.0481 86.8416 49.0336 86.8416C49.0336 90.4489 49.0336 94.0561 49.0336 97.6633Z"
        fill="url(#paint0_linear_547_3492)"
      />
      <path
        d="M139.923 129.491C139.699 130.472 139.425 131.439 139.044 132.371C138.338 134.093 137.257 135.559 135.868 136.784C132.69 139.589 128.988 140.581 124.844 139.764C122.071 139.217 119.745 137.82 117.842 135.72C116.188 133.895 114.518 132.085 112.86 130.264C111.693 128.981 110.541 127.683 109.37 126.403C107.343 124.187 105.374 121.919 103.387 119.667C101.697 117.75 100 115.839 98.3242 113.91C96.807 112.164 95.3134 110.397 93.8098 108.639C92.3429 106.924 90.8682 105.215 89.4124 103.491C88.2692 102.137 87.1506 100.761 86.019 99.3974C84.9949 98.163 83.9628 96.9352 82.9442 95.6963C82.3887 95.0208 81.8539 94.3283 81.31 93.6431C81.3603 93.1305 81.213 92.9486 80.7434 92.9425C80.7434 92.9425 80.7333 92.9257 80.7334 92.9256C80.8658 92.7485 80.6432 92.5686 80.7307 92.4017C80.9028 92.2423 81.0709 92.0963 81.228 91.9392C84.5845 88.5849 87.941 85.2305 91.2929 81.8716C91.554 81.61 91.77 81.3039 92.0268 81.0378C92.3381 80.7153 92.349 80.7205 92.7846 80.9261C92.8882 80.9751 92.9568 80.9665 93.0186 80.8748C93.2179 80.9441 93.3596 81.0962 93.5177 81.224C94.6198 82.1146 95.726 83.0002 96.8209 83.8996C97.5552 84.5027 98.2667 85.1333 98.9997 85.7381C100.308 86.8176 101.633 87.8771 102.936 88.9633C104.21 90.0252 105.463 91.1121 106.729 92.1841C107.781 93.0755 108.842 93.9566 109.892 94.8502C112.021 96.6617 114.113 98.5166 116.221 100.353C118.231 102.105 120.226 103.873 122.215 105.649C123.341 106.654 124.475 107.651 125.599 108.66C126.68 109.631 127.754 110.611 128.828 111.59C130.274 112.91 131.731 114.218 133.158 115.558C134.394 116.719 135.714 117.792 136.853 119.054C138.257 120.607 139.17 122.421 139.68 124.443C140.047 125.902 140.188 127.378 139.968 128.877C139.955 128.968 139.965 129.062 139.964 129.155C139.923 129.264 139.905 129.375 139.923 129.491Z"
        fill="url(#paint1_linear_547_3492)"
      />
      <path
        d="M93.0191 80.8746C92.9572 80.9664 92.8886 80.9749 92.785 80.926C92.3494 80.7204 92.3384 80.7152 92.0273 81.0377C91.7704 81.3038 91.5544 81.6099 91.2934 81.8714C87.9414 85.2304 84.585 88.5848 81.2284 91.9391C81.0713 92.0961 80.9032 92.2421 80.7311 92.4016C80.6437 92.5685 80.8663 92.7484 80.7338 92.9255C79.7513 91.8299 78.8146 90.6958 77.8915 89.5502C77.7535 89.3788 77.609 89.2126 77.474 89.0517C76.6032 89.4519 75.914 90.0602 75.1406 90.5376C73.4274 91.595 71.6708 92.5668 69.8538 93.4344C67.0539 94.7713 64.1401 95.7829 61.1435 96.5745C59.2291 97.0802 57.2809 97.3916 55.325 97.6692C55.1604 97.6925 54.9921 97.6902 54.8255 97.6997C54.8224 97.5397 54.9062 97.4868 55.0598 97.4518C55.638 97.3199 56.2278 97.2539 56.8098 97.1457C58.2127 96.8849 59.6205 96.6568 61.0097 96.3222C61.9495 96.0958 62.8639 95.7917 63.7831 95.5003C64.6683 95.2197 65.5821 95.002 66.4274 94.6298C67.3437 94.2264 68.3111 93.9453 69.1928 93.465C70.1086 92.9661 71.0858 92.594 71.9927 92.0742C73.4234 91.2542 74.8754 90.4752 76.2081 89.4907C76.6479 89.1657 77.1106 88.8348 77.6732 88.7052C78.0101 88.6277 78.2788 88.3814 78.4979 88.1502C79.0314 87.5872 79.7424 87.2699 80.301 86.7466C81.0054 86.0867 81.8098 85.5409 82.4931 84.8548C83.1251 84.2201 83.7697 83.5974 84.3903 82.9518C85.2401 82.0677 86.0092 81.1101 86.8344 80.2047C87.4264 79.5551 87.7882 78.739 88.4242 78.1293C88.4747 78.0809 88.5017 78.0039 88.5289 77.9358C88.7997 77.2571 89.2753 76.706 89.6548 76.095C90.2332 75.1634 90.8523 74.2565 91.3855 73.2953C91.7255 72.6824 91.9708 72.016 92.393 71.441C92.4783 71.5342 92.4443 71.6342 92.4639 71.7228C92.026 72.6069 91.545 73.4668 91.0434 74.3167C90.4003 75.4063 89.6827 76.4474 88.9616 77.517C90.3036 78.678 91.7392 79.6833 93.0191 80.8746Z"
        fill="#31314E"
      />
      <defs>
        <linearGradient
          id="paint0_linear_547_3492"
          x1="49.0429"
          y1="0"
          x2="49.0429"
          y2="98.1"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#A380FF" />
          <stop offset="0.285" stop-color="#9975F4" />
          <stop offset="1" stop-color="#290781" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_547_3492"
          x1="110.391"
          y1="80.7819"
          x2="110.391"
          y2="140.041"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#A380FF" />
          <stop offset="0.285" stop-color="#9975F4" />
          <stop offset="1" stop-color="#290781" />
        </linearGradient>
        <radialGradient
          id="paint2_diamond_547_3492"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(39.8485 30.1882) rotate(78.2834) scale(460.349 538.124)"
        >
          <stop stop-color="white" />
          <stop offset="0.110143" stop-color="#875BF8" />
          <stop offset="1" stop-color="#11112C" />
        </radialGradient>
        <clipPath id="clip0_547_3492">
          <rect
            x="10.3042"
            y="11.6481"
            width="74.5361"
            height="74.5921"
            rx="37.268"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
