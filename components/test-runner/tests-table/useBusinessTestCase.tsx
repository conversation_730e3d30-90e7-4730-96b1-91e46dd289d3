import useToast from "@/hooks/useToast";
import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import axios from "axios";
import { useState } from "react";
import { toast } from "react-toastify";

const useBusinessTestCase = () => {
  const { showToast } = useToast();

  const [showEnhancedPrompt, setShowEnhancedPrompt] = useState(false);

  const [editPrompt, setEditPrompt] = useState<number | null>(null);
  const [promptsLoading, setPromptsLoading] = useState(false);
  const [userPrompt, setUserPrompt] = useState<string>("");
  const [businessTestCaseModal, setBusinessTestCaseModal] = useState(false);
  const [prompts, setPrompts] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [title, setTitle] = useState<string>("");

  const handleAddPrompt = () => {
    setPrompts((prev) => [...prev, ""]);
  };

  const handleRemovePrompt = (index: number) => {
    setPrompts((prev) => prev.filter((_, i) => i !== index));
  };

  const handlePromptChange = (index: number, value: string) => {
    setPrompts((prev) =>
      prev.map((prompt, i) => (i === index ? value : prompt))
    );
  };

  const handleEditPrompt = (index: number) => {
    setEditPrompt(index);
  };

  const handleGenerateTestCasesUsingPrompts = async ({
    testSuitId,
    prompts = [],
  }: {
    testSuitId: string | number;
    prompts: string[];
  }) => {
    try {
      setLoading(true);
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GENERATE_SINGLE_TESTCASE_FROM_PROMPT}`,
        { test_suite_id: testSuitId, prompts: prompts, title: title }
      );

      if (response.data.success) {
        showToast("Test cases generated successfully!", "success");
        return { success: true, data: response.data.data };
      } else {
        // showToast(
        //   "Error generating test cases. Please try again later.",
        //   "error"
        // );
        return { success: false, data: null };
      }
    } catch (error) {
      console.log("log:: error", error);
      // showToast(
      //   "Error generating test cases. Please try again later.",
      //   "error"
      // );
      return { success: false, data: null };
    } finally {
      setLoading(false);
    }
  };

  const handleEnhancedPrompt = async ({
    testSuitId,
    userPrompt,
  }: {
    testSuitId: string | number;
    userPrompt: string;
  }) => {
    try {
      setShowEnhancedPrompt(true);
      setPromptsLoading(true);

      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.ENHANCED_USER_PROMPT}`,
        {
          prompt: userPrompt,
          test_suite_id: testSuitId,
        }
      );

      setPrompts(response.data?.improvedPrompt || []);
      setTitle(response.data?.title || "");

      setPromptsLoading(false);
    } catch (error) {
      console.log("log:: error", error);
      showToast(
        "Error generating enhanced prompt. Please try again later.",
        "error"
      );
    }
  };

  return {
    businessTestCaseModal,
    prompts,
    loading,
    userPrompt,
    promptsLoading,
    showEnhancedPrompt,
    editPrompt,
    title,
    setUserPrompt,
    setLoading,
    setBusinessTestCaseModal,
    setPrompts,
    handleAddPrompt,
    handleRemovePrompt,
    handlePromptChange,
    handleGenerateTestCasesUsingPrompts,
    handleEnhancedPrompt,
    setShowEnhancedPrompt,
    handleEditPrompt,
    setEditPrompt,
  };
};

export default useBusinessTestCase;
