import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import axios from "axios";
import React, { useEffect, useState } from "react";

const useTestExecution = () => {
  const [runDelay, setRunDelay] = useState<number>(1024);
  const [waitTime, setWaitTime] = useState<number>(10000);
  const [concurrency, setConcurrency] = useState<number>(1);
  const [minElapsedTime, setMinElapsedTime] = useState<number>(500);

  const fetchRunSettings = async () => {
    try {
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/settingsConstants`
      );
      const data = response.data;
      setRunDelay(data.runDelay);
      setWaitTime(data.waitTime);
      setConcurrency(data.concurrency);
      setMinElapsedTime(data.minElapsedTime);
    } catch (error) {}
  };

  useEffect(() => {
    fetchRunSettings();
  }, []);

  return {
    runDelay,
    setRunDelay,
    waitTime,
    setWaitTime,
    concurrency,
    setConcurrency,
    minElapsedTime,
    setMinElapsedTime,
  };
};

export default useTestExecution;
