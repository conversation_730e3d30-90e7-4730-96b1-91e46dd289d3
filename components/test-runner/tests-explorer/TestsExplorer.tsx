"use client";
import React, { useEffect, useMemo, useState } from "react";
import styles from "./TestsExplorer.module.scss";
import SearchInput from "@/components/common/search-input/SearchInput";
import Image from "next/image";
import Select from "react-select";
import TestSuitList from "./blocks/testSuit-list/TestSuitList";
import { TestSuitItemProps } from "./blocks/testSuit-item/TestSuitItem";
import Link from "next/link";
import { useParams, usePathname } from "next/navigation";
import axios from "axios";
import { useGlobalStore } from "@/stores/globalstore";
import { useRouter } from "next/navigation";
import AddApiPostman from "@/components/add-api-postman/AddApiPostman";
import { ButtonOutlinedPrimary } from "@/components/common/button-outlined-primary/ButtonOutlinedPrimary";
import { ButtonPrimary } from "@/components/common/button-primary/ButtonPrimary";
import { toast } from "react-toastify";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import { FaBars } from "react-icons/fa";
import { RxCross2 } from "react-icons/rx";
import useToast from "@/hooks/useToast";
import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import { E2ETestSuitItemProps } from "./blocks/testSuit-item/E2ESuiteItem";

export const TestsExplorer = ({ handleCollapse = () => {} }) => {
  const [staticData, setStaticData] = useState<TestSuitItemProps[]>([]);
  const [data, setData] = useState<TestSuitItemProps[]>([]);
  const [e2eData, setE2eData] = useState<E2ETestSuitItemProps[]>([]);

  const [search, setSearch] = useState("");

  const { projectId, groupId, testSuitId } = useParams();

  const { showToast } = useToast();

  const {
    revalidateTestsExplorer,
    showLoader,
    triggerRevalidateTestsExplorer,
    setEnvironment,
  } = useGlobalStore();

  const [groupList, setGroupList] = useState<any>([]);
  const [selectedGroup, setSelectedGroup] = useState<any>(null);
  const [groups, setGroups] = useState<any>([]);

  const router = useRouter();
  const pathname = usePathname();

  const [postmanCollection, setPostmanCollection] = useState<any[]>([]);
  const [showAddImportPopup, setShowAddImportPopup] = useState<boolean>(false);

  const filterByName = (searchKey: string) => {
    if (!searchKey.trim()) {
      setData(staticData);
      return;
    }
    setData((prev) => {
      return prev.filter((item) => {
        return item.test_suite_name
          .toLowerCase()
          .includes(searchKey.trim().toLowerCase());
      });
    });
  };

  const onInputChange = (e: any) => {
    let searchKey = e.target.value;
    setSearch(searchKey);
    filterByName(searchKey);
  };

  const fetchTestSuites = async () => {
    try {
      let suitesResponse: any[] = (
        await axios.get(
          `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testSuites/group/${groupId}`
        )
      ).data?.data;

      let testSuitData: TestSuitItemProps[] = suitesResponse?.map((suit) => {
        return {
          id: suit.id,
          method: suit.method,
          test_suite_name: suit.test_suite_name,
          url: suit.url,
        };
      });
      console.log(testSuitId, "testSuitId");
      // if (testSuitData.length === 0) {
      //   router.push(
      //     `/project/${projectId}/group/${groupId}/testSuit/playground`
      //   );
      // }
      //  else if (
      //   !testSuitId &&
      //   !pathname?.includes("playground") &&
      //   !pathname?.includes("e2e")
      // ) {
      //   router.push(
      //     `/project/${projectId}/group/${groupId}/testSuit/${testSuitData[0].id}`
      //   );
      // }
      setStaticData(testSuitData);
      setData(testSuitData);
    } catch (err) {
      console.log(err);
      setStaticData([]);
      setData([]);
    }
  };

  const fetchE2ETestSuites = async () => {
    try {
      let e2eSuitesResponse: any[] = (
        await axios.get(
          `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.GET_E2E_BY_GROUP_ID}/${groupId}`
        )
      ).data?.data;

      let e2eSuitData: E2ETestSuitItemProps[] = e2eSuitesResponse?.map(
        (suit) => {
          return {
            id: suit.e2e_id,
            method: "E2E",
            description:
              suit.status === "pending"
                ? "Flow is being generated..."
                : suit.description,
            name: suit.name,
            status: suit.status,
          };
        }
      );

      setE2eData(e2eSuitData);
    } catch (error) {}
  };

  const getGropupByProjectId = async () => {
    try {
      //showLoader(true)
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/groups/project/${projectId}`
      );
      let filterList = response.data?.data.map((item: any) => {
        return { value: item.id, label: item.group_name };
      });
      setGroupList(filterList);
      setGroups(response.data?.data);
    } catch (err) {
      console.error("Error:", err);
    } finally {
      //showLoader(false)
    }
  };

  const onAddApi = async () => {
    if (postmanCollection.length === 0) {
      alert("Please Add postman collection");
      return;
    }

    const playgroundPayload: {
      projectId: string | string[];
      groupId: string | string[];
      method: string;
      testSuiteName: string;
      url: string;
      request: {
        headers: any;
        path_params: {};
        query_params: {};
        request_body: any;
      };
    }[] = [];

    playgroundPayload.push(...postmanCollection);

    setShowAddImportPopup(false);
    try {
      //showLoader(true);
      await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testSuites/bulk`,
        playgroundPayload
      );
      //showLoader(false);
      // toast.success("Added successfully");
      showToast("Added successfully", "success");
      triggerRevalidateTestsExplorer();
    } catch (err: any) {
      //showLoader(false);
      // toast.error(err.response?.data?.message || "Something went wrong");
      showToast(err.response?.data?.message || "Something went wrong", "error");
      console.log(err);
    }
  };

  const redirectToGroup = (selected: { value: string; label: string }) => {
    if (selected) {
      setSelectedGroup(selected.value);
      const envId = groups.find(
        (group: any) => group.id === selected.value
      )?.env_id;
      setEnvironment(envId);
      router.push(`/project/${projectId}/group/${selected.value}`);
    }
  };

  const fetchData = async () => {
    await fetchTestSuites();
    await fetchE2ETestSuites();
    await getGropupByProjectId();
  };

  useEffect(() => {
    
    if(!e2eData && !staticData) return;
    if (e2eData?.length > 0) {
      router.push(
        `/project/${projectId}/group/${groupId}/testSuit/e2e/${e2eData[0]?.id}`
      );
    } else if (
      !testSuitId &&
      !pathname?.includes("playground") &&
      !pathname?.includes("e2e") &&
      staticData?.length > 0
    ) {
      console.log("log:: static data is ",staticData)
      router.push(
        `/project/${projectId}/group/${groupId}/testSuit/${staticData[0]?.id}`
      );
    }
  }, [e2eData, staticData]);

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    fetchTestSuites();
    fetchE2ETestSuites();
  }, [revalidateTestsExplorer]);

  useEffect(() => {
    setSelectedGroup({
      value: groupId,
      label: groupList.find((item: any) => item.value == groupId)?.label,
    });
  }, [groupList]);

  const reversedData = [...data];

  return (
    <div className={`overflow-y-auto h-[100vh] bg-[#0D0D22]`}>
      {/* Sticky Header */}
      <div
        className="sticky top-0 z-10 bg-[#0D0D22] border-drcodeBlue"
        style={{ boxShadow: "7px 0px 24px 0px #00000073" }}
      >
        <div className="flex items-center px-3 py-2">
          <Link
            href={`/project/${projectId}`}
            className="flex items-center gap-2 cursor-pointer max-w-[fit-content]"
          >
            <Image
              src={"/arrow-left.png"}
              quality={100}
              alt="back"
              width={20}
              height={20}
            />
            <p className="text-[14px] font-normal text-[#B6B6BF]">
              Back to Dashboard
            </p>
          </Link>
          <button className="ml-auto" onClick={handleCollapse}>
            <RxCross2 size={20} />
          </button>
        </div>

        <div className="px-4">
          <Select
            className="my-4"
            onChange={redirectToGroup}
            options={groupList}
            value={selectedGroup}
            placeholder="Default Folder"
            classNamePrefix="react-select"
            instanceId="method-select"
            styles={{
              control: (provided) => ({
                ...provided,
                fontSize: "14px",
                color: "#777781",
              }),
              singleValue: (provided) => ({
                ...provided,
                fontSize: "14px",
                color: "#777781",
              }),
              option: (provided, state) => ({
                ...provided,
                fontSize: "14px",
                color: state.isSelected ? "white" : "#777781",
                backgroundColor: state.isSelected ? "#777781" : "white",
              }),
              placeholder: (provided) => ({
                ...provided,
                fontSize: "14px",
                color: "#777781",
              }),
            }}
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="flex items-center gap-2 px-3">
        <div className="min-w-[90%]">
          <SearchInput value={search} onChangeHandler={onInputChange} />
        </div>
        <Link
          href={`/project/${projectId}/group/${groupId}/testSuit/playground`}
          className="min-w-[16px] w-[40px] cursor-pointer"
        >
          <Image
            src={"/plus.png"}
            quality={100}
            alt="add"
            width={25}
            height={25}
          />
        </Link>
      </div>

      <TestSuitList data={reversedData} e2eData={e2eData} />
    </div>
  );
};
