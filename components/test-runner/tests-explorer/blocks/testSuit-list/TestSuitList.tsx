import React from "react";
import TestSuitItem, { TestSuitItemProps } from "../testSuit-item/TestSuitItem";
import styles from "./TestSuitList.module.scss";
import E2ESuiteItem, {
  E2ETestSuitItemProps,
} from "../testSuit-item/E2ESuiteItem";
const TestSuitList = (props: {
  data: TestSuitItemProps[];
  e2eData: E2ETestSuitItemProps[];
}) => {
  return (
    <div className={`pb-20 mt-4 px-3 ${styles["test-suit-list"]}`}>
      {props.e2eData &&
        props.e2eData.map((ts) => <E2ESuiteItem key={ts.id} {...ts} />)}
      {props.data.map((ts) => (
        <TestSuitItem key={ts.id} {...ts} />
      ))}
    </div>
  );
};

export default TestSuitList;
