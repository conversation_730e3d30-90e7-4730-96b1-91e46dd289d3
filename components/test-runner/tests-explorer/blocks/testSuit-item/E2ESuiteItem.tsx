import useToast from "@/hooks/useToast";
import { useGlobalStore } from "@/stores/globalstore";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import axios from "axios";
import Link from "next/link";
import { useParams } from "next/navigation";
import React from "react";
import { Popover, PopoverTrigger, PopoverContent } from "@nextui-org/react";

import styles from "./TestSuitItem.module.scss";
import Image from "next/image";

export interface E2ETestSuitItemProps {
  method: "E2E";
  description: string;
  id: string;
  name: string;
  status?: string;
}

const E2ESuiteItem = ({
  description = "",
  id = "",
  method = "E2E",
  name = "",
}: E2ETestSuitItemProps) => {
  const { showToast } = useToast();
  const { groupId, projectId, testSuitId } = useParams();
  const { triggerRevalidateTestsExplorer } = useGlobalStore();

  const deleteTestSuit = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevents event from reaching the Link
    try {
      await axios.delete(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testSuites/${id}`
      );
      triggerRevalidateTestsExplorer();
      // toast.error('Test Deleted');
      showToast("Test Deleted", "error");
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <div className={`relative`}>
      {/* Wrap only the necessary part inside Link */}
      <Link
        className={`${styles["test-suit-item"]}  ${
          testSuitId === id.toString() ? "bg-[#1B1B41]" : ""
        }`}
        href={`/project/${projectId}/group/${groupId}/testSuit/e2e/${id}`}
      >
        <p className={`${styles["test-suit-title"]}`}>{name}</p>
        <div className={`${styles["test-suit-url"]}`}>
          <span
            className={`${styles["method"]} ${styles[`method--${method}`]}`}
          >
            {method}
          </span>{" "}
          {description}
        </div>
      </Link>

      {/* Popover should not trigger navigation */}
      <div className="absolute top-0 right-0">
        <Popover placement="bottom">
          <PopoverTrigger>
            <button
              className="p-2"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation(); // Stops the Link click event
              }}
            >
              <Image
                src={"/menu-dots.png"}
                alt="menu"
                width={20}
                height={20}
                quality={100}
              />
            </button>
          </PopoverTrigger>
          <PopoverContent className="p-0">
            <div
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
              className="px-2 py-2 background-primary primary-border rounded-[10px]"
            >
              <button
                onClick={deleteTestSuit}
                className="flex text-[#D1D1E3] hover:bg-[#131330] text-[14px] px-4 py-2 w-full rounded-md gap-2"
              >
                <Image
                  src={"/delete-icon.png"}
                  alt="delete"
                  width={20}
                  height={20}
                />{" "}
                Delete
              </button>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
};

export default E2ESuiteItem;
