// 'use client'
// import Link from 'next/link'
// import React from 'react'
// import styles from './TestSuitItem.module.scss'
// import { Popover, PopoverTrigger, PopoverContent } from "@nextui-org/react";
// import Image from 'next/image';
// import { useParams } from 'next/navigation'
// import axios from 'axios';
// import { useGlobalStore } from '@/stores/globalstore';
// import { toast } from 'react-toastify';
// import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from '@/temp-utils/Constants/globalVariables';

// export interface TestSuitItemProps {
//     id: number;
//     test_suite_name: string;
//     method: 'POST' | 'GET' | 'PUT' | 'PATCH' | 'DELETE';
//     url: string;
// }
// const TestSuitItem = ({ id, test_suite_name, method, url }: TestSuitItemProps) => {

//     const { groupId, projectId, testSuitId } = useParams();

//     const { triggerRevalidateTestsExplorer } = useGlobalStore();

//     const deleteTestSuit = async () => {
//         try {
//             await axios.delete(`${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testSuites/${id}`);
//             triggerRevalidateTestsExplorer();
//             toast.error('Test Deleted')
//         } catch (err) {
//             console.log(err)

//         }
//     }

//     return (
//         <>
//             <Link className={`${styles['test-suit-item']} relative ${testSuitId === id.toString() ? 'bg-[#1B1B41]' : ''}`} href={`/project/${projectId}/group/${groupId}/testSuit/${id}`}>
//                 <p className={`${styles['test-suit-title']}`}>{test_suite_name}</p>
//                 <div className={`${styles['test-suit-url']}`}> <span className={`${styles['method']} ${styles[`method--${method}`]}`}>{method}</span> {url}</div>
//                 <div className='absolute top-0  right-0'>
//                     <Popover placement="bottom">
//                         <PopoverTrigger>
//                             <button className='p-2' onClick={(e) => {
//                                 e.preventDefault();
//                                 e.stopPropagation();
//                             }}>
//                                 <Image src={'/menu-dots.png'} alt='menu' width={20} height={20} quality={100} />
//                             </button>
//                         </PopoverTrigger>
//                         <PopoverContent className='p-0'>
//                             <div onClick={(e) => {
//                                 e.preventDefault();
//                                 e.stopPropagation();
//                             }} className="px-2 py-2 background-primary primary-border rounded-[10px]" >
//                                 {/* <button className='flex text-[#D1D1E3] hover:bg-[#131330] px-4 py-2 w-full rounded-md gap-2'><Image src={'/edit-icon.png'} alt='edit' width={20} height={20} /> Edit</button> */}
//                                 {/* <button className='flex text-[#D1D1E3] hover:bg-[#131330] px-4 py-2 w-full rounded-md gap-2'><Image src={'/remove-icon.png'} alt='edit' width={20} height={20} /> Add to new group</button> */}
//                                 <button onClick={deleteTestSuit} className='flex text-[#D1D1E3] hover:bg-[#131330] text-[14px] px-4 py-2 w-full rounded-md gap-2'><Image src={'/delete-icon.png'} alt='edit' width={20} height={20} /> Delete</button>
//                             </div>
//                         </PopoverContent>
//                     </Popover>
//                 </div>
//             </Link>

//                 {/* <div className="w-full h-[0.1px] bg-drcodePurple "></div> */}
//         </>
//     )
// }

// export default TestSuitItem

"use client";
import Link from "next/link";
import React from "react";
import styles from "./TestSuitItem.module.scss";
import { Popover, PopoverTrigger, PopoverContent } from "@nextui-org/react";
import Image from "next/image";
import { useParams } from "next/navigation";
import axios from "axios";
import { useGlobalStore } from "@/stores/globalstore";
import { toast } from "react-toastify";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import useToast from "@/hooks/useToast";

export interface TestSuitItemProps {
  id: number;
  test_suite_name: string;
  method: "POST" | "GET" | "PUT" | "PATCH" | "DELETE";
  url: string;
}


const TestSuitItem = ({
  id,
  test_suite_name,
  method,
  url,
}: TestSuitItemProps) => {
  const { showToast } = useToast();
  const { groupId, projectId, testSuitId } = useParams();
  const { triggerRevalidateTestsExplorer } = useGlobalStore();

  const deleteTestSuit = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevents event from reaching the Link
    try {
      await axios.delete(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testSuites/${id}`
      );
      triggerRevalidateTestsExplorer();
      // toast.error('Test Deleted');
      showToast("Test Deleted", "error");
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <div className={`relative`}>
      {/* Wrap only the necessary part inside Link */}
      <Link
        className={`${styles["test-suit-item"]}  ${
          testSuitId === id.toString() ? "bg-[#1B1B41]" : ""
        }`}
        href={`/project/${projectId}/group/${groupId}/testSuit/${id}`}
      >
        <p className={`${styles["test-suit-title"]}`}>{test_suite_name}</p>
        <div className={`${styles["test-suit-url"]}`}>
          <span
            className={`${styles["method"]} ${styles[`method--${method}`]}`}
          >
            {method}
          </span>{" "}
          {url}
        </div>
      </Link>

      {/* Popover should not trigger navigation */}
      <div className="absolute top-0 right-0">
        <Popover placement="bottom">
          <PopoverTrigger>
            <button
              className="p-2"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation(); // Stops the Link click event
              }}
            >
              <Image
                src={"/menu-dots.png"}
                alt="menu"
                width={20}
                height={20}
                quality={100}
              />
            </button>
          </PopoverTrigger>
          <PopoverContent className="p-0">
            <div
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
              className="px-2 py-2 background-primary primary-border rounded-[10px]"
            >
              <button
                onClick={deleteTestSuit}
                className="flex text-[#D1D1E3] hover:bg-[#131330] text-[14px] px-4 py-2 w-full rounded-md gap-2"
              >
                <Image
                  src={"/delete-icon.png"}
                  alt="delete"
                  width={20}
                  height={20}
                />{" "}
                Delete
              </button>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
};

export default TestSuitItem;
