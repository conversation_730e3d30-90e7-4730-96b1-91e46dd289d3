import React, { useEffect } from "react";
import { TestCase } from "../tests-table/TestsTable";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import axios from "axios";
import { toast } from "react-toastify";
import { ButtonPrimary } from "@/components/common/button-primary/ButtonPrimary";
import { ButtonOutlinedPrimary } from "@/components/common/button-outlined-primary/ButtonOutlinedPrimary";
import PrimaryInput from "@/components/common/primary-input/PrimaryInput";
import useToast from "@/hooks/useToast";

interface TagEditorProps {
  testCase: TestCase;
  onTagsUpdated?: (tags: string[]) => void;
  handleClose: () => void;
}

export interface Tag {
  name: string;
  isPositive: boolean;
}

const TagEditor = ({
  testCase,
  onTagsUpdated,
  handleClose,
}: TagEditorProps) => {

  const { showToast } = useToast()

  const [tags, setTags] = React.useState<Tag[]>([]);
  const [newTag, setNewTag] = React.useState("");

  const removeTag = (index: number) => {
    setTags((prev) => prev.filter((_, i) => i !== index));
  };

  const addTag = () => {
    if (!newTag) return;
    setTags((prev) => [
      ...prev,
      { name: newTag, isPositive: newTag.toLowerCase() !== "negative" },
    ]);
    setNewTag("");
  };

  const polarityIndex = tags.findIndex((tag) => {
    const lower = tag.name.toLowerCase();
    return lower === "positive" || lower === "negative";
  });

  const togglePolarity = () => {
    if (polarityIndex === -1) return;
    const current = tags[polarityIndex];
    const toggledName =
      current.name.toLowerCase() === "positive" ? "negative" : "positive";
    const updated = [...tags];
    updated[polarityIndex] = {
      name: toggledName,
      isPositive: toggledName === "positive",
    };
    setTags(updated);
  };

  const handleSave = async () => {
    // Convert the internal tag objects back to simple strings.
    const updatedTags = tags.map((tag) => tag.name);
    try {
      // Use your httpService to update the test case.
      const updateUrl = `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testCases/${testCase.id}`;

      const response = await axios.put(updateUrl, { tags: updatedTags });
      if (response.status === 200) {
        console.log("Tags updated successfully.");
        if (onTagsUpdated) {
          onTagsUpdated(updatedTags);
        }
        // toast.success("Tags updated successfully.");
        showToast("Tags updated successfully.", "success")
        handleClose();
      }
    } catch (error) {
      console.error("Error updating tags:", error);
      // alert("Error updating tags.")
    }
  };

  useEffect(() => {
    setTags(
      testCase.tags.map((tag) => {
        const lower = tag.toLowerCase();
        if (lower === "positive" || lower === "negative") {
          return { name: lower, isPositive: lower === "positive" };
        }
        return { name: tag, isPositive: true };
      })
    );
  }, [testCase]);
  return (
    <div>
      <div className="flex flex-wrap gap-2 mb-4">
        {tags.map((tag, index) => (
          <div
            key={index}
            className={`flex justify-center items-center flex-row bg-[#2E2E60] gap-[5px]
              h-[28px] min-w-[95px] rounded-[12px] border-[1px] border-[#2E2E60] text-[12px] text-[#D1D1E3] py-[4px] px-[8px]
              
              `}
          >
            <span>{tag.name.toLocaleLowerCase()}</span>
            <button
              onClick={() => removeTag(index)}
              className="text-xs focus:outline-none"
            >
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4.26665 12.6668L3.33331 11.7335L7.06665 8.00016L3.33331 4.26683L4.26665 3.3335L7.99998 7.06683L11.7333 3.3335L12.6666 4.26683L8.93331 8.00016L12.6666 11.7335L11.7333 12.6668L7.99998 8.9335L4.26665 12.6668Z" fill="#D1D1E3" />
              </svg>

            </button>
          </div>
        ))}
      </div>
      <div className="h-0.5 w-full bg-drcodePurple/40"></div>
      <div className="mt-4 flex gap-2">
        {/* <input
          type="text"
          value={newTag}
          onChange={(e) => setNewTag(e.target.value)}
          onKeyPress={(e) => {
            if (e.key === "Enter") {
              e.preventDefault();
              addTag();
            }
          }}
          placeholder="Add new tag"
          className="border rounded-md px-1 py-1 bg-transparent text-sm"
        /> */}
        <PrimaryInput
          value={newTag}
          onChange={(e) => setNewTag(e.target.value)}
          placeholder="Add new tag"
          className="max-w-[200px]"
        />
        {/* <button
          onClick={addTag}
          className="px-2 py-1 border border-drcodePurple  text-sm rounded focus:outline-none"
        >
          Add
        </button> */}
        <button
          onClick={addTag}
          className="button--outlined-primary p-0 px-2 "
          style={{ fontSize: "12px" }}
        >
          Add
        </button>
      </div>

      {polarityIndex !== -1 && (
        <div className="mt-4 flex items-center gap-2">
          <span className="text-sm">Polarity:</span>
          <button
            onClick={togglePolarity}
            className="px-3 py-1 bg-vscode-sideBar-background border border-drcodePurple/50 rounded text-sm"
          >
            {tags[polarityIndex].name === "positive"
              ? "Switch to Negative"
              : "Switch to Positive"}
          </button>
        </div>
      )}

      <div className="mt-0 flex justify-end gap-2">
        {/* <ButtonOutlinedPrimary
          onClick={handleClose}
          text="Cancel"
          size="small"
        />

        <ButtonPrimary onClick={handleSave} text="Save" size="small" /> */}

        <button
          onClick={handleSave}
          className="button--primary mt-4 py-2 px-8 text-[14px]"
          style={{ fontSize: "14px" }}
        >
          Save
        </button>
        <button
          type="button"
          onClick={handleClose} className="button--outlined-primary mt-4 py-2 px-8 text-[14px]"
          style={{ fontSize: "14px" }}
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default TagEditor;
