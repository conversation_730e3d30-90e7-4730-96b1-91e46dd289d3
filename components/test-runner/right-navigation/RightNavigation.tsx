import React, { JSX } from "react";
import Image from "next/image";

export interface NavigationItem {
  title: string;
  icon: string;
  disabled?: boolean;
  onClick: () => void;
  tooltip?: JSX.Element;
  tooltipWidth?: string;
}

interface RightNavigationProps {
  navigationItems: NavigationItem[];
}

const RightNavigation = ({ navigationItems }: RightNavigationProps) => {
  return (
    <div className="flex flex-col gap-8 p-2 h-full w-full border-l-2 border-l-drCodeDarkBlue">
      {navigationItems.map((item, index) => (
        <div
          key={index}
          className="flex flex-col items-center justify-center cursor-pointer relative"
          onClick={item.onClick}
        >
          <Image
            src={`/${item.icon}`}
            alt={item.title}
            width={24}
            height={24}
          />
          <div className="text-[10px] font-medium text-center text-[##B2B2C1]">
            {item.title}
          </div>

          {item.tooltip && (
            <div
              className={`absolute`}
              style={{
                left: `-${item.tooltipWidth}`,
              }}
            >
              {item.tooltip}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default RightNavigation;
