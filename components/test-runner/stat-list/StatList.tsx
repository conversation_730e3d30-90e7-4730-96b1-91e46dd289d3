import React from "react";
import { StatCard } from "../stat-card/StatCard";

const StatList = (props: {
  allTestCount: number;
  executedCount: number;
  passedCount: number;
  failedCount: number;
  onFilter?: (type: string, value: string) => void;
  selectedFilter: string;
  isProjectView?: boolean;
}) => {
  return (
    <ul className="stat-list flex gap-4 justify-between">
      <li
        onClick={() => props.onFilter?.("", "")}
        className={`flex-[1_0_20%] text-[13px] ${
          props?.isProjectView ? "cursor-auto" : "cursor-pointer"
        }`}
      >
        <StatCard
          isAllTestCount={true}
          title="All Tests"
          image="/codetest.png"
          count={props.allTestCount}
          className={
            props.selectedFilter === "" && !props?.isProjectView
              ? "border-[1px] border-drcodePurple"
              : "border-[1px] border-[#1B1B41]"
          }
          isProjectView={props.isProjectView}
        />
      </li>

      <li
        onClick={() => props.onFilter?.("Result", "Executed")}
        className={`flex-[1_0_20%] text-[13px] ${
          props?.isProjectView ? "cursor-auto" : "cursor-pointer"
        }`}
      >
        <StatCard
          title="Executed"
          image="/revote.png"
          count={props.executedCount}
          className={
            props.selectedFilter === "Executed"
              ? "border-[1px] border-drcodePurple"
              : "border-[1px] border-[#1B1B41]"
          }
        />
      </li>

      <li
        onClick={() => props.onFilter?.("Result", "Passed")}
        className={`flex-[1_0_20%] text-[13px] ${
          props?.isProjectView ? "cursor-auto" : "cursor-pointer"
        }`}
      >
        <StatCard
          title="Passed"
          image="/hourglass.png"
          count={props.passedCount}
          className={
            props.selectedFilter === "Passed"
              ? "border-[1px] border-drcodePurple"
              : "border-[1px] border-[#1B1B41]"
          }
        />
      </li>

      <li
        onClick={() => props.onFilter?.("Result", "Failed")}
        className={`flex-[1_0_20%] text-[13px] ${
          props?.isProjectView ? "cursor-auto" : "cursor-pointer"
        }`}
      >
        <StatCard
          title="Failed"
          image="/danger.png"
          count={props.failedCount}
          className={
            props.selectedFilter === "Failed"
              ? "border-[1px] border-drcodePurple"
              : "border-[1px] border-[#1B1B41]"
          }
        />
      </li>
    </ul>
  );
};

export default StatList;
