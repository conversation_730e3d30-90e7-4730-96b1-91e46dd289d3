.assertion-results-container {
    padding: 0 2rem;
    max-height: 200px;
    overflow-y: auto;
    background-color: #0D0D22;
    .assert-list {
        border-collapse: separate;
        border-spacing: 0 10px;
        .assert-row {
            margin: 1rem;
            .assert-case{
                padding-left: 1rem;
            }
        }
    }

    .assert-status {
        text-align: center;
        padding: 0.35rem;
        color: #fff;
        border-radius: 8px;
        &--failed {
            background-color: #8C1823;
        }

        &--passed {
            background-color: #0B7B69;
        }
    }
}