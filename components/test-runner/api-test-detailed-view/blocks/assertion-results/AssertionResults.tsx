import React from "react";
import styles from "./AssertionResults.module.scss";
export interface TestAssertionEntity {
  passed: boolean;
  message: string;
}
export const AssertionResults = (props: { list: TestAssertionEntity[] }) => {
  let assertionResults;
  if (typeof props.list === "string") {
    assertionResults = JSON.parse(props.list);
  } else {
    assertionResults = props.list;
  }

  // If assertionResults is a string, attempt to parse it.
  if (typeof assertionResults === "string") {
    try {
      assertionResults = JSON.parse(assertionResults);
    } catch (error) {
      console.error("Failed to parse assertionResults string:", error);
      assertionResults = [];
    }
  }

  // Ensure assertionResults is an array; if not, wrap it in one.
  if (!Array.isArray(assertionResults)) {
    assertionResults = [assertionResults];
  }

  // Parse each item if needed and filter out invalid (null) items.
  const parsedResults = assertionResults
    .map((item: any) => {
      if (typeof item === "string") {
        try {
          return JSON.parse(item);
        } catch (error) {
          console.error("Failed to parse item:", item, error);
          return null;
        }
      }
      return item;
    })
    .filter((item: any) => item !== null);

  const filteredResults = parsedResults.filter(
    (result: any) => result?.message !== "Processing..."
  );

  if (filteredResults?.length === 0) {
    return (
      <p className="text-sm/6 font-semibold text-[#B2B2C1]">
        Run the testcase to see the Assertions
      </p>
    );
  }


  return (
    <div className={`${styles["assertion-results-container"]}`}>
      <table className={`${styles["assert-list"]}`}>
        <tbody>
          {filteredResults?.map((assr) => {
            return (
              <tr className={`${styles["assert-row"]}`} key={assr.message}>
                <td
                  className={`${styles["assert-status"]} ${
                    // !assr.success || !assr.passed
                    //   ? styles["assert-status--failed"]
                    //   : styles["assert-status--passed"]
                    assr.success || assr.passed
                      ? styles["assert-status--passed"]
                      : styles["assert-status--failed"]
                  }`}
                >
                  {assr.success || assr.passed ? "Success" : "Failed"}
                </td>
                <td className={`${styles["assert-case"]}`}>
                  {assr.assertion ?? assr.message} &nbsp;
                  {assr.error?.toString()
                    ? "(" + assr.error?.toString() + ")"
                    : ""}
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};
