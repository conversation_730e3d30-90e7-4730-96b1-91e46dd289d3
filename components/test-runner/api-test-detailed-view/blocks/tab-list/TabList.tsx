import React from 'react'
import styles from './TabList.module.scss'
const TabList = (props: {
	tabs: {
		id: number;
		name: string;
	}[],
	activeIndex: number,
	onTabClickHandler: (...args: any[]) => any,
	tabSecondary: boolean
}) => {

	const onTabClick = (tab: { id: number, name: string }, index: number) => {
		props.onTabClickHandler(tab, index);
	}

	return (
		<div className={`${styles['api-view-tab-list-container']}`}>
			{!props.tabSecondary && <nav className={`${styles['tab-list']}`}>
				{props.tabs.map(((tab, index) => (
					<button className={`${styles['tab']} ${index === props.activeIndex ? styles['active'] : ''}`} onClick={(e) => { e.stopPropagation(); onTabClick(tab, index) as any }} key={tab.id}>{tab.name}</button>
				)))}
			</nav>}
			{props.tabSecondary && <nav className={`${styles['tab-list-secondary']}`}>
				{props.tabSecondary && props.tabs.map(((tab, index) => (
					<button className={`${styles['secondary-tab']} ${index === props.activeIndex ? styles['active'] : ''}`} onClick={(e) => { e.stopPropagation(); onTabClick(tab, index) as any }} key={tab.id}>{tab.name}</button>
				)))}
			</nav>}
		</div>
	)
}

export default TabList