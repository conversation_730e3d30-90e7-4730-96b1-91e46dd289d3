import React, { memo, useMemo, useState } from "react";
import styles from "./ApiTestDetailedView.module.scss";
import TabList from "./blocks/tab-list/TabList";
import { TestCase } from "../tests-table/TestsTable";
import ApiRequestResponseOutputView from "@/components/common/api-request-response-output-view/ApiRequestResponseOutputView";
import { AssertionResults } from "./blocks/assertion-results/AssertionResults";
import Image from "next/image";
import { getAssertions } from "@/temp-utils/assertionsUtilities";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import axios from "axios";
import { ButtonOutlinedPrimary } from "@/components/common/button-outlined-primary/ButtonOutlinedPrimary";
import useToast from "@/hooks/useToast";

const ApiTestDetailedView = ({
  apiTestDetails,
  idx,
  running,
  modifyRequestBodyRow,
  modifyAssertionRow,
  modifyResponseBodyRow,
  handleSaveRequest,
  generateAssertions,
  runAssertionsLocally,
  defaultTab = 0,
  isE2E = false,
  flowId,
}: {
  idx: number;
  apiTestDetails: TestCase;
  running: boolean;
  modifyRequestBodyRow: (
    rowIndex: number,
    body: string,
    testCase: TestCase,
    flowId?: string
  ) => void;
  modifyAssertionRow: (
    rowIndex: number,
    body: string,
    row?: TestCase,
    flowId?: string
  ) => void;
  modifyResponseBodyRow: (
    rowIndex: number,
    body: string,
    row?: TestCase,
    flowId?: string
  ) => void;
  generateAssertions: (
    rowIndex: number,
    responseBody: any,
    testCase: TestCase,
    flowId?: string
  ) => void;
  runAssertionsLocally: (
    rowIndex: number,
    response: any,
    row?: TestCase,
    flowId?: string
  ) => void;
  handleSaveRequest: (testCase: TestCase) => void;
  defaultTab?: number;
  isE2E?: boolean;
  flowId?: string;
}) => {
  const { showToast } = useToast();
  const [activeTab, setActiveTab] = useState(defaultTab);
  const [prompt, setPrompt] = useState<string>("");
  const [promptAssertionsLoading, setPromptAssertionsLoading] = useState(false);
  const [isChanged, setIsChanged] = useState(false);

  // console.log("apiTestDetails", apiTestDetails);

  const tabs = useMemo(
    () => [
      {
        id: 1,
        name: "Request",
      },
      {
        id: 2,
        name: "Response",
      },
      {
        id: 3,
        name: "Assertion",
      },
      {
        id: 4,
        name: "Assertion Results",
      },
    ],
    []
  );

  const saveRequest = () => {
    handleSaveRequest(apiTestDetails);
  };

  const generateAssertionsWithPrompt = async () => {
    setPromptAssertionsLoading(true);
    try {
      const backendUrl = `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/generate-assertions/prompt`;
      const response = await axios.post(backendUrl, {
        minElapsedTime: 4096,
        test_name: apiTestDetails.description,
        tags: apiTestDetails.tags,
        request: {
          url: apiTestDetails.url,
          method: apiTestDetails.method,
          headers: JSON.parse(apiTestDetails.request)?.headers || {},
          body: JSON.parse(apiTestDetails.request)?.json_body || {},
        },
        response: apiTestDetails.response,
        previousAssertions:
          apiTestDetails?.assertions?.assertions?.assertions ||
          apiTestDetails?.assertions?.assertions ||
          apiTestDetails?.assertions ||
          "",
        userPrompt: prompt,
      });

      if (response.data?.combinedAssertions) {
        modifyAssertionRow(
          idx,
          response.data?.combinedAssertions,
          apiTestDetails
        );
      }
      setIsClicked(false);
    } catch (error) {
    } finally {
      setPromptAssertionsLoading(false);
    }
  };

  const handleResetAssertions = async () => {
    const testSuite = apiTestDetails;
    try {
      // Reset assertions but keep other data
      const updatedTestSuite = {
        ...testSuite,
        assertions: undefined,
        assertionResults: undefined,
        assertions_results: [],
      };

      const updateUrl = isE2E
        ? `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/e2e/step/${testSuite.id}`
        : `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testCases/${testSuite.id}`;
      await axios.put(updateUrl, {
        assertions: null,
        assertions_results: [],
      });

      // Update state
      modifyAssertionRow(idx, null, updatedTestSuite);
      showToast("Assertions reset successfully", "success");
    } catch (error) {
      console.error("Error resetting assertions:", error);
      // alert("Failed to reset assertions.")
      showToast("Failed to reset assertions.", "error");
    }
  };

  const [isClicked, setIsClicked] = useState(false);

  const onTabClickHandler = (
    tab: { id: number; name: string },
    index: number
  ) => {
    setActiveTab(index);
  };

  const assertions = getAssertions(apiTestDetails);

  function extractAssertions(obj) {
    if (typeof obj === "string") {
      return obj;
    }

    if (typeof obj === "object" && obj !== null) {
      for (const key in obj) {
        const result = extractAssertions(obj[key]);
        if (result) return result; // Return the first found string
      }
    }

    return ""; // If no string found
  }

  return (
    <div className={`${styles["api-test-detailed-view"]}`}>
      {!isE2E && (
        <p className="text-xs">
          Method: {apiTestDetails.method} - Endpoint: {apiTestDetails.url}
        </p>
      )}
      <TabList
        tabSecondary={false}
        activeIndex={activeTab}
        onTabClickHandler={onTabClickHandler}
        tabs={tabs}
      />
      <div className="tab-view mt-4 relative">
        {activeTab === 0 && (
          <>
            <ApiRequestResponseOutputView
              onChange={(e) => {
                modifyRequestBodyRow(idx, e || "", apiTestDetails, flowId);
              }}
              readonly={false}
              value={apiTestDetails.request}
              responseType="json"
            />
            <div className="flex justify-end mt-2">
              <button
                className="text-white absolute bottom-5 right-5 bg-drcodePurple rounded-md py-1.5 px-2 hover:bg-drcodePurple/50"
                onClick={saveRequest}
              >
                Save Request
              </button>
            </div>
          </>
        )}
        {activeTab === 1 && (
          <ApiRequestResponseOutputView
            onChange={(e) => {
              modifyResponseBodyRow(idx, e || "");
            }}
            readonly={false}
            value={JSON.stringify(apiTestDetails.response, null, 2)}
            responseType="json"
          />
        )}

        {activeTab === 2 && (
          <>
            {apiTestDetails.assertions ? (
              <>
                {isClicked && (
                  <div className="absolute top-10 bg-drcodeBlue border-white border-1 p-2 rounded-md min-w-56 z-10">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 36 36"
                      fill=""
                      xmlns="http://www.w3.org/2000/svg"
                      className="bg-drcodeBlue rounded-full p-1 absolute top-[-15px] left-[-15px]"
                    >
                      <path
                        d="M18.6694 8.94469C18.6333 9.09036 18.4882 9.17857 18.3454 9.14172C18.2026 9.10487 18.1161 8.95691 18.1523 8.81124C18.1884 8.66558 18.3334 8.57736 18.4762 8.61421C18.6191 8.65106 18.7055 8.79902 18.6694 8.94469Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M18.0592 10.2992C18.2021 10.336 18.3471 10.2478 18.3832 10.1021C18.4194 9.95647 18.3329 9.80851 18.1901 9.77166C18.0473 9.73481 17.9022 9.82303 17.8661 9.96869C17.83 10.1144 17.9164 10.2623 18.0592 10.2992Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M18.0957 11.2609C18.0595 11.4066 17.9145 11.4948 17.7717 11.4579C17.6289 11.4211 17.5424 11.2731 17.5785 11.1274C17.6146 10.9818 17.7597 10.8936 17.9025 10.9304C18.0453 10.9673 18.1318 11.1152 18.0957 11.2609Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M19.429 9.4214C19.5718 9.45825 19.7168 9.37004 19.7529 9.22437C19.7891 9.0787 19.7026 8.93074 19.5598 8.89389C19.417 8.85704 19.2719 8.94526 19.2358 9.09093C19.1997 9.23659 19.2861 9.38455 19.429 9.4214Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M19.4647 10.3831C19.4286 10.5288 19.2836 10.617 19.1407 10.5801C18.9979 10.5433 18.9114 10.3953 18.9476 10.2497C18.9837 10.104 19.1288 10.0158 19.2716 10.0526C19.4144 10.0895 19.5009 10.2374 19.4647 10.3831Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M18.8553 11.7376C18.9981 11.7744 19.1432 11.6862 19.1793 11.5406C19.2155 11.3949 19.129 11.2469 18.9862 11.2101C18.8433 11.1732 18.6983 11.2615 18.6622 11.4071C18.626 11.5528 18.7125 11.7007 18.8553 11.7376Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M20.8358 9.50405C20.7997 9.64972 20.6547 9.73793 20.5118 9.70108C20.369 9.66423 20.2826 9.51627 20.3187 9.3706C20.3548 9.22494 20.4999 9.13672 20.6427 9.17357C20.7855 9.21042 20.872 9.35838 20.8358 9.50405Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M20.2236 10.8585C20.3664 10.8954 20.5115 10.8072 20.5476 10.6615C20.5838 10.5158 20.4973 10.3679 20.3545 10.331C20.2117 10.2942 20.0666 10.3824 20.0305 10.5281C19.9943 10.6737 20.0808 10.8217 20.2236 10.8585Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M20.2601 11.8202C20.2239 11.9659 20.0789 12.0541 19.9361 12.0173C19.7932 11.9804 19.7068 11.8325 19.7429 11.6868C19.779 11.5411 19.9241 11.4529 20.0669 11.4898C20.2097 11.5266 20.2962 11.6746 20.2601 11.8202Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M17.5535 13.2993C17.3048 13.2302 17.0483 13.3798 16.9805 13.6335L16.4671 15.556C16.3994 15.8097 16.5461 16.0714 16.7948 16.1405L18.3726 16.5789C18.6214 16.648 18.8779 16.4983 18.9457 16.2446L19.459 14.3221C19.5268 14.0684 19.3801 13.8067 19.1313 13.7376L17.5535 13.2993Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M16.1595 18.5958C16.1233 18.7414 15.9783 18.8297 15.8355 18.7928C15.6927 18.756 15.6062 18.608 15.6423 18.4623C15.6784 18.3167 15.8235 18.2284 15.9663 18.2653C16.1091 18.3021 16.1956 18.4501 16.1595 18.5958Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M15.5479 19.9518C15.6907 19.9887 15.8358 19.9004 15.8719 19.7548C15.908 19.6091 15.8215 19.4612 15.6787 19.4243C15.5359 19.3875 15.3909 19.4757 15.3547 19.6213C15.3186 19.767 15.4051 19.915 15.5479 19.9518Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M15.5857 20.9135C15.5496 21.0592 15.4045 21.1474 15.2617 21.1106C15.1189 21.0737 15.0324 20.9257 15.0686 20.7801C15.1047 20.6344 15.2497 20.5462 15.3925 20.583C15.5354 20.6199 15.6218 20.7679 15.5857 20.9135Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M16.9182 19.0725C17.061 19.1093 17.2061 19.0211 17.2422 18.8755C17.2784 18.7298 17.1919 18.5818 17.0491 18.545C16.9062 18.5081 16.7612 18.5963 16.7251 18.742C16.6889 18.8877 16.7754 19.0356 16.9182 19.0725Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M16.9554 20.0329C16.9193 20.1786 16.7742 20.2668 16.6314 20.2299C16.4886 20.1931 16.4021 20.0451 16.4383 19.8995C16.4744 19.7538 16.6194 19.6656 16.7623 19.7024C16.9051 19.7393 16.9915 19.8872 16.9554 20.0329Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M16.3432 21.3887C16.486 21.4255 16.6311 21.3373 16.6672 21.1916C16.7033 21.046 16.6169 20.898 16.474 20.8612C16.3312 20.8243 16.1862 20.9125 16.15 21.0582C16.1139 21.2039 16.2004 21.3518 16.3432 21.3887Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M18.3259 19.1538C18.2898 19.2995 18.1447 19.3877 18.0019 19.3509C17.8591 19.314 17.7726 19.1661 17.8087 19.0204C17.8449 18.8747 17.9899 18.7865 18.1327 18.8234C18.2755 18.8602 18.362 19.0082 18.3259 19.1538Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M17.7143 20.5096C17.8571 20.5465 18.0022 20.4582 18.0383 20.3126C18.0744 20.1669 17.988 20.019 17.8451 19.9821C17.7023 19.9453 17.5573 20.0335 17.5212 20.1791C17.485 20.3248 17.5715 20.4728 17.7143 20.5096Z"
                        fill="#875BF8"
                      />
                      <path
                        d="M17.7501 21.4713C17.714 21.617 17.5689 21.7052 17.4261 21.6684C17.2833 21.6315 17.1968 21.4835 17.2329 21.3379C17.2691 21.1922 17.4141 21.104 17.5569 21.1408C17.6997 21.1777 17.7862 21.3257 17.7501 21.4713Z"
                        fill="#875BF8"
                      />
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M19.8712 34.0547L20.8048 32.446C21.5288 31.1982 21.8909 30.5743 22.4724 30.2293C23.054 29.8843 23.7861 29.8714 25.2505 29.8457C27.4123 29.8077 28.7682 29.6726 29.9053 29.1922C32.0151 28.3008 33.6913 26.5911 34.5652 24.4391C35.2206 22.8251 35.2206 20.779 35.2206 16.6869V14.9304C35.2206 9.18057 35.2206 6.30562 33.9518 4.19369C33.2418 3.01195 32.2677 2.01837 31.1091 1.2942C29.0386 0 26.2201 0 20.583 0H15.4168C9.77979 0 6.96126 0 4.89074 1.2942C3.73217 2.01837 2.75808 3.01195 2.04811 4.19369C0.779297 6.30562 0.779297 9.18054 0.779297 14.9304V16.6869C0.779297 20.779 0.779297 22.8251 1.43472 24.4391C2.30861 26.5911 3.98482 28.3008 6.09459 29.1922C7.23168 29.6726 8.58751 29.8077 10.7493 29.8457C12.2137 29.8714 12.9458 29.8843 13.5274 30.2293C14.1089 30.5743 14.471 31.1982 15.1951 32.446L16.1286 34.0547C16.9606 35.4886 19.0392 35.4886 19.8712 34.0547ZM12.4933 21.5717C13.2487 20.8012 13.2487 19.5519 12.4933 18.7813L8.69413 14.9062L12.5387 11.0846C13.3038 10.3241 13.3196 9.07487 12.574 8.29443C11.8283 7.514 10.6036 7.49788 9.8385 8.25843L4.7457 13.3207C4.68277 13.3707 4.62216 13.4252 4.56431 13.4842C4.26582 13.7887 4.08527 14.1679 4.02266 14.5628C3.92086 15.1646 4.09356 15.8061 4.54341 16.2769C4.60953 16.3461 4.67942 16.4093 4.75242 16.4665L9.75753 21.5717C10.513 22.3423 11.7378 22.3423 12.4933 21.5717ZM23.4785 8.26618C22.7252 9.03453 22.7252 10.2803 23.4785 11.0486L27.2682 14.9142L23.4346 18.7249C22.6716 19.4833 22.6559 20.7289 23.3994 21.5071C24.1429 22.2853 25.3641 22.3014 26.127 21.543L31.2197 16.4809C31.2768 16.4346 31.3319 16.3845 31.3847 16.3306C31.6394 16.0708 31.8079 15.7565 31.8904 15.4246C32.0577 14.7782 31.8977 14.061 31.407 13.5474C31.3413 13.4787 31.2718 13.4158 31.1992 13.3589L26.2064 8.26618C25.4531 7.49782 24.2317 7.49782 23.4785 8.26618ZM17.429 8.54964C17.7374 7.37563 18.9205 6.67892 20.0715 6.99349C21.2225 7.30807 21.9055 8.5148 21.5971 9.68882L18.5401 21.3261C18.2317 22.5001 17.0486 23.1968 15.8976 22.8822C14.7466 22.5676 14.0635 21.3609 14.372 20.1869L17.429 8.54964Z"
                        fill="#875BF8"
                      />
                    </svg>
                    <input
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      className="border-white mb-2 rounded-md border-1 p-1 text-white w-full text-[10px]"
                      placeholder="Write prompt to generate assertions"
                    />
                    <div className="flex justify-end  space-x-2">
                      <button
                        className="border-none text-white"
                        onClick={generateAssertionsWithPrompt}
                        disabled={promptAssertionsLoading}
                      >
                        {promptAssertionsLoading ? "Loading..." : "Submit"}
                      </button>
                      <button
                        onClick={() => setIsClicked(false)}
                        className="border-none text-white"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}

                <div
                  onClick={() => setIsClicked((prev) => !prev)}
                  className="absolute top-20 right-0 p-2 z-10 select-none"
                >
                  <svg
                    width="32"
                    height="32"
                    viewBox="0 0 36 36"
                    fill=""
                    xmlns="http://www.w3.org/2000/svg"
                    className="bg-vscode-sideBar-background  p-1 "
                  >
                    <path
                      d="M18.6694 8.94469C18.6333 9.09036 18.4882 9.17857 18.3454 9.14172C18.2026 9.10487 18.1161 8.95691 18.1523 8.81124C18.1884 8.66558 18.3334 8.57736 18.4762 8.61421C18.6191 8.65106 18.7055 8.79902 18.6694 8.94469Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M18.0592 10.2992C18.2021 10.336 18.3471 10.2478 18.3832 10.1021C18.4194 9.95647 18.3329 9.80851 18.1901 9.77166C18.0473 9.73481 17.9022 9.82303 17.8661 9.96869C17.83 10.1144 17.9164 10.2623 18.0592 10.2992Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M18.0957 11.2609C18.0595 11.4066 17.9145 11.4948 17.7717 11.4579C17.6289 11.4211 17.5424 11.2731 17.5785 11.1274C17.6146 10.9818 17.7597 10.8936 17.9025 10.9304C18.0453 10.9673 18.1318 11.1152 18.0957 11.2609Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M19.429 9.4214C19.5718 9.45825 19.7168 9.37004 19.7529 9.22437C19.7891 9.0787 19.7026 8.93074 19.5598 8.89389C19.417 8.85704 19.2719 8.94526 19.2358 9.09093C19.1997 9.23659 19.2861 9.38455 19.429 9.4214Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M19.4647 10.3831C19.4286 10.5288 19.2836 10.617 19.1407 10.5801C18.9979 10.5433 18.9114 10.3953 18.9476 10.2497C18.9837 10.104 19.1288 10.0158 19.2716 10.0526C19.4144 10.0895 19.5009 10.2374 19.4647 10.3831Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M18.8553 11.7376C18.9981 11.7744 19.1432 11.6862 19.1793 11.5406C19.2155 11.3949 19.129 11.2469 18.9862 11.2101C18.8433 11.1732 18.6983 11.2615 18.6622 11.4071C18.626 11.5528 18.7125 11.7007 18.8553 11.7376Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M20.8358 9.50405C20.7997 9.64972 20.6547 9.73793 20.5118 9.70108C20.369 9.66423 20.2826 9.51627 20.3187 9.3706C20.3548 9.22494 20.4999 9.13672 20.6427 9.17357C20.7855 9.21042 20.872 9.35838 20.8358 9.50405Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M20.2236 10.8585C20.3664 10.8954 20.5115 10.8072 20.5476 10.6615C20.5838 10.5158 20.4973 10.3679 20.3545 10.331C20.2117 10.2942 20.0666 10.3824 20.0305 10.5281C19.9943 10.6737 20.0808 10.8217 20.2236 10.8585Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M20.2601 11.8202C20.2239 11.9659 20.0789 12.0541 19.9361 12.0173C19.7932 11.9804 19.7068 11.8325 19.7429 11.6868C19.779 11.5411 19.9241 11.4529 20.0669 11.4898C20.2097 11.5266 20.2962 11.6746 20.2601 11.8202Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M17.5535 13.2993C17.3048 13.2302 17.0483 13.3798 16.9805 13.6335L16.4671 15.556C16.3994 15.8097 16.5461 16.0714 16.7948 16.1405L18.3726 16.5789C18.6214 16.648 18.8779 16.4983 18.9457 16.2446L19.459 14.3221C19.5268 14.0684 19.3801 13.8067 19.1313 13.7376L17.5535 13.2993Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M16.1595 18.5958C16.1233 18.7414 15.9783 18.8297 15.8355 18.7928C15.6927 18.756 15.6062 18.608 15.6423 18.4623C15.6784 18.3167 15.8235 18.2284 15.9663 18.2653C16.1091 18.3021 16.1956 18.4501 16.1595 18.5958Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M15.5479 19.9518C15.6907 19.9887 15.8358 19.9004 15.8719 19.7548C15.908 19.6091 15.8215 19.4612 15.6787 19.4243C15.5359 19.3875 15.3909 19.4757 15.3547 19.6213C15.3186 19.767 15.4051 19.915 15.5479 19.9518Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M15.5857 20.9135C15.5496 21.0592 15.4045 21.1474 15.2617 21.1106C15.1189 21.0737 15.0324 20.9257 15.0686 20.7801C15.1047 20.6344 15.2497 20.5462 15.3925 20.583C15.5354 20.6199 15.6218 20.7679 15.5857 20.9135Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M16.9182 19.0725C17.061 19.1093 17.2061 19.0211 17.2422 18.8755C17.2784 18.7298 17.1919 18.5818 17.0491 18.545C16.9062 18.5081 16.7612 18.5963 16.7251 18.742C16.6889 18.8877 16.7754 19.0356 16.9182 19.0725Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M16.9554 20.0329C16.9193 20.1786 16.7742 20.2668 16.6314 20.2299C16.4886 20.1931 16.4021 20.0451 16.4383 19.8995C16.4744 19.7538 16.6194 19.6656 16.7623 19.7024C16.9051 19.7393 16.9915 19.8872 16.9554 20.0329Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M16.3432 21.3887C16.486 21.4255 16.6311 21.3373 16.6672 21.1916C16.7033 21.046 16.6169 20.898 16.474 20.8612C16.3312 20.8243 16.1862 20.9125 16.15 21.0582C16.1139 21.2039 16.2004 21.3518 16.3432 21.3887Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M18.3259 19.1538C18.2898 19.2995 18.1447 19.3877 18.0019 19.3509C17.8591 19.314 17.7726 19.1661 17.8087 19.0204C17.8449 18.8747 17.9899 18.7865 18.1327 18.8234C18.2755 18.8602 18.362 19.0082 18.3259 19.1538Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M17.7143 20.5096C17.8571 20.5465 18.0022 20.4582 18.0383 20.3126C18.0744 20.1669 17.988 20.019 17.8451 19.9821C17.7023 19.9453 17.5573 20.0335 17.5212 20.1791C17.485 20.3248 17.5715 20.4728 17.7143 20.5096Z"
                      fill="#875BF8"
                    />
                    <path
                      d="M17.7501 21.4713C17.714 21.617 17.5689 21.7052 17.4261 21.6684C17.2833 21.6315 17.1968 21.4835 17.2329 21.3379C17.2691 21.1922 17.4141 21.104 17.5569 21.1408C17.6997 21.1777 17.7862 21.3257 17.7501 21.4713Z"
                      fill="#875BF8"
                    />
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M19.8712 34.0547L20.8048 32.446C21.5288 31.1982 21.8909 30.5743 22.4724 30.2293C23.054 29.8843 23.7861 29.8714 25.2505 29.8457C27.4123 29.8077 28.7682 29.6726 29.9053 29.1922C32.0151 28.3008 33.6913 26.5911 34.5652 24.4391C35.2206 22.8251 35.2206 20.779 35.2206 16.6869V14.9304C35.2206 9.18057 35.2206 6.30562 33.9518 4.19369C33.2418 3.01195 32.2677 2.01837 31.1091 1.2942C29.0386 0 26.2201 0 20.583 0H15.4168C9.77979 0 6.96126 0 4.89074 1.2942C3.73217 2.01837 2.75808 3.01195 2.04811 4.19369C0.779297 6.30562 0.779297 9.18054 0.779297 14.9304V16.6869C0.779297 20.779 0.779297 22.8251 1.43472 24.4391C2.30861 26.5911 3.98482 28.3008 6.09459 29.1922C7.23168 29.6726 8.58751 29.8077 10.7493 29.8457C12.2137 29.8714 12.9458 29.8843 13.5274 30.2293C14.1089 30.5743 14.471 31.1982 15.1951 32.446L16.1286 34.0547C16.9606 35.4886 19.0392 35.4886 19.8712 34.0547ZM12.4933 21.5717C13.2487 20.8012 13.2487 19.5519 12.4933 18.7813L8.69413 14.9062L12.5387 11.0846C13.3038 10.3241 13.3196 9.07487 12.574 8.29443C11.8283 7.514 10.6036 7.49788 9.8385 8.25843L4.7457 13.3207C4.68277 13.3707 4.62216 13.4252 4.56431 13.4842C4.26582 13.7887 4.08527 14.1679 4.02266 14.5628C3.92086 15.1646 4.09356 15.8061 4.54341 16.2769C4.60953 16.3461 4.67942 16.4093 4.75242 16.4665L9.75753 21.5717C10.513 22.3423 11.7378 22.3423 12.4933 21.5717ZM23.4785 8.26618C22.7252 9.03453 22.7252 10.2803 23.4785 11.0486L27.2682 14.9142L23.4346 18.7249C22.6716 19.4833 22.6559 20.7289 23.3994 21.5071C24.1429 22.2853 25.3641 22.3014 26.127 21.543L31.2197 16.4809C31.2768 16.4346 31.3319 16.3845 31.3847 16.3306C31.6394 16.0708 31.8079 15.7565 31.8904 15.4246C32.0577 14.7782 31.8977 14.061 31.407 13.5474C31.3413 13.4787 31.2718 13.4158 31.1992 13.3589L26.2064 8.26618C25.4531 7.49782 24.2317 7.49782 23.4785 8.26618ZM17.429 8.54964C17.7374 7.37563 18.9205 6.67892 20.0715 6.99349C21.2225 7.30807 21.9055 8.5148 21.5971 9.68882L18.5401 21.3261C18.2317 22.5001 17.0486 23.1968 15.8976 22.8822C14.7466 22.5676 14.0635 21.3609 14.372 20.1869L17.429 8.54964Z"
                      fill="#875BF8"
                    />
                  </svg>
                </div>

                <button
                  className=" absolute bottom-1 right-4 z-10 select-none button--outlined-primary px-2 py-1"
                  style={{
                    backgroundColor: "#0D0D22",
                    zIndex: 10,
                  }}
                  onClick={handleResetAssertions}
                >
                  Reset Assertions
                </button>

                <div className="flex justify-center items-center mb-2">
                  <div className="flex gap-1 p-1 text-drcodePurple justify-center font-medium rounded-lg flex-1">
                    Missing any assertions ?{" "}
                    <p
                      onClick={() => {
                        setIsClicked(true);
                      }}
                      className="  text-warning-200 font-medium cursor-pointer"
                    >
                      Click Here
                    </p>
                  </div>
                  {isChanged && (
                    <div className="flex float-right items-center ml-auto">
                      <button
                        className="button--outlined-primary py-[6px] px-2 mx-2 text-sm"
                        onClick={() => {
                          runAssertionsLocally(
                            idx,
                            apiTestDetails.response,
                            apiTestDetails
                          );
                          setActiveTab(3);
                          // saveRequest();
                        }} // Use the new handler
                      >
                        Run Assertions
                      </button>
                    </div>
                  )}
                </div>
                <ApiRequestResponseOutputView
                  onChange={(e) => {
                    modifyAssertionRow(idx, e || "", apiTestDetails);
                    setIsChanged(true);
                  }}
                  readonly={false}
                  value={extractAssertions(apiTestDetails.assertions) || ""}
                  responseType="js"
                />
              </>
            ) : (
              <p className="text-sm/6 font-semibold text-[#B2B2C1]">
                Run the testcase to see the Assertions
              </p>
            )}
          </>
        )}
        {activeTab === 3 && (
          <>
            <AssertionResults list={assertions} />
            {assertions.length > 0 && (
              <div className="text-center text-xs mt-2 text-vscode-editor-foreground ">
                AI-powered! Double-check for accuracy.{" "}
                <span
                  onClick={() => setActiveTab(2)}
                  className="text-warning-200 cursor-pointer"
                >
                  Review here.
                </span>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default memo(ApiTestDetailedView);
