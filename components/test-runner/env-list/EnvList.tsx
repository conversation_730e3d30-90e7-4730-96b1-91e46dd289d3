// "use client";
// import { ButtonOutlinedPrimary } from "@/components/common/button-outlined-primary/ButtonOutlinedPrimary";
// import { useGlobalStore } from "@/stores/globalstore";
// import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
// import {
//   LOCAL_STORAGE_DATA_KEYS,
//   UserData,
// } from "@/temp-utils/Constants/localStorageDataModels";
// import { getDataFromLocalStorage } from "@/temp-utils/globalUtilities";
// import axios from "axios";
// import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
// import { useParams, useRouter } from "next/navigation";

// const EnvList = (props: {
//   className?: string;
//   setSelectedEnvs?: Dispatch<
//     SetStateAction<{ value: string; label: string }[]>
//   >;
//   setSelectedEnvironment?: Dispatch<SetStateAction<string | null>>;
// }) => {
//   const { showLoader, environment } = useGlobalStore();
//   const router = useRouter();

//   const userData: UserData = getDataFromLocalStorage(
//     LOCAL_STORAGE_DATA_KEYS.USER_DATA
//   ) as UserData;

//   // console.log("log:: selected environment ",environment, selectedEnvironment)
//   const getEnvironmentList = async () => {
//     try {
//       //showLoader(true);
//       const response = await axios.get(
//         `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables/user/${userData.user_id}`
//       );
//       const responseData = response.data;
//       // Find the selected environment based on matching id
//       const selectedEnv = responseData?.find(
//         (item: any) => item.id === environment
//       );
//       if (selectedEnv) {
//         props.setSelectedEnvironment(selectedEnv.name);

//         const envObj = selectedEnv.env;
//         Object.keys(envObj).forEach((key) => {
//           props.setSelectedEnvs &&
//             props.setSelectedEnvs((prev) => {
//               return [...prev, { value: envObj[key], label: key }];
//             });
//         });
//       }
//     } catch (err) {
//       console.error("Error fetching environments:", err);
//     } finally {
//       //showLoader(false);
//     }
//   };

//   useEffect(() => {
//     getEnvironmentList();
//   }, [environment]);

//   return (
//     // <ButtonOutlinedPrimary text={`Environment: ${selectedEnvironment}`} size='small' onClick={() => { router.push(`/environment/${environment}`) }} />
//     <p
//       onClick={() => {
//         router.push(`/environment/${environment}`);
//       }}
//       className="font-semibold text-[14px] py-2 cursor-pointer"
//     >
//       Edit Environment
//     </p>
//   );
// };

// export default EnvList;

import { useGlobalStore } from "@/stores/globalstore";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import {
  LOCAL_STORAGE_DATA_KEYS,
  UserData,
} from "@/temp-utils/Constants/localStorageDataModels";
import { getDataFromLocalStorage } from "@/temp-utils/globalUtilities";
import axios from "axios";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { KeyValuePair } from "tailwindcss/types/config";
import styles from "./styles.module.scss";
import Image from "next/image";
import { ButtonPrimary } from "@/components/common/button-primary/ButtonPrimary";
import { ButtonOutlinedPrimary } from "@/components/common/button-outlined-primary/ButtonOutlinedPrimary";
import PrimaryInput from "@/components/common/primary-input/PrimaryInput";
import Loader from "@/components/common/loader/Loader";
import useToast from "@/hooks/useToast";

const EnvList = ({
  environmentId,
  handleClose = () => {},
  setSelectedEnvOptions=()=>{},
}: {
  environmentId: string;
  handleClose: () => void;
  setSelectedEnvOptions?:any;
}) => {
  const [keyValuePairs, setKeyValuePairs] = useState<KeyValuePair[]>([
    { key: "", value: "" },
  ]);
  const [environmentName, setEnvironmentName] = useState("");
  const [showLoader,setShowLoader]=useState(false);
  const userData: UserData = getDataFromLocalStorage(
    LOCAL_STORAGE_DATA_KEYS.USER_DATA
  ) as UserData;

  const {showToast} = useToast()

  // const handleInputChange = (
  //   index: number,
  //   field: keyof KeyValuePair,
  //   value: string
  // ) => {
  //   const updatedPairs = [...keyValuePairs];
  //   updatedPairs[index][field] = value;
  //   if (
  //     index === keyValuePairs.length - 1 &&
  //     (updatedPairs[index].key || updatedPairs[index].value)
  //   ) {
  //     updatedPairs.push({ key: "", value: "" });
  //   }
  //   setKeyValuePairs(updatedPairs);
  // };


  const handleInputChange = (
    index: number,
    field: keyof KeyValuePair,
    value: string
  ) => {
    const updatedPairs = [...keyValuePairs];
    updatedPairs[index][field] = value;
  
    // If typing in the last row, ensure an extra empty row exists
    const lastPair = updatedPairs[updatedPairs.length - 1];
    if (
      (index === updatedPairs.length - 1) && // Editing the last row
      (lastPair.key || lastPair.value) // If it contains any value
    ) {
      updatedPairs.push({ key: "", value: "" });
    }
  
    // If the last two rows are empty, remove the last row
    while (
      updatedPairs.length > 1 && // Ensure at least one row remains
      !updatedPairs[updatedPairs.length - 1].key && 
      !updatedPairs[updatedPairs.length - 1].value && 
      !updatedPairs[updatedPairs.length - 2].key && 
      !updatedPairs[updatedPairs.length - 2].value
    ) {
      updatedPairs.pop();
    }
  
    setKeyValuePairs(updatedPairs);
  };
  
  
  
  const deleteKeyValuePair = (index: number) => {
    const filterArray = keyValuePairs.filter(
      (value, keyValueindex) => index != keyValueindex
    );
    setKeyValuePairs(filterArray);
  };

  const createEnvironment = async () => {
    try {
      setShowLoader(true)
      let environmentRequestObj: any = {
        name: environmentName,
        env: {},
        user_id: userData.user_id,
      };
      let emptyObject = environmentRequestObj?.env;
      keyValuePairs.pop();
      keyValuePairs.map((item) => {
        emptyObject[item.key] = item.value;
      });
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables`,
        environmentRequestObj
      );
      if (response.status == 201) {
        // toast.success("Environment Added");
        showToast("Environment Added", "success")
        handleClose();
      }
    } catch (err) {
      console.error("Error:", err);
    } finally {
      setShowLoader(false)
    }
  };
  const updateEnvironment = async () => {
    try {
      //setShowLoader(true)
      let environmentRequestObj: any = {
        name: environmentName,
        env: {},
        user_id: userData.user_id,
      };
      let emptyObject = environmentRequestObj?.env;
      keyValuePairs.pop();
      keyValuePairs.map((item) => {
        emptyObject[item.key] = item.value;
      });
      const response = await axios.put(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables/${environmentId}`,
        environmentRequestObj
      );
      if (response.status == 200) {
        // toast.success("Environment Updated");
        showToast("Environment Updated", "success")
        setSelectedEnvOptions(()=>{
        return  Object.keys(environmentRequestObj.env).map((key)=>{
            return {value:environmentRequestObj.env[key],label:key}
          }
          )
        })
       
       
        handleClose();
      }
    } catch (err) {
      console.error("Error:", err);
    } finally {
      setShowLoader(false)
    }
  };
  const getEnvironmentDetails = async () => {
    try {
      setShowLoader(true)
      const response = await axios.get(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables/${environmentId}`
      );
      let responseData: {
        id: number;
        env: any;
        user_id: string;
        name: string;
        created_at: string;
        updated_at: string;
      } = response.data;
      setEnvironmentName(responseData.name);
      let empyArray: KeyValuePair[] = [];
      for (let key in responseData.env) {
        empyArray.push({ value: responseData.env[key], key: key });
      }
      empyArray.push({ value: "", key: "" });

      setKeyValuePairs(empyArray);
    } catch (err) {
      console.error("Error:", err);
    } finally {
      setShowLoader(false)
    }
  };

  useEffect(() => {
    if (environmentId !== "add") {
      getEnvironmentDetails();
    }
  }, []);

  return (
    <div>
      {
        showLoader && <Loader/>
      }
      <div className={`${styles["environment-details-container"]} `}>
        <p className="title--medium mt-3">Environment name</p>
        <PrimaryInput
          value={environmentName}
          onChange={(e) => setEnvironmentName(e.target.value)}
          border="primary--border"
        />

        <div className="mt-4">Variables</div>

        <div className={`${styles["key-value-table"]}`}>
          {keyValuePairs.length > 0 &&
            keyValuePairs.map((pair, index) => (
              <div
                key={index}
                className={` flex gap-2 w-full mb-2 items-center`}
              >
                {/* <input
                  value={pair.key}
                  onChange={(event) =>
                    handleInputChange(index, "key", event.target.value)
                  }
                  className="primary-input !rounded-none text-white"
                  placeholder="Enter Variable"
                /> */}
                <PrimaryInput
                  value={pair.key}
                  onChange={(e) =>
                    handleInputChange(index, "key", e.target.value)
                  }
                  border="primary--border"
                />
                {/* <input
                  value={pair.value}
                  onChange={(event) =>
                    handleInputChange(index, "value", event.target.value)
                  }
                  className="primary-input !rounded-none  text-white"
                  placeholder="Enter value"
                /> */}
                <PrimaryInput
                  value={pair.value}
                  onChange={(e) =>
                    handleInputChange(index, "value", e.target.value)
                  }
                  border="primary--border"
                />
                <div
                  onClick={() => deleteKeyValuePair(index)}
                  className={`cursor-pointer`}
                >
                  <Image src="/trashbin.svg" alt="" width={35} height={35} />
                </div>
              </div>
            ))}
        </div>
        <div className="flex gap-4 mt-4 justify-end">
          <ButtonOutlinedPrimary
            onClick={handleClose}
            size="medium"
            text="Cancel"
            style={{
              fontSize: "14px",
            }}
          />
          {environmentId == "add" ? (
            <ButtonPrimary
              onClick={createEnvironment}
              size="medium"
              text="Create"
              style={{
                fontSize: "14px",
              }}
            />
          ) : (
            <ButtonPrimary
              onClick={updateEnvironment}
              size="medium"
              text="Update"
              style={{
                fontSize: "14px",
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default EnvList;
