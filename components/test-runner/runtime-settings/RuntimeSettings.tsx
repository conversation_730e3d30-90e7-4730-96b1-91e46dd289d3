import PrimaryInput from "@/components/common/primary-input/PrimaryInput";
import { Tooltip } from "@nextui-org/react";
import React, { useState } from "react";

interface RuntimeSettingsProps {
  waitTime: number;
  setWaitTime: (value: number) => void;
  minElapsedTime: number;
  setMinElapsedTime: (value: number) => void;
  runDelay: number;
  setRunDelay: (value: number) => void;
  concurrency: number;
  concurrencyFunction: (value: number) => void;
  handleModalClose: () => void;
}

const RuntimeSettings = ({
  waitTime,
  setWaitTime,
  minElapsedTime,
  setMinElapsedTime,
  runDelay,
  setRunDelay,
  concurrency,
  concurrencyFunction,
  handleModalClose,
}: RuntimeSettingsProps) => {
  const [originalValues] = useState({
    waitTime: waitTime,
    minElapsedTime: minElapsedTime,
    runDelay: runDelay,
    concurrency: concurrency,
  });

  const handleCancel = () => {
    setWaitTime(originalValues.waitTime);
    setMinElapsedTime(originalValues.minElapsedTime);
    setRunDelay(originalValues.runDelay);
    concurrencyFunction(originalValues.concurrency);
    handleModalClose();
  };

  return (
    <div>
      <div className="grid grid-cols-2 gap-4 items-center justify-center mt-2">
        {/* Wait Time Input */}
        <div className="flex flex-col">
          <label htmlFor="waitTime" className="mb-1 text-[12px]">
            Timeout Duration for Request Completion (ms)
          </label>
          <PrimaryInput
            type="number"
            id="waitTime"
            min={0}
            value={waitTime}
            onChange={(e) => setWaitTime(Number(e.target.value))}
            // className="border border-drcodePurple/50 px-3 py-2 focus:outline-drcodePurple rounded-md bg-vscode-editor-background text-white"
            placeholder="e.g., 1000"
          />
        </div>

        {/* Minimum Elapsed Time Input */}
        <div className="flex flex-col">
          <label htmlFor="minElapsedTime" className="mb-1 text-[12px]">
            Minimum elapsed time to compare (ms)
          </label>
          <PrimaryInput
            min={0}
            type="number"
            id="minElapsedTime"
            value={minElapsedTime}
            onChange={(e) => setMinElapsedTime(Number(e.target.value))}
            // className="border border-drcodePurple/50 px-3 py-2 focus:outline-drcodePurple rounded-md bg-vscode-editor-background text-white"
            placeholder="e.g., 500"
          />
        </div>

        <div className="flex flex-col">
          <label htmlFor="runIntervalDelay" className="mb-1 text-[12px]">
            Run Interval Delay (ms)
          </label>
          <PrimaryInput
            min={0}
            type="number"
            id="runIntervalDelay"
            value={runDelay}
            onChange={(e) => setRunDelay(Number(e.target.value))}
            // className="border border-drcodePurple/50 px-3 py-2 focus:outline-drcodePurple rounded-md bg-vscode-editor-background text-white"
            placeholder="e.g., 500"
          />
        </div>
        <div className="flex flex-col">
          <label htmlFor="waitTime" className="mb-1 text-[12px]">
            No. of test cases to run in parallel
          </label>
          <div title="Concurrency : Min 1  Max 5">
            <PrimaryInput
              type="number"
              id="concurrency"
              min={1}
              max={5}
              value={concurrency}
              onChange={(e) => concurrencyFunction(Number(e.target.value))}
              //   className="border border-drcodePurple/50 px-3 py-2 focus:outline-drcodePurple rounded-md bg-vscode-editor-background text-white"
              placeholder="e.g., 1000"
            />
          </div>
        </div>
      </div>
      <div className="flex gap-2 items-end justify-end">
        <button
          onClick={() => handleCancel()}
          className="button--outlined-primary mt-4 py-2 px-8 text-[14px]"
          style={{
            fontSize: "14px",
          }}
        >
          Cancel
        </button>
        <button
          onClick={() => handleModalClose()}
          className="button--primary mt-4 py-2 px-8 text-[14px]"
          style={{
            fontSize: "14px",
          }}
        >
          Save Settings
        </button>
      </div>
    </div>
  );
};

export default RuntimeSettings;
