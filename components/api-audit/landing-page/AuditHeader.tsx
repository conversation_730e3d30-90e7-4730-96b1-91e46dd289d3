"use client";
import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
// import "../../app/globals.css";
import { usePathname, useRouter } from "next/navigation";
const Logo = "../../Logo.svg";


const Header: React.FC = () => {
  const pathname = usePathname();
  const router = useRouter()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="z-50 mx-auto">
      <div className="flex justify-between items-center py-2 bg-blue bg-opacity-15 backdrop-blur-lg">
        <Link href={'/'} passHref>
          <div className="flex items-center space-x-4 cursor-pointer">
            <Image
              src={Logo}
              alt="Dr.Code AI automation for software development lifecycle"
              className="h-100 w-100"
              width={192} // Adjust width as needed
              height={90} // Adjust height as needed
              style={{ verticalAlign: "middle" }}
            />
          </div>
        </Link>
        <div className="sm:hidden">
          <button
            onClick={toggleMobileMenu}
            className="text-white focus:outline-none"
            aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              {isMobileMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 6h16M4 12h16m-7 6h7"
                />
              )}
            </svg>
          </button>
        </div>
        <div
          className={`hidden sm:flex items-center space-x-4 ${isMobileMenuOpen ? "hidden" : ""
            }`}
        >
          <Link href={'/pricing'}>
            <p
              className={`text-sm text-gray-400 mb-4 sm:mb-0 ${pathname === '/pricing'
                  ? "underline underline-offset-4 decoration-[#875BF8]"
                  : ""
                }`}
            >
              Pricing
            </p>
          </Link>
          <div className="border-l-[1px] border-[#2E2E60] h-8"></div>
          <button
            onClick={() =>
              window.open("https://discord.gg/b7Cc3RyZXV", "_blank")
            }
            className="border-2 border-[#875BF8] rounded-lg text-[#875BF8] font-base py-2 px-3 transition flex items-center space-x-2 hover:bg-[#875BF8] hover:text-white"
          >
            {/* <FaDiscord className="text-lg" /> */}
            <span className="text-base">Join Discord</span>
          </button>
          <button
            onClick={() => router.push("/api-audit-report")} // Adjusted for Next.js
            // onClick={() => window.open("https://marketplace.visualstudio.com/items?itemName=DrCodeAI.drcode-vscode-tool", "_blank")}
            className="bg-[#875BF8] border border-[#875BF8] rounded-lg text-white font-base py-2 px-8 transition hover:bg-[#7649d9]"
          >
            <span className="text-base">Login</span>
          </button>
        </div>
      </div>
      {isMobileMenuOpen && (
        <div className="sm:hidden flex flex-col items-center bg-[#875BF8] bg-opacity-15 backdrop-blur-lg p-4 space-y-4 rounded-lg">
          <button
            onClick={() =>
              window.open("https://discord.gg/b7Cc3RyZXV", "_blank")
            }
            className="border-2 border-[#875BF8] rounded-lg text-[#875BF8] font-base py-2 px-3 transition flex items-center space-x-2 hover:bg-[#875BF8] hover:text-white justify-center"
          >
            {/* <FaDiscord className="text-lg" /> */}
            <span className="text-base">Join Discord</span>
          </button>
          <Link href={'/pricing'}>
            <p
              className={`text-sm text-[#875BF8] text-[16px]`}
            >
              Pricing
            </p>
          </Link>

          <button
            // onClick={() => (window.location.href = "/login")} // Adjusted for Next.js
            onClick={() => router.push("/api-audit-report")}
            className="bg-[#875BF8] border border-[#875BF8] rounded-lg text-white font-base py-2 px-14 transition hover:bg-[#7649d9] "
          >
            <span className="text-base">Login</span>
          </button>
        </div>
      )}
    </header>
  );
};

export default Header;
