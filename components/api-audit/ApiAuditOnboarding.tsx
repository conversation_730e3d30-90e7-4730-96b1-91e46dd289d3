"use client";
import React from "react";
import AddApi<PERSON>ostman from "../add-api-postman/AddApiPostman";
import Modal from "../common/modal/Modal";
import {
  EnvironmentVariable,
  loadPostmanCollection,
  PostmanCollection,
} from "@/temp-utils/Services/parsingPostmanCollection";
import {
  MAX_PAYLOAD_SIZE,
  samplePostmanCollection,
} from "@/temp-utils/Constants/samplePostmanCollection";
import { NEXT_PUBLIC_DR_CODE_BASE_API_URL } from "@/temp-utils/Constants/globalVariables";
import axios from "axios";
import { Checkbox } from "@mui/material";
import {
  CustomCheckedIcon,
  CustomIcon,
  CustomIndeterminateIcon,
  GroupTableData,
} from "@/app/project/[projectId]/page";
import { getDataFromLocalStorage } from "@/temp-utils/globalUtilities";
import {
  LOCAL_STORAGE_DATA_KEYS,
  UserData,
} from "@/temp-utils/Constants/localStorageDataModels";
import PrimaryInput from "../common/primary-input/PrimaryInput";
import { KeyValuePair } from "tailwindcss/types/config";
import Image from "next/image";
import useToast from "@/hooks/useToast";
import { useParams, useRouter } from "next/navigation";
import { GLOBAL_API_ROUTES } from "@/services/apiRoutes";
import FloatingDropdown from "../common/floating-dropdown/FloatingDropdown";
import { LuUserRound } from "react-icons/lu";
import { IoChevronDownOutline } from "react-icons/io5";
import PrimaryButton from "../common/buttons/PrimaryButton";

const MAX_API_COUNT = 4;

const ApiAuditOnboarding = ({ isFresh = true }) => {
  const [postmanCollection, setPostmanCollection] = React.useState<any[]>([]);
  const [sampleCollection, setSampleCollection] = React.useState<string>(
    JSON.stringify(samplePostmanCollection, null, 4)
  );
  const [showAddGroupPopup, setShowAddGroupPopup] = React.useState(false);
  const [createdGroupEnvironmentId, setCreatedGroupEnvironmentId] =
    React.useState<number>(-1);
  const [showAddImportPopup, setShowAddImportPopup] =
    React.useState<boolean>(false);
  const [showSelectApiModal, setShowSelectApiModal] =
    React.useState<boolean>(false);
  const [errorMessage, setErrorMessage] = React.useState<string>("");
  const [selectedApis, setSelectedApis] = React.useState<any[]>([]);
  const [
    showExtractedEnvironmentVariables,
    setShowExtractedEnvironmentVariables,
  ] = React.useState<boolean>(false);
  const [keyValuePairs, setKeyValuePairs] = React.useState<
    EnvironmentVariable[]
  >([]);
  const [groupName, setGroupName] = React.useState("");
  const [groupDescription, setGroupDescription] = React.useState("");
  const [groupDetail, setGroupDetails] = React.useState<GroupTableData>({
    created_at: "",
    description: "",
    env: null,
    env_id: -1,
    group_name: "",
    id: -1,
    project_id: -1,
    updated_at: "",
  });

  const [apiList, setApiList] = React.useState<[]>([]);

  const { projectId } = useParams();

  const { showToast } = useToast();

  const router = useRouter();

  const onAddApi = async (postmanData = []) => {
    const postman_collection = postmanCollection
      .map((item) => (selectedApis.includes(item.id) ? item : null))
      .filter((item) => item);

    console.log(
      "postman collection is ",
      postman_collection.length,
      selectedApis.length
    );

    // handleLoadCollection();

    // setShowExtractedEnvironmentVariables(true);

    if (postmanCollection.length === 0 && postmanData.length === 0) {
      // toast.error("Please Add postman collection");
      showToast("Please Add postman collection", "error");
      return;
    }

    const playgroundPayload: {
      projectId: string | string[];
      groupId: string | string[];
      method: string;
      testSuiteName: string;
      url: string;
      request: {
        headers: any;
        path_params: {};
        query_params: {};
        request_body: any;
      };
    }[] = [];

    if (postmanData.length > 0) {
      playgroundPayload.push(...postmanData);
    } else {
      playgroundPayload.push(...postman_collection);
    }

    const data = playgroundPayload.map((api: any) => ({
      groupId: groupDetail.id,
      method: api.method,
      testSuiteName:
        api.controller === api.name
          ? api.name
          : `${api.controller} - ${api.name}`,
      url: api.url,
      request: api.details,
      description: api.description,
    }));

    try {
      //showLoader(true);

      const jsonString = JSON.stringify(data);

      const payloadSize = new Blob([jsonString]).size; // Browser

      if (payloadSize > MAX_PAYLOAD_SIZE) {
        setErrorMessage(
          `Payload size exceeds the  limit. Please reduce the number of selected APIs or optimize your data.`
        );
        return;
      }

      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/testSuites/bulk`,
        data
      );
      if (response.data.success) {
        setShowExtractedEnvironmentVariables(true);
        handleLoadCollection();
        // toast.success("Added successfully");
        showToast("APIs added successfully", "success");
        // triggerRevalidateTestsExplorer();
        setShowSelectApiModal(false);
        setShowAddImportPopup(false);
        setApiList(response.data.data);
      } else {
        setErrorMessage(
          response.data.message +
            "Error : " +
            (response.data.errors && response.data.errors[0]
              ? response.data.errors[0]
              : "")
        );
      }
    } catch (err: any) {
      //showLoader(false);

      // toast.success(err.response?.data?.message || "Something went wrong");
      showToast(err.response?.data?.message || "Something went wrong", "error");
      setShowExtractedEnvironmentVariables(true);

      console.log(err);
    } finally {
      //showLoader(false);
    }
  };

  const handleLoadCollection = async () => {
    if (sampleCollection) {
      try {
        const result = await loadPostmanCollection(
          JSON.parse(sampleCollection)
        );
        const envArray: EnvironmentVariable[] = result.env;
        console.log("log:: env array is ", envArray);
        setKeyValuePairs([...envArray, { key: "", value: "" }]);
      } catch (error) {
        console.error("Error loading collection:", error);
        throw error; // Propagate the error
      }
    } else {
      console.warn("No Postman collection data provided.");
      throw new Error("No Postman collection data provided.");
    }
  };

  const userData: UserData = getDataFromLocalStorage(
    LOCAL_STORAGE_DATA_KEYS.USER_DATA
  ) as UserData;

  const handleSelectAllChange = (isChecked: boolean) => {
    if (isChecked) {
      // Select only the first 15 APIs
      const firstFindIds = postmanCollection
        .slice(0, MAX_API_COUNT)
        .map((api) => api.id);
      setSelectedApis(firstFindIds);
    } else {
      setSelectedApis([]);
    }
  };

  const handleCheckboxChange = (id: string, isChecked: boolean) => {
    setSelectedApis((prevSelected) => {
      if (isChecked) {
        // Prevent adding more if 15 are already selected
        if (prevSelected.length >= MAX_API_COUNT) return prevSelected;
        // Add the new selection
        return [...prevSelected, id];
      } else {
        // Remove the deselected item
        return prevSelected.filter((apiId) => apiId !== id);
      }
    });
  };

  const getMethodClasses = (method: string) => {
    switch (method.toUpperCase()) {
      case "GET":
        return "text-green-500 bg-green-500/20"; // Increased contrast
      case "POST":
        return "text-orange-500 bg-orange-500/20"; // More vibrant
      case "PUT":
        return "text-purple-600 bg-purple-600/20"; // Darker shade for better visibility
      case "PATCH":
        return "text-indigo-400 bg-indigo-400/20"; // Using indigo for better contrast
      case "DELETE":
        return "text-red-500 bg-red-500/20"; // More visible red
      default:
        return "";
    }
  };

  const handleInputChange = (
    index: number,
    field: keyof KeyValuePair,
    value: string
  ) => {
    const updatedPairs = [...keyValuePairs];
    updatedPairs[index][field] = value;
    if (
      index === keyValuePairs.length - 1 &&
      (updatedPairs[index].key || updatedPairs[index].value)
    ) {
      updatedPairs.push({ key: "", value: "" });
    }
    setKeyValuePairs(updatedPairs);
  };

  const deleteKeyValuePair = (index: number) => {
    const filterArray = keyValuePairs.filter(
      (value, keyValueindex) => index != keyValueindex
    );
    setKeyValuePairs(filterArray);
  };

  const updateEnvironment = async () => {
    try {
      //showLoader(true)
      let environmentRequestObj: any = {
        env: {},
        user_id: userData.user_id,
      };
      let emptyObject = environmentRequestObj?.env;
      keyValuePairs.pop();
      keyValuePairs.map((item) => {
        emptyObject[item.key] = item.value;
      });
      const response = await axios.put(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables/${createdGroupEnvironmentId}`,
        environmentRequestObj
      );
      if (response.status == 200) {
        // showToast("Environment Updated", "success");
        // setShowExtractedEnvironmentVariables(false);
        handleApiAudit();
      }
    } catch (err) {
      //showLoader(false);
      console.error("Error:", err);
    } finally {
      //showLoader(false)
    }
  };

  const createEnvironmentVariables = async (data: any) => {
    try {
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}/environmentVariables`,
        data
      );
      return response;
    } catch (error) {
      console.error("Error in creating environment variables:", error);
    }
  };

  const createGroup = async () => {
    setShowAddGroupPopup(false);
    try {
      //showLoader(true)
      const envResponse = await createEnvironmentVariables({
        name: "",
        env: {},
        user_id: userData.user_id,
      });
      let environmentVariable = envResponse.data.id;

      let groupRequestObj = {
        project_id: projectId,
        env: environmentVariable,
        env_id: environmentVariable,
      };
      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.CREATE_API_AUDIT_GROUP}`,
        groupRequestObj
      );

      if (response.data.success || true) {
        showToast("Folder created", "success");
        setCreatedGroupEnvironmentId(environmentVariable);
        setGroupDetails(response.data.data);
        setShowAddImportPopup(true);
      }

      console.log({ response });
    } catch (err) {
      //showLoader(false);
      console.error("Error:", err);
      setShowAddImportPopup(true);
    } finally {
      //showLoader(false)
    }
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    if (!groupName) {
      // toast.error("Please enter folder name");
      showToast("Please enter folder name", "error");
      return;
    }
    createGroup();
  };

  const handleApiAudit = async () => {
    try {
      const data = apiList.map((api: any) => ({
        method: api.method,
        test_suite_id: api.id,
        testSuiteName: `${api.test_suite_name}`,
        url: api.url,
        request: api.request,
        // environment_variables:,
        path_params: api.request?.path_params,
        query_parmas: api.request?.query_params,
        headers: api.request?.headers,
        json_body: api.request?.request_body,
        user_input: api.user_input || {},
        description: api.description,
        requires_auth:
          "Authorization" in api || "authorization" in api
            ? api.request?.headers
            : "",
      }));

      const payloadData = {
        apiList: data,
        // userId: userData.user_id,
        group_id: groupDetail.id,
      };

      const response = await axios.post(
        `${NEXT_PUBLIC_DR_CODE_BASE_API_URL}${GLOBAL_API_ROUTES.CREATE_API_AUDIT_REPORT}`,
        payloadData
      );

      if (response.data.success) {
        // toast.success("Test cases generated successfully");
        showToast("API audit report is being generated", "success");
        router.push(`/project/${projectId}/api-audit/${response.data.data.id}`);
      }
    } catch (error) {
      // toast.error("Something went wrong, please try again later.");
      console.log("log:: error is", error?.response?.data?.error);
      showToast(
        `${
          error?.response?.data?.error ??
          "Something went wrong, please try again later."
        } `,
        "error"
      );
    }

    // handleCloseModal();
  };

  const handleCloseModal = () => {
    setShowAddImportPopup(false);
    setShowSelectApiModal(false);
    setShowExtractedEnvironmentVariables(false);
    setKeyValuePairs([]);
  };

  return (
    <div>
      <button
        onClick={() => {
          createGroup();
        }}
        className="button--primary mt-4 py-2 px-8 text-[14px]"
        style={{ fontSize: "14px" }}
      >
        {isFresh ? "Let's get started" : "New Audit"}
      </button>

      {showAddImportPopup && (
        <Modal
          isOpen={showAddImportPopup}
          onClose={handleCloseModal}
          title={
            <>
              <div className="font-inter">
                <p className="  text-background text-2xl font-inter font-semibold ">
                  Drop in your APIs
                </p>
                <span className="text-[#9494A1] text-sm">
                  Import your APIs to launch the testing mission!
                </span>
              </div>
            </>
          }
          showFooterButtons={true}
          //   showBackButton={true}
          saveLabel="Continue"
          cancelLabel="Close"
          onSave={() => {
            setShowSelectApiModal(true);
          }}
          onCancel={() => {
            setShowAddImportPopup(false);
          }}
          onBack={() => {
            setShowAddImportPopup(false);
            //   setShowAddGroupPopup(true);
          }}
          isDisabled={postmanCollection.length === 0}
        >
          <div className="relative">
            <AddApiPostman
              setSampleCollection={setSampleCollection}
              setPostmanCollection={setPostmanCollection}
              groupId={0}
              setShowSelectApiModal={setShowSelectApiModal}
              setSelectedApis={setSelectedApis}
              // key={refreshKey}
              setErrorMessage={setErrorMessage}
            />

            {errorMessage && (
              <div className="text-red-500  mt-2 text-[12px]">
                {errorMessage}
              </div>
            )}
            <div
              onClick={() => {
                loadPostmanCollection(
                  samplePostmanCollection as PostmanCollection
                ).then((data) => {
                  setPostmanCollection(data.apis);
                  const selectedApisIds = data.apis.map((item) => item.id);
                  setSelectedApis(selectedApisIds);
                  setShowSelectApiModal(true);
                  // onAddApi(data.apis);
                });
              }}
              className="text-xs text-drcodePurple mt-2 hover:underline cursor-pointer"
            >
              Don't have an API ready? Try with a sample API instead!
            </div>
          </div>
        </Modal>
      )}

      {showSelectApiModal && (
        <Modal
          isOpen={showSelectApiModal}
          onClose={handleCloseModal}
          title={
            <>
              <p className="text-background text-2xl font-inter font-semibold">
                Select APIs
              </p>
              <span className="text-[#9494A1] text-sm">
                Ready for testing? Pick your targets for the mission!
              </span>

              <p>*Only {MAX_API_COUNT} APIs are allowed at max</p>
            </>
          }
          showFooterButtons={true}
          showBackButton={true}
          saveLabel="Continue"
          cancelLabel="Close"
          backLabel="Back"
          onSave={() => {
            onAddApi();
          }}
          onCancel={handleCloseModal}
          onBack={() => {
            setShowSelectApiModal(false);
            setShowAddImportPopup(true);
          }}
          isDisabled={selectedApis.length === 0}
        >
          <div className="flex justify-between items-center text-[14px]">
            <div className="mb-2 flex items-center gap-2">
              <Checkbox
                checked={
                  selectedApis.length > 0 &&
                  selectedApis.length ===
                    Math.min(postmanCollection.length, MAX_API_COUNT)
                }
                indeterminate={
                  selectedApis.length > 0 &&
                  selectedApis.length <
                    Math.min(postmanCollection.length, MAX_API_COUNT)
                }
                onChange={(e) => handleSelectAllChange(e.target.checked)}
                icon={<CustomIcon />}
                checkedIcon={
                  <CustomCheckedIcon>
                    <svg className="font-bold" viewBox="0 0 24 24">
                      <path
                        d="M9 16.2l-4.2-4.2L4 13l5 5 12-12-1.4-1.4z"
                        fill="#FFFFFF"
                      />
                    </svg>
                  </CustomCheckedIcon>
                }
                indeterminateIcon={<CustomIndeterminateIcon />}
              />
              <label className="text-sm">Select all</label>
            </div>

            <div className="flex">
              <span>
                {selectedApis.length} / {postmanCollection.length} selected
              </span>
            </div>
          </div>
          <div className="bg-transparent border-1 border-white rounded-md p-2">
            <div className="max-h-[270px] overflow-y-auto">
              {postmanCollection.map((api) => {
                const isSelected = selectedApis.includes(api.id);
                const disableCheckbox =
                  !isSelected && selectedApis.length >= MAX_API_COUNT;

                return (
                  <div className="flex items-center gap-2 mb-4" key={api.id}>
                    <label className="flex items-center space-x-2 cursor-pointer">
                      <Checkbox
                        checked={isSelected}
                        disabled={disableCheckbox}
                        onChange={(e) =>
                          handleCheckboxChange(api.id, e.target.checked)
                        }
                        icon={<CustomIcon />}
                        checkedIcon={
                          <CustomCheckedIcon>
                            <svg viewBox="0 0 24 24">
                              <path
                                d="M9 16.2l-4.2-4.2L4 13l5 5 12-12-1.4-1.4z"
                                fill="#FFFFFF"
                              />
                            </svg>
                          </CustomCheckedIcon>
                        }
                      />
                    </label>

                    <div>
                      <p className="text-[11px] font-medium text-[#B2B2C1] font-sans">
                        {api.controller === api.name
                          ? api.name
                          : `${api.controller} - ${api.name}`}
                      </p>
                      <div className="text-[11px] mt-[6px] leading-[11px] text-[#EBEBF3] font-medium font-sans text-wrap">
                        <span
                          className={`text-[9px] font-medium p-[6px] mr-[10px] rounded-[4px] ${getMethodClasses(
                            api.method || ""
                          )}`}
                        >
                          {api.method}
                        </span>
                        {api.url}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {errorMessage && (
            <div className="text-red-500 text-sm mt-4">{errorMessage}</div>
          )}
        </Modal>
      )}

      {showExtractedEnvironmentVariables && (
        <Modal
          isOpen={showExtractedEnvironmentVariables}
          onClose={handleCloseModal}
          size="large"
          title={
            <>
              <p className="text-background text-xl font-inter font-semibold">
                Review Environment Variables
              </p>
              <span className="text-[#9494A1] text-sm">
                Customize auto-fetched API variables for your tests.
              </span>
            </>
          }
          showFooterButtons={true}
          saveLabel="Start Auditing"
          cancelLabel="Close"
          onSave={() => {
            updateEnvironment();
          }}
          onCancel={() => {
            setShowExtractedEnvironmentVariables(false);
            setKeyValuePairs([]);
          }}
        >
          <div className="relative min-w-[670px] max-h-[360px] overflow-auto">
            <div className={``}>
              <div className="flex w-full gap-4 justify-between mb-2">
                <span className="text-left text-[#9494A1] text-[14px] w-[48%]">
                  Variable
                </span>
                <span className="text-left text-[#9494A1] text-[14px] w-[52%]">
                  Value
                </span>
              </div>
              {keyValuePairs.length > 0 &&
                keyValuePairs.map((pair, index) => (
                  <div
                    key={index}
                    className={`flex gap-2 items-center  mb-2  `}
                  >
                    <PrimaryInput
                      value={pair.key}
                      onChange={(e) =>
                        handleInputChange(index, "key", e.target.value)
                      }
                      placeholder="Enter Variable"
                    />
                    <PrimaryInput
                      value={pair.value}
                      onChange={(e) =>
                        handleInputChange(index, "value", e.target.value)
                      }
                      placeholder="Enter value"
                    />
                    <div
                      className={``}
                      onClick={() => deleteKeyValuePair(index)}
                    >
                      <Image
                        src="/trashbin.svg"
                        alt=""
                        width={35}
                        height={35}
                      />
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default ApiAuditOnboarding;
