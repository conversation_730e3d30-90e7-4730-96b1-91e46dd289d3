'use client'
import React from 'react'
import Blocks from '../blocks/Blocks'
import { DrCodeTableColumn } from '@/components/common/drcode-table/DrCodeTableTypes'
import { StatCard } from '@/components/test-runner/stat-card/StatCard'
import DrCodeTable from '@/components/common/drcode-table/DrCodeTable'
import Image from 'next/image'

export const TestsTableLoader = () => {
    const columns: DrCodeTableColumn<{ description: string, status_code: string, id: number }>[] = [
        {
            displayName: 'Description',
            accessor: 'a',
            dataRender(row) {
                return row.id%2===0 ? <Blocks className='w-[70%] h-3' /> : <Blocks className='w-[40%] h-3' />
            }
        },
        {
            displayName: 'runall',
            accessor: 'b',
            dataRender(row) {
                return <Blocks className='w-24 h-3' />
            },
            columnHeadRender() {
            return <button className='button--outlined-primary px-4 py-2' onClick={() => console.log("Clicked run all")}> <Image src={'/play.png'} alt='run all' quality={100} width={15} height={15} className='mr-2' /> Run all</button>
            },
        },
        {
            displayName: 'Status Code',
            accessor: 'c',
            dataRender(row) {
                return <Blocks className='w-14 h-3' />
            }
        }
    ]

    const data: { description: string, status_code: string, id: number }[] = [
        {
            description: '',
            status_code: '',
            id: 1
        },
        {
            description: '',
            status_code: '',
            id: 2
        },
        {
            description: '',
            status_code: '',
            id: 3
        },
        {
            description: '',
            status_code: '',
            id: 4
        },
        {
            description: '',
            status_code: '',
            id: 5
        },
        {
            description: '',
            status_code: '',
            id: 6
        },
        {
            description: '',
            status_code: '',
            id: 7
        },
        {
            description: '',
            status_code: '',
            id: 8
        }
    ]
    return (
        <div>
            <div>
                <Blocks className='w-[30%] h-8' />
            </div>
            <div>
                <Blocks className='w-[60%] h-4 my-2 block' />
            </div>
            <ul className='stat-list flex gap-4 justify-between my-4'>
                <li className='flex-[1_0_20%]'><StatCard title='All Tests' image='/codetest.png' count='' /></li>
                <li className='flex-[1_0_20%]'><StatCard title='Executed' image='/exec.png' count='' /></li>
                <li className='flex-[1_0_20%]'><StatCard title='Passed' image='/revote.png' count='' /></li>
                <li className='flex-[1_0_20%]'><StatCard title='Failed' image='/danger.png' count='' /></li>
            </ul>
            <DrCodeTable columns={columns} data={data} className="w-full"></DrCodeTable>
        </div>
    )
}
